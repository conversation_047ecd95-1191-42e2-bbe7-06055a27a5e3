version: '3.8'

services:
  cdp-platform-postgres:
    image: postgres:15
    volumes:
      - ./deployment/dev/postgres:/docker-entrypoint-initdb.d
      - ./deployment/migrations:/sql
    environment:
      - POSTGRES_PASSWORD=cdp
      - POSTGRES_DB=cdp_platform
      - POSTGRES_USER=cdp
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cdp -d cdp_platform" ]
      interval: 5s
      timeout: 60s
      retries: 30
    container_name: cdp-platform-postgres
    networks:
      - cdp-network

  cdp-platform-flyway:
    image: flyway/flyway:8.4.0-alpine
    environment:
      - FLYWAY_SCHEMAS=public,flyway
      - FLYWAY_PASSWORD=cdp
      - FLYWAY_USER=cdp
      - FLYWAY_URL=*********************************************************
      - FLYWAY_DEFAULT_SCHEMA=public
    command:
      - -connectRetries=60
      - migrate
    volumes:
      - ./deployment/migrations:/flyway/sql
    restart: "no"
    depends_on:
      cdp-platform-postgres:
        condition: service_healthy
    container_name: cdp-platform-flyway
    networks:
      - cdp-network

networks:
  cdp-network:
    external: true
