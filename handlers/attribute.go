package handlers

import (
	"errors"
	"strconv"

	dtos "github.com/Zomato/cdp-platform/dtos"
	utils "github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"

	"github.com/Zomato/cdp-platform/internal/authorization"
	"github.com/Zomato/cdp-platform/internal/manager"

	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetAllBaseAttributes(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to list all attributes",
		"method", r.Method,
		"path", r.URL.Path)

	attributes, err := controllerManager.AttributeController.GetAllBaseAttributes(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, attributes)
}

func GetAllDerivedAttributes(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to list all attributes",
		"method", r.Method,
		"path", r.URL.Path)

	attributes, err := controllerManager.AttributeController.GetAllDerivedAttributes(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, attributes)
}

func GetAttributeByID(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get attribute by ID : ",
		"id", r.URL.Query().Get("id"),
		"method", r.Method,
		"path", r.URL.Path)

	attributeIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if attributeIDStr == "" {
		log.Info("No attribute ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no attribute ID associated with request"))
		return
	}

	attributeID, err := strconv.ParseInt(attributeIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid attribute ID:", attributeIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid attribute ID"))
		return
	}

	log.Info("Received Get Attribute Request for ID:", attributeID, r.URL)
	resp, err := controllerManager.AttributeController.GetAttributeByID(ctx, attributeID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func UpdateAttribute(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update attribute")

	var attributeDTO *dtos.Attributes
	err := ctx.ShouldBindJSON(&attributeDTO)
	if err != nil {
		log.Info("Error while decoding attribute request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeDTO)

	ownerID, err := controllerManager.AttributeController.GetAttributeOwnerID(ctx, attributeDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, ownerID); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	err = controllerManager.AttributeController.UpdateAttribute(ctx, attributeDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Updated successfully!")
}

func ValidateAttributeNames(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("Validate Attribute Name handler called")
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to Validate Attribute Name",
		"method", r.Method,
		"path", r.URL.Path)

	var validateAttributeNames *dtos.ValidateAttributeNames
	err := ctx.ShouldBindJSON(&validateAttributeNames)
	if err != nil {
		log.Info("Error while decoding valdiate attribute query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(validateAttributeNames)

	resp, err := controllerManager.AttributeController.ValidateAttributeNames(ctx, validateAttributeNames)
	if err != nil {
		log.Info("Error while validating names")
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, resp)
}

func RegisterAttribute(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Receieved request to register attribute",
		"method", r.Method,
		"path", r.URL.Path)

	var registerAttributeRequest *dtos.RegisterAttributeRequest
	err := ctx.ShouldBindJSON(&registerAttributeRequest)
	if err != nil {
		log.Info("Error while decoding register attribute request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(registerAttributeRequest)

	// Set default attribute category as METRIC if it's empty
	for i := range registerAttributeRequest.Attributes {
		if registerAttributeRequest.Attributes[i].AttributeCategory == "" {
			registerAttributeRequest.Attributes[i].AttributeCategory = dtos.AttributeCategoryMetric
		}
	}

	err = controllerManager.AttributeController.RegisterAttribute(ctx, registerAttributeRequest)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Register Attribute Successful")
}

func GetAttributeSchemaDetails(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get attribute schema details",
		"method", r.Method,
		"path", r.URL.Path)

	attributes, err := controllerManager.AttributeController.GetAttributeSchemaDetails(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, attributes)
}

func GetAttributeSchemaDetailsTgCg(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get attribute schema details",
		"method", r.Method,
		"path", r.URL.Path)

	attributes, err := controllerManager.AttributeController.GetAttributeSchemaDetailsTgCg(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, attributes)
}

func GetAttributeDistinctValues(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get attribute distinct values", r)

	attributeIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if attributeIDStr == "" {
		log.Info("No attribute ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no attribute ID associated with request"))
		return
	}

	attributeID, err := strconv.ParseInt(attributeIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid attribute ID:", attributeIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid attribute ID"))
		return
	}

	log.Info("Received Get Attribute Distinct Values Request for ID:", attributeID, r.URL)
	result, err := controllerManager.AttributeController.GetAttributeDistinctValues(ctx, attributeID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, result)
}
