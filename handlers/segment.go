package handlers

import (
	"errors"
	"strconv"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/authorization"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetSegmentSize(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get segment size")

	var condition *dtos.Condition
	err := ctx.ShouldBindJSON(&condition)
	if err != nil {
		log.Info("Error while decoding segment request", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(condition)

	size, err := controllerManager.SegmentController.GetSegmentSize(ctx, condition)
	if err != nil {
		log.Error("Error while getting segment size", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, size)
}

func GetSegmentSizeTgCg(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get segment size for tg cg")

	var segmentSizeTgCgRequest *dtos.SegmentSizeTgCgRequest
	err := ctx.ShouldBindJSON(&segmentSizeTgCgRequest)
	if err != nil {
		log.Info("Error while decoding segment size tg cg request", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(segmentSizeTgCgRequest)

	size, err := controllerManager.SegmentController.GetSegmentSizeTgCg(ctx, segmentSizeTgCgRequest)
	if err != nil {
		log.Error("Error while getting segment size for tg cg", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, size)
}

func GetAllSegments(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get all segments")

	segmentList, err := controllerManager.SegmentController.GetAllSegments(ctx)
	if err != nil {
		log.Error("Error while getting all segments", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, segmentList)
}

func GetSegmentOverview(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get segment overview")

	segmentIdStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if segmentIdStr == "" {
		log.Info("No segment ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no segment ID associated with request"))
		return
	}

	segmentId, err := strconv.ParseInt(segmentIdStr, 10, 64)
	if err != nil {
		log.Info("Invalid segment ID:", segmentIdStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid segment ID"))
		return
	}

	log.Info("Received Get Segment Overview Request for ID:", segmentId, r.URL)
	resp, err := controllerManager.SegmentController.GetSegmentOverview(ctx, segmentId)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func GetDownstreamDestinationsOfSegment(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get downstream destinations of segment")

	segmentIdStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if segmentIdStr == "" {
		log.Info("No segment ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no segment ID associated with request"))
		return
	}

	segmentId, err := strconv.ParseInt(segmentIdStr, 10, 64)
	if err != nil {
		log.Info("Invalid segment ID:", segmentIdStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid segment ID"))
		return
	}

	log.Info("Received Get Downstream Destinations of Segment Request for ID:", segmentId, r.URL)
	resp, err := controllerManager.SegmentController.GetDownstreamDestinationsOfSegment(ctx, segmentId)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func UpdateSegment(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update segment")

	var segmentDTO *dtos.Segment
	err := ctx.ShouldBindJSON(&segmentDTO)
	if err != nil {
		log.Info("Error while decoding segment request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(segmentDTO)

	ownerID, err := controllerManager.SegmentController.GetSegmentOwnerID(ctx, segmentDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, ownerID); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	err = controllerManager.SegmentController.UpdateSegment(ctx, segmentDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Updated successfully!")
}

func AddSegment(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to add segment")

	var segment *dtos.Segment
	err := ctx.ShouldBindJSON(&segment)
	if err != nil {
		log.Info("Error while decoding segment request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(segment)

	err = controllerManager.SegmentController.AddSegment(ctx, segment)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Added successfully!")
}

func GetSegmentSizeById(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get segment size by ID")

	segmentIdStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if segmentIdStr == "" {
		log.Info("No segment ID associated with request", r)
		HttpStatus.StatusBadRequest(w, r, errors.New("no segment ID associated with request"))
		return
	}

	segmentId, err := strconv.ParseInt(segmentIdStr, 10, 64)
	if err != nil {
		log.Info("Invalid segment ID:", segmentIdStr, r)
		HttpStatus.StatusBadRequest(w, r, errors.New("invalid segment ID"))
		return
	}

	size, err := controllerManager.SegmentController.GetSegmentSizeById(ctx, segmentId)
	if err != nil {
		log.Error("Error while getting segment size", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, size)
}

func GetAllSegmentNames(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get all segment names")

	segments, err := controllerManager.SegmentController.GetAllSegmentNames(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, segments)
}

func GetUpstreamAttributeInfo(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get upstream attribute info for a segment")

	segmentIdStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if segmentIdStr == "" {
		log.Info("No segment ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no segment ID associated with request"))
		return
	}

	segmentId, err := strconv.ParseInt(segmentIdStr, 10, 64)
	if err != nil {
		log.Info("Invalid segment ID:", segmentIdStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid segment ID"))
		return
	}

	log.Info("Received Get Upstream Attribute Info Request for Segment ID:", segmentId, r.URL)
	resp, err := controllerManager.SegmentController.GetUpstreamAttributeInfo(ctx, segmentId)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}
