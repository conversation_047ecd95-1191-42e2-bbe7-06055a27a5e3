package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
	"time"

	auth "github.com/Zomato/cdp-platform/internal/auth"
	"github.com/Zomato/cdp-platform/internal/manager"
	utils "github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"

	// env "github.com/Zomato/cdp-platform/internal/env"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

type AuthConfig struct {
	GoogleClientID     string
	GoogleClientSecret string
	GoogleRedirectURL  string
}

// Scopes: OAuth 2.0 scopes provide a way to limit the amount of access that is granted to an access token.
var googleOauthConfig = &oauth2.Config{
	ClientID:     "GOOGLE_CLIENT_ID",
	ClientSecret: "GOOGLE_CLIENT_SECRET",
	Endpoint:     google.Endpoint,
}

var whitelistedHD = []string{"zomato.com", "blinkit.com", "grofers.com"}
var whitelistedISSs = []string{"https://accounts.google.com"}

func AuthInitialise(authConfig *AuthConfig) {
	googleOauthConfig.ClientID = authConfig.GoogleClientID
	googleOauthConfig.ClientSecret = authConfig.GoogleClientSecret
	googleOauthConfig.RedirectURL = authConfig.GoogleRedirectURL
}

func validateTokenSanity(payloadMap map[string]interface{}) bool {
	email := payloadMap["email"]
	hd, ok := payloadMap["hd"].(string)
	if !ok {
		log.Errorf("Invalid callback, user %v email does not belong to a corporate ID", email)
		return false
	}
	if !utils.StringInSlice(hd, whitelistedHD) {
		log.Errorf("Domain %v is not under whitelisted domains, auditing email %v", hd, email)
		return false
	}
	aud := payloadMap["aud"].(string)
	if googleOauthConfig.ClientID != aud {
		log.Errorf("Mismatch between the callback client ID & the issuer email %v and client ID %v", email, aud)
		return false
	}
	iss := payloadMap["iss"].(string)
	if !utils.StringInSlice(iss, whitelistedISSs) {
		log.Errorf("Token issuer %v has not been whitelisted audit email %v", iss, email)
		return false
	}
	return true
}

/* Handling callback Google, fetching internal User and returning Token Pair */
func OauthGoogleCallback(ctx *gin.Context, controllerManager *manager.Manager) {
	w := ctx.Writer
	r := ctx.Request
	log.Info("Received request for OAuth Google Callback", r)

	// Get the authorization code from the query parameters
	code := r.URL.Query().Get("code")
	if code == "" {
		log.Error("No code found in Google callback")
		HttpStatus.StatusBadRequest(w, r, errors.New("No code found in callback"))
		return
	}

	log.Info("Exchanging authorization code for token")

	// Exchange the authorization code for tokens
	token, err := googleOauthConfig.Exchange(context.Background(), code)
	if err != nil {
		log.Error("Error exchanging code for token", err)
		HttpStatus.StatusUnauthorized(w, r, err)
		return
	}

	// Use the access token to get user info
	client := googleOauthConfig.Client(context.Background(), token)
	resp, err := client.Get("https://www.googleapis.com/oauth2/v3/userinfo")
	if err != nil {
		log.Error("Error getting user info", err)
		HttpStatus.StatusUnauthorized(w, r, err)
		return
	}
	defer resp.Body.Close()

	userData := make(map[string]interface{})
	if err := json.NewDecoder(resp.Body).Decode(&userData); err != nil {
		log.Error("Error parsing user data", err)
		HttpStatus.StatusUnauthorized(w, r, err)
		return
	}

	// Extract user information
	email, ok := userData["email"].(string)
	if !ok {
		log.Error("Email not found in user data")
		HttpStatus.StatusUnauthorized(w, r, errors.New("Email not found in user data"))
		return
	}
	email = strings.ToLower(email)

	name, _ := userData["name"].(string)
	if name == "" {
		name = email // Use email as name if name is not available
	}

	googleID, _ := userData["sub"].(string)
	if googleID == "" {
		log.Error("Google ID not found in user data")
		HttpStatus.StatusUnauthorized(w, r, errors.New("Google ID not found in user data"))
		return
	}

	// Check domain (hd) if available
	hd, hdOk := userData["hd"].(string)
	if hdOk && !utils.StringInSlice(hd, whitelistedHD) {
		log.Errorf("Domain %v is not under whitelisted domains, auditing email %v", hd, email)
		HttpStatus.StatusUnauthorized(w, r, errors.New("Domain not allowed"))
		return
	}

	log.Info("User info extracted", "email", email, "name", name, "googleID", googleID)

	// Get or create user
	user, err := controllerManager.UserController.GetOrCreateUser(ctx, email, name, googleID)
	if err != nil {
		log.Error("Error getting or creating user", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	log.Info("User retrieved/created", "user", user, "isAllowed", user.IsAllowed)

	// Check if user is allowed
	if !user.IsAllowed {
		log.Info("User is not allowed to access the system", email)
		HttpStatus.StatusUnauthorized(w, r, errors.New("User is not allowed to access the system"))
		return
	}

	// Generate JWT tokens
	accessTokenString, refreshTokenString, err := auth.GetJWTPair(user)
	if err != nil {
		log.Error("Error generating JWT tokens", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	// Create session
	sessionToken := auth.GenerateSessionToken(accessTokenString)
	auth.SetSessionCookie(ctx, sessionToken, user.Email, accessTokenString, refreshTokenString, true)

	// Create response with user information
	userResponse := map[string]interface{}{
		"id":            user.Id,
		"name":          user.Name,
		"email":         user.Email,
		"is_allowed":    user.IsAllowed,
		"tenant_id":     user.TenantID,
		"authenticated": true,
	}

	// Return user information to the frontend
	HttpStatus.StatusOK(w, r, userResponse)
}

/* Called by frontend for generating AT upon receiving expired status */
func RefreshJWTRequest(ctx *gin.Context, controllerManager *manager.Manager) {
	w := ctx.Writer
	r := ctx.Request
	log.Info("Received request for Refresh JWT Request", r)

	jwtRequest, err := r.Cookie("refresh_token")
	if err != nil {
		log.Info("Unable to find refresh token in cookie", err)
		HttpStatus.StatusUnauthorized(w, r, err)
		return
	}
	jwtRequestStr := jwtRequest.Value

	refreshClaims, err := auth.ValidateToken(jwtRequestStr)
	if err != nil {
		log.Info("Unable to validate refresh token", err)
		HttpStatus.StatusUnauthorized(w, r, err)
		return
	}

	email := refreshClaims["email"].(string)

	// Check if user is still allowed
	isAllowed, err := controllerManager.UserController.IsUserAllowed(ctx, email)
	if err != nil {
		log.Error("Error checking if user is allowed", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	if !isAllowed {
		log.Info("User is not allowed to access the system", email)
		HttpStatus.StatusUnauthorized(w, r, errors.New("User is not allowed to access the system"))
		return
	}

	updatedAccessTokenString, err := auth.RefreshJWT(refreshClaims)
	if err != nil {
		log.Info("Unable to refresh JWT", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	updatedSessionToken := auth.GenerateSessionToken(updatedAccessTokenString)
	auth.SetSessionCookie(ctx, updatedSessionToken, email, updatedAccessTokenString, jwtRequestStr, false)

	HttpStatus.StatusOK(w, r, "OK")
}

func LogoutUser(ctx *gin.Context) {
	w := ctx.Writer
	r := ctx.Request

	log.Info("Received request for Logout", r)
	http.SetCookie(w, &http.Cookie{
		Name:     "session_token",
		Value:    "",
		Expires:  time.Now(),
		HttpOnly: true,
		Path:     "/",
		Secure:   true,
	})
	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    "",
		Expires:  time.Now(),
		HttpOnly: true,
		Path:     "/",
		Secure:   true,
	})
	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    "",
		Expires:  time.Now(),
		HttpOnly: true,
		Path:     "/",
		Secure:   true,
	})

	HttpStatus.StatusOK(w, r, "Logged out successfully")
}

// InitiateGoogleLogin generates and returns the Google OAuth URL
func InitiateGoogleLogin(ctx *gin.Context, controllerManager *manager.Manager) {
	w := ctx.Writer
	r := ctx.Request
	log.Info("Received request to initiate Google login", r)

	// Generate a random state token to prevent CSRF
	stateToken := auth.GenerateStateToken()

	// Get the origin or use the configured redirect URL
	origin := r.Header.Get("Origin")
	println("origin", origin)
	if origin == "" {
		// Use the configured redirect URL from config if Origin header is not available
		googleOauthConfig.RedirectURL = googleOauthConfig.RedirectURL
	} else {
		// Construct a complete URL with the origin
		googleOauthConfig.RedirectURL = origin + "/auth/google/callback"
	}

	googleOauthConfig.Scopes = []string{"email", "profile"}

	authURL := googleOauthConfig.AuthCodeURL(stateToken)

	log.Info("Generated OAuth URL: " + authURL)

	HttpStatus.StatusOK(w, r, map[string]string{
		"auth_url": authURL,
		"state":    stateToken,
	})
}
