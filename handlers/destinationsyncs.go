package handlers

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/authorization"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

func GetAllDestinationSyncs(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to list all destination syncs", r)

	queries, err := controllerManager.DestinationSyncController.GetAllDestinationSyncs(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, queries)
}

func RegisterDestinationSync(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Receieved request to register destination sync", r)

	var registerDestinationSyncRequest *dtos.RegisterDestinationSyncRequest
	err := ctx.ShouldBindJSON(&registerDestinationSyncRequest)
	if err != nil {
		log.Info("Error while decoding register attribute request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(registerDestinationSyncRequest)

	err = controllerManager.DestinationSyncController.RegisterDestinationSync(ctx, registerDestinationSyncRequest)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Register Destination Sync Successful")
}

func GetDestinationSyncOverview(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get destination sync overview", r)

	destinationSyncIdString := utils.SanitizeString(r.URL.Query().Get("id"))
	destinationSyncId, err := strconv.ParseInt(destinationSyncIdString, 10, 64)
	if err != nil {
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}

	destinationSyncOverview, err := controllerManager.DestinationSyncController.GetDestinationSyncOverview(ctx, destinationSyncId)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, destinationSyncOverview)
}

func ValidateDestinationSyncSegment(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to validate destination sync")

	var request dtos.ValidateDestinationSyncRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		log.Info("Error while decoding validation request:", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&request)

	response, err := controllerManager.DestinationSyncController.ValidateDestinationSyncSegment(ctx, &request)
	if err != nil {
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}

func ValidateDestinationSyncAppsflyerAudienceName(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to validate audience name")

	var request dtos.ValidateAppsflyerAudienceNameRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		log.Info("Error while decoding validation request:", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&request)

	if strings.TrimSpace(request.Name) == "" {
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("name cannot be empty"))
		return
	}

	response, err := controllerManager.DestinationSyncController.ValidateDestinationSyncAppsflyerAudienceName(ctx, &request)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}

func ValidateDestinationSyncFeatureStoreSegment(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to validate feature store segment")

	var request dtos.ValidateFeatureStoreSegmentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		log.Info("Error while decoding validation request:", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&request)

	if strings.TrimSpace(request.FeatureName) == "" {
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("name cannot be empty"))
		return
	}

	response, err := controllerManager.DestinationSyncController.ValidateDestinationSyncFeatureStoreSegment(ctx, &request)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}

func ValidateDestinationSyncSize(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to validate destination sync size", r)

	var request dtos.ValidateDestinationSyncSizeRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		log.Info("Error while decoding validation request:", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&request)

	response, err := controllerManager.DestinationSyncController.ValidateDestinationSyncSize(ctx, &request)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}

func UpdateDestinationSyncCron(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update cron for destination sync")

	var destinationSyncUpdateCronRequestDTO *dtos.DestinationSyncUpdateCronRequest
	err := ctx.ShouldBindJSON(&destinationSyncUpdateCronRequestDTO)
	if err != nil {
		log.Info("Error while decoding destination sync request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(destinationSyncUpdateCronRequestDTO)

	// Additional validation for required fields
	if destinationSyncUpdateCronRequestDTO.Id == 0 {
		HttpStatus.StatusBadRequest(w, r, errors.New("id is required"))
		return
	}

	if destinationSyncUpdateCronRequestDTO.CronExpression == "" {
		HttpStatus.StatusBadRequest(w, r, errors.New("cron_expression is required"))
		return
	}

	if destinationSyncUpdateCronRequestDTO.DestinationType == "" {
		HttpStatus.StatusBadRequest(w, r, errors.New("destination_type is required"))
		return
	}

	// Validate destination type is supported
	if destinationSyncUpdateCronRequestDTO.DestinationType != "APPSFLYER" && destinationSyncUpdateCronRequestDTO.DestinationType != "FEATURE_STORE" {
		HttpStatus.StatusBadRequest(w, r, errors.New("destination_type must be either 'APPSFLYER' or 'FEATURE_STORE'"))
		return
	}

	err = controllerManager.DestinationSyncController.UpdateDestinationSyncCron(ctx, destinationSyncUpdateCronRequestDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Cron Updated successfully!")
}

func UpdateDestinationSyncTgCg(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update tgcg for destination sync")

	var destinationSyncUpdateTgCgRequestDTO *dtos.DestinationSyncUpdateTgCgRequest
	err := ctx.ShouldBindJSON(&destinationSyncUpdateTgCgRequestDTO)
	if err != nil {
		log.Info("Error while decoding destination sync request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(destinationSyncUpdateTgCgRequestDTO)

	if destinationSyncUpdateTgCgRequestDTO.Id == 0 {
		HttpStatus.StatusBadRequest(w, r, errors.New("id is required"))
		return
	}

	destSyncOwnerId, err := controllerManager.DestinationSyncController.GetDestinationSyncOwnerID(ctx, destinationSyncUpdateTgCgRequestDTO.Id)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	if err := authorization.ValidateOwnership(ctx, destSyncOwnerId); err != nil {
		HttpStatus.StatusForbidden(w, r, err)
		return
	}

	if err := controllerManager.SegmentController.ValidateFilterConditions(&destinationSyncUpdateTgCgRequestDTO.Conditions); err != nil {
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("invalid tg_cg conditions: %w", err))
		return
	}

	err = controllerManager.DestinationSyncController.UpdateDestinationSyncTgCg(ctx, destinationSyncUpdateTgCgRequestDTO)

	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "TGCG conditions updated successfully!")
}

func UpdateDestinationSyncEndDate(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to update end_date for destination sync")

	var req *dtos.DestinationSyncUpdateEndDateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		log.Info("Error decoding update end date request:", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(req)

	if req.Id == 0 {
		HttpStatus.StatusBadRequest(w, r, errors.New("id is required"))
		return
	}
	if req.EndDate == "" {
		HttpStatus.StatusBadRequest(w, r, errors.New("end_date is required"))
		return
	}
	if req.DestinationType == "" {
		HttpStatus.StatusBadRequest(w, r, errors.New("destination_type is required"))
		return
	}
	if req.DestinationType != "APPSFLYER" && req.DestinationType != "FEATURE_STORE" {
		HttpStatus.StatusBadRequest(w, r, errors.New("destination_type must be either 'APPSFLYER' or 'FEATURE_STORE'"))
		return
	}

	err := controllerManager.DestinationSyncController.UpdateDestinationSyncEndDate(ctx, req)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "End date updated successfully!")
}
