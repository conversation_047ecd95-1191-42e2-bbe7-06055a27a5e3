package handlers

import (
	"errors"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func AddDataSourceConfig(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to add data source config", r)

	var config dtos.DataSourceConfig
	err := ctx.ShouldBindJSON(&config)
	if err != nil {
		log.Info("Error while decoding data source config request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&config)

	// Validate that at least one field is marked as required
	hasRequiredField := false
	for _, required := range config.ConfigFields {
		if required {
			hasRequiredField = true
			break
		}
	}
	if !hasRequiredField {
		HttpStatus.StatusBadRequest(w, r, errors.New("at least one field must be marked as required"))
		return
	}
	resp, err := controllerManager.DatasourceConfigController.AddDataSourceConfig(ctx, &config)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func GetAllDataSourceTypes(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get all data source types", r)

	types, err := controllerManager.DatasourceConfigController.GetAllDataSourceTypes(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, types)
}

func GetConfigFields(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get config fields for a data source type", r)

	dataSourceType := utils.SanitizeString(ctx.Query("data_source_type"))
	if dataSourceType == "" {
		HttpStatus.StatusBadRequest(w, r, errors.New("data_source_type is required"))
		return
	}

	fields, err := controllerManager.DatasourceConfigController.GetConfigFields(ctx, dataSourceType)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, fields)
}
