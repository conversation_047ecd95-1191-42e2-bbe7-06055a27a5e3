package handlers

import (
	"fmt"
	"strconv"

	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetFeature(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get a feature from the feature store", r)

	destinationIDStr := utils.SanitizeString(r.URL.Query().Get("destination_id"))
	contextName := utils.SanitizeString(r.URL.Query().Get("context_name"))
	featureName := utils.SanitizeString(r.URL.Query().Get("feature_name"))

	if destinationIDStr == "" {
		log.Info("Missing destination_id in get feature request", r)
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("destination_id is required"))
		return
	}

	if contextName == "" {
		log.Info("Missing context_name in get feature request", r)
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("context_name is required"))
		return
	}

	if featureName == "" {
		log.Info("Missing feature_name in get feature request", r)
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("feature_name is required"))
		return
	}

	destinationID, err := strconv.ParseInt(destinationIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid destination_id in get feature request", r, err)
		HttpStatus.StatusBadRequest(w, r, fmt.Errorf("invalid destination_id"))
		return
	}

	feature, err := controllerManager.FeatureStoreController.GetFeature(ctx, contextName, featureName, destinationID)
	if err != nil {
		log.Info("Error getting feature from feature store", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, feature)
}

func ManualTriggerSparkJob(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to trigger spark job", r)

	err := controllerManager.FeatureStoreSparkController.ManualTriggerSparkJob(ctx)
	if err != nil {
		log.Info("Error triggering spark job", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	response := map[string]string{
		"status":  "success",
		"message": "Spark job triggered successfully",
	}
	HttpStatus.StatusOK(w, r, response)
}

func ScheduleSparkJob(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to trigger spark job", r)

	err := controllerManager.FeatureStoreSparkController.ScheduleSparkJob(ctx)
	if err != nil {
		log.Info("Error triggering spark job", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	response := map[string]string{
		"status":  "success",
		"message": "Spark job scheduled successfully",
	}
	HttpStatus.StatusOK(w, r, response)
}

// ScheduleFeatureStoreStatusChecker schedules the feature store status checker to run every 20 minutes
func ScheduleFeatureStoreStatusChecker(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to schedule feature store status checker", r)

	err := controllerManager.FeatureStoreSparkController.ScheduleFeatureStoreStatusChecker(ctx)
	if err != nil {
		log.Error("Error scheduling feature store status checker", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	response := map[string]string{
		"status":  "success",
		"message": "Feature store status checker scheduled successfully to run every 20 minutes",
	}
	HttpStatus.StatusOK(w, r, response)
}

// ManualTriggerFeatureStoreStatusChecker manually triggers the feature store status checker
func ManualTriggerFeatureStoreStatusChecker(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to manually trigger feature store status checker", r)

	err := controllerManager.FeatureStoreSparkController.ManualTriggerFeatureStoreStatusChecker(ctx)
	if err != nil {
		log.Error("Error triggering feature store status checker", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	response := map[string]string{
		"status":  "success",
		"message": "Feature store status checker triggered successfully",
	}
	HttpStatus.StatusOK(w, r, response)
}
