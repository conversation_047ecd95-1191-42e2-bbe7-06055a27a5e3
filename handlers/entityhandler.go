package handlers

import (
	"errors"
	"strconv"

	dtos "github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	utils "github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetAllEntities(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to list all entities", r)

	entities, err := controllerManager.EntityController.GetAllEntities(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, entities)
}

func GetEntityByID(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get entity by ID", r)

	entityIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if entityIDStr == "" {
		log.Info("No entity ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no entity ID associated with request"))
		return
	}

	entityID, err := strconv.ParseInt(entityIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid entity ID:", entityIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid entity ID"))
		return
	}

	log.Info("Received Get Entity Request for ID:", entityID, r.URL)
	resp, err := controllerManager.EntityController.GetEntityByID(ctx, entityID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func GetTotalUsers(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Received request to get total users count for an entity")

	entityIdStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if entityIdStr == "" {
		log.Info("No entity ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no entity ID associated with request"))
		return
	}

	entityId, err := strconv.ParseInt(entityIdStr, 10, 64)
	if err != nil {
		log.Info("Invalid entity ID:", entityIdStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid entity ID"))
		return
	}

	count, err := controllerManager.EntityController.GetTotalUsers(ctx, entityId)
	if err != nil {
		log.Error("Error while getting total users count", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, &dtos.SegmentSize{Count: count})
}
