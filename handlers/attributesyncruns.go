package handlers

import (
	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"

	"github.com/Zomato/cdp-platform/internal/manager"

	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func AddAttributeSyncRun(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Receieved request to create attribute sync run")

	var attributeSyncRun *dtos.AttributeSyncRuns
	err := ctx.ShouldBindJSON(&attributeSyncRun)
	if err != nil {
		log.Info("Error while decoding attribute query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeSyncRun)

	resp, err := controllerManager.AttributeSyncRunsController.AddAttributeSyncRun(ctx, attributeSyncRun)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, resp)
}

func UpdateAttributeSyncRun(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Infoln("Receieved request to update sync run ")

	var attributeSyncRun *dtos.AttributeSyncRuns
	err := ctx.ShouldBindJSON(&attributeSyncRun)
	if err != nil {
		log.Info("Error  while decoding attribute query request, ", err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(attributeSyncRun)

	err = controllerManager.AttributeSyncRunsController.UpdateAttributeSyncRun(ctx, attributeSyncRun)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, "Updated successfully !")
}
