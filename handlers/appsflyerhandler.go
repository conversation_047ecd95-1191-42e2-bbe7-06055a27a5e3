package handlers

import (
	dtos "github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	utils "github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func CreateAudience(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer

	var audience *dtos.AppsflyerAudience
	err := ctx.ShouldBindJSON(&audience)
	if err != nil {
		log.Info("Error while decoding audience request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(audience)

	id, err := controllerManager.AppsflyerController.CreateAudience(ctx, audience)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, id)
}

func GetAllAudiences(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get all AppsFlyer audiences")

	audiences, err := controllerManager.AppsflyerController.GetAllAudiences(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, audiences)
}
