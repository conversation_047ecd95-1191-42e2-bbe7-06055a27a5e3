package handlers

import (
	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func CreateTag(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to create a tag", r)

	var tagDTO dtos.Tag
	err := ctx.ShouldBindJSON(&tagDTO)
	if err != nil {
		log.Info("Error while decoding tag request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(&tagDTO)

	createdTag, err := controllerManager.TagController.CreateTag(ctx, &tagDTO)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, createdTag)
}

func ListTags(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to list all tags", r)

	tags, err := controllerManager.TagController.ListTags(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, tags)
}
