package handlers

import (
	"errors"
	"strconv"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func AddDataSource(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Receieved request to add data source", r)

	var datasource *dtos.DataSource
	err := ctx.ShouldBindJSON(&datasource)
	if err != nil {
		log.Info("Error while decoding data source request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(datasource)

	// setDataSourceDefaultValues(&datasource)
	resp, err := controllerManager.DatasourceController.AddDataSource(ctx, datasource)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}

func DeleteDataSource(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Receieved request to delete data source", r)

	HttpStatus.StatusInternalServerError(w, r, errors.New("not implemented yet"))
}

func GetAllDataSources(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to list all data sources", r)

	dataSources, err := controllerManager.DatasourceController.GetAllDataSources(ctx)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, dataSources)
}

func GetDataSourceByID(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to get data source by ID", r)

	dataSourceIDStr := utils.SanitizeString(r.URL.Query().Get("id"))
	if dataSourceIDStr == "" {
		log.Info("No data source ID associated with request", r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("no data source ID associated with request"))
		return
	}

	dataSourceID, err := strconv.ParseInt(dataSourceIDStr, 10, 64)
	if err != nil {
		log.Info("Invalid data source ID:", dataSourceIDStr, r)
		HttpStatus.StatusInternalServerError(w, r, errors.New("invalid data source ID"))
		return
	}

	log.Info("Received Get Data Source Request for ID:", dataSourceID, r.URL)
	resp, err := controllerManager.DatasourceController.GetDataSourceByID(ctx, dataSourceID)
	if err != nil {
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	HttpStatus.StatusOK(w, r, resp)
}
