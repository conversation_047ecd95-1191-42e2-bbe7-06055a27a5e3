package handlers

import (
	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func GetQueryPreview(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("Get Query preview handler called")
	r := ctx.Request
	w := ctx.Writer
	var userEmail string
	var ok bool
	if userEmail, ok = r.Context().Value("email").(string); ok {
		log.Info("Query preview requested by user ", "email ", userEmail)
	}
	userEmail = utils.SanitizeString(userEmail)

	var previewQuery *dtos.QueryEngineInput
	err := ctx.ShouldBindJSON(&previewQuery)
	if err != nil {
		log.Info("Error while decoding preview query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(previewQuery)

	resp, err := controllerManager.QueryEngineController.PreviewSQL(ctx, previewQuery, &userEmail)
	if err != nil {
		log.Info("Error while preview query")
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, resp)
}

// func GetQueryColumns(ctx *gin.Context, controllerManager *manager.Manager) {
// 	log.Info("Get Query columns handler called")
// 	r := ctx.Request
// 	w := ctx.Writer
// 	log.Info("Receieved request to query columns", r)

// 	var previewQuery *dtos.PreviewQuery
// 	err := ctx.ShouldBindJSON(&previewQuery)
// 	if err != nil {
// 		log.Info("Error while decoding query columns request", r, err)
// 		HttpStatus.StatusInternalServerError(w, r, err)
// 		return
// 	}

// 	resp, err := controllerManager.QueryEngineController.GetQueryColumns(ctx, previewQuery)
// 	if err != nil {
// 		log.Info("Error while fetching query columns")
// 		HttpStatus.StatusInternalServerError(w, r, err)
// 		return
// 	}

// 	HttpStatus.StatusOK(w, r, resp)
// }

func QueryEngineTester(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("Query engine test handler called")
	r := ctx.Request
	w := ctx.Writer

	var syncAttibute *dtos.SyncAttibute
	err := ctx.ShouldBindJSON(&syncAttibute)
	if err != nil {
		log.Info("Error while decoding preview query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(syncAttibute)

	err = controllerManager.UserAttributesController.SyncAttibute(ctx, syncAttibute)
	if err != nil {
		log.Error("Schema sync failed for query_id: 2", err)
		HttpStatus.StatusInternalServerError(ctx.Writer, ctx.Request, err)
		return
	}

	HttpStatus.StatusOK(ctx.Writer, ctx.Request, "Schema sync completed successfully")
}

func SyncStarrocksSchemaTest(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("SyncStarrocksSchemaTest called")
	r := ctx.Request
	w := ctx.Writer

	var syncAttibute *dtos.SyncAttibute
	err := ctx.ShouldBindJSON(&syncAttibute)
	if err != nil {
		log.Info("Error while decoding preview query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(syncAttibute)

	_, err = controllerManager.UserAttributesController.SyncAttributeSchema(ctx, syncAttibute)
	if err != nil {
		log.Error("Schema sync failed for query_id: 2", err)
		HttpStatus.StatusInternalServerError(ctx.Writer, ctx.Request, err)
		return
	}

	HttpStatus.StatusOK(ctx.Writer, ctx.Request, "Schema sync completed successfully")
}

func SyncStarrocksDataTest(ctx *gin.Context, controllerManager *manager.Manager) {
	log.Info("SyncStarrocksDataTest handler called")
	r := ctx.Request
	w := ctx.Writer

	var syncTableSchemaAndData *dtos.SyncAttibute
	err := ctx.ShouldBindJSON(&syncTableSchemaAndData)
	if err != nil {
		log.Info("Error while decoding preview query request", r, err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}
	utils.SanitizeStruct(syncTableSchemaAndData)

	err = controllerManager.UserAttributesController.SyncAttributeData(ctx, syncTableSchemaAndData)
	if err != nil {
		log.Error("Schema sync failed for query_id: 2", err)
		HttpStatus.StatusInternalServerError(ctx.Writer, ctx.Request, err)
		return
	}

	HttpStatus.StatusOK(ctx.Writer, ctx.Request, "Schema sync completed successfully")
}

func TriggerDatabaseBackup(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to trigger database backup")

	var backupRequest *dtos.DatabaseBackupRequest
	err := ctx.ShouldBindJSON(&backupRequest)
	if err != nil {
		log.Info("Error while decoding backup request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(backupRequest)

	err = controllerManager.StarrocksController.TriggerDatabaseBackup(ctx, backupRequest)
	if err != nil {
		log.Error("Failed to trigger database backup", "error", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, "Database backup triggered successfully")
}

func ScheduleDatabaseBackup(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to schedule database backup")

	var backupRequest *dtos.DatabaseBackupRequest
	err := ctx.ShouldBindJSON(&backupRequest)
	if err != nil {
		log.Info("Error while decoding backup schedule request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(backupRequest)

	err = controllerManager.StarrocksController.ScheduleDatabaseBackup(ctx, backupRequest)
	if err != nil {
		log.Error("Failed to schedule database backup", "error", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, "Database backup schedule created successfully")
}

func UpdateDatabaseBackupCron(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to update database backup cron")

	var updateRequest *dtos.DatabaseBackupUpdateCronRequest
	err := ctx.ShouldBindJSON(&updateRequest)
	if err != nil {
		log.Info("Error while decoding backup update cron request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(updateRequest)

	err = controllerManager.StarrocksController.UpdateDatabaseBackupCron(ctx, updateRequest)
	if err != nil {
		log.Error("Failed to update database backup cron", "error", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, "Database backup cron updated successfully")
}
