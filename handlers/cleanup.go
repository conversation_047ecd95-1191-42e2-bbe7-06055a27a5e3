package handlers

import (
	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/utils"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func ScheduleAttributeQueriesCleanup(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to schedule cleanup workflow", r)

	var request *dtos.ScheduleAttributeQueriesCleanupRequest
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Info("Error while decoding schedule cleanup request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(request)

	response, err := controllerManager.CleanupController.ScheduleAttributeQueriesCleanup(ctx, request)
	if err != nil {
		log.Error("Failed to schedule cleanup", "error", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}

func TriggerAttributeQueriesCleanup(ctx *gin.Context, controllerManager *manager.Manager) {
	r := ctx.Request
	w := ctx.Writer
	log.Info("Received request to trigger immediate cleanup workflow", r)

	var request *dtos.TriggerAttributeQueriesCleanupRequest
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Info("Error while decoding trigger cleanup request", r, err)
		HttpStatus.StatusBadRequest(w, r, err)
		return
	}
	utils.SanitizeStruct(request)

	response, err := controllerManager.CleanupController.TriggerAttributeQueriesCleanup(ctx, request)
	if err != nil {
		log.Error("Failed to trigger cleanup", "error", err)
		HttpStatus.StatusInternalServerError(w, r, err)
		return
	}

	HttpStatus.StatusOK(w, r, response)
}
