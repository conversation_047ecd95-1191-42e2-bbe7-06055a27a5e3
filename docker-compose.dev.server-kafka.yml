services:
  cdp-platform-server:
    platform: linux/amd64
    build:
      context: .
    ports:
      - 8000:8000
    depends_on:
      cdp-platform-postgres:
        condition: service_healthy
      cdp-platform-flyway:
        condition: service_completed_successfully
      # temporal:
      #   condition: service_healthy
    container_name: cdp-platform-server
    restart: unless-stopped
    environment:
      - CONFIG_SOURCE=local
      - APP_MODE=http
      - AWS_ACCESS_KEY_ID=AKIAxxxxxxxxxxxxxxx
      - AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

  # cdp-platform-worker:
  #   platform: linux/amd64
  #   build:
  #     context: .
  #   ports:
  #     - 8001:8001
  #   depends_on:
  #     temporal:
  #       condition: service_healthy
  #   container_name: cdp-platform-worker
  #   restart: unless-stopped
  #   environment:
  #     - CONFIG_SOURCE=local
  #     - APP_MODE=worker
  #     - AWS_ACCESS_KEY_ID=AKIAxxxxxxxxxxxxxxx
  #     - AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

  cdp-platform-postgres:
    image: postgres:15
    volumes:
      - ./deployment/dev/postgres:/docker-entrypoint-initdb.d
      - ./deployment/migrations:/sql
    environment:
      - POSTGRES_PASSWORD=cdp
      - POSTGRES_DB=cdp_platform
      - POSTGRES_USER=cdp
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cdp -d cdp_platform" ]
      interval: 5s
      timeout: 60s
      retries: 30
    container_name: cdp-platform-postgres

  cdp-platform-flyway:
    image: flyway/flyway:8.4.0-alpine
    environment:
      - FLYWAY_SCHEMAS=public,flyway
      - FLYWAY_PASSWORD=cdp
      - FLYWAY_USER=cdp
      - FLYWAY_URL=*********************************************************
      - FLYWAY_DEFAULT_SCHEMA=public
    command:
      - -connectRetries=60
      - migrate
    volumes:
      - ./deployment/migrations:/flyway/sql
    restart: "no"
    depends_on:
      cdp-platform-postgres:
        condition: service_healthy
    container_name: cdp-platform-flyway

  # temporal:
  #   image: temporalio/auto-setup:latest
  #   environment:
  #     - DB=postgres12
  #     - POSTGRES_SEEDS=cdp-platform-postgres
  #     - DB_PORT=5432
  #     - POSTGRES_USER=cdp
  #     - POSTGRES_PWD=cdp
  #   ports:
  #     - "7233:7233"
  #     - "8233:8233"
  #   depends_on:
  #     cdp-platform-postgres:
  #       condition: service_healthy
  #   container_name: temporal-server
  #   restart: unless-stopped
  #   healthcheck:
  #     test: [ "CMD", "tctl", "--address", "temporal:7233", "workflow", "list", "||", "exit 1" ]
  #     interval: 5s
  #     timeout: 60s
  #     retries: 30

  # temporal-admin-tools:
  #   container_name: temporal-admin-tools
  #   depends_on:
  #     - temporal
  #   environment:
  #     - TEMPORAL_ADDRESS=temporal:7233
  #     - TEMPORAL_CLI_ADDRESS=temporal:7233
  #   image: temporalio/admin-tools:latest
  #   stdin_open: true
  #   tty: true

  # temporal-ui:
  #   container_name: temporal-ui
  #   depends_on:
  #     - temporal
  #   environment:
  #     - TEMPORAL_ADDRESS=temporal:7233
  #   image: temporalio/ui:latest
  #   ports:
  #     - 8080:8080

  zookeeper:
    image: bitnami/zookeeper:3.9.1
    # to survive the container restart
    tmpfs: "/zktmp"
    environment:
      ALLOW_ANONYMOUS_LOGIN: 'yes'
    ports:
      - "2181:2181"

  kafka1:
    image: bitnami/kafka:3.7.0
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_CFG_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_CFG_LISTENERS: INTERNAL://:9092,EXTERNAL://0.0.0.0:29092
      KAFKA_CFG_ADVERTISED_LISTENERS: INTERNAL://kafka1:9092,EXTERNAL://localhost:29092
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_INTER_BROKER_LISTENER_NAME: INTERNAL
      # optional - enable topic auto create
      KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE: 'true'
      ALLOW_PLAINTEXT_LISTENER: 'yes'
    ports:
      - "9092:9092"
      - "29092:29092"
    volumes:
      - kafka_data1:/bitnami/kafka

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    depends_on:
      - kafka1
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka1:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181

volumes:
  kafka_data1:
    driver: local
