package router

import (
	"context"
	"time"

	"github.com/Zomato/cdp-platform/handlers"
	"github.com/Zomato/cdp-platform/internal/env"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/internal/middleware"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

var whitelistedDomains []string

func WhitelistDomains(domainList []string) {
	whitelistedDomains = domainList
}

func SetupRouter(ctx context.Context) *gin.Engine {
	r := gin.New()
	r.ContextWithFallback = true
	r.Use(gin.Recovery())

	// Add logging middleware
	r.Use(middleware.LoggingMiddleware())

	environment, err := env.FromContext(ctx)
	if err != nil {
		log.Info("Unable to extract environment from starting context", ctx, err)
		return nil
	}

	r.Use(config.GinMiddleware())
	r.Use(env.MiddleWare(environment))

	// Get whitelisted domains from config
	whitelistedDomains = config.GetStringSlice(ctx, "whitelist.domains")
	if len(whitelistedDomains) == 0 {
		log.Warn("No whitelisted domains found in config, CORS will allow no origins", ctx)
	}

	r.Use(cors.New(cors.Config{
		AllowOrigins:     whitelistedDomains,
		AllowMethods:     []string{"GET", "PUT", "POST", "OPTIONS", "DELETE", "PATCH"},
		AllowHeaders:     []string{"Origin", "X-Requested-With", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Type", "Cache-Control"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Setup Controller Manager
	postgresClient := environment.PostgresClient()
	starrocksClient := environment.StarRocksClient()
	temporalClient := environment.TemporalClient()
	kafkaProducer := environment.KafkaProducer()
	appsflyerClient := environment.AppsflyerClient()
	emrClient := environment.EMRClient()
	featureStoreClient := environment.FeatureStoreClient()
	s3Client := environment.S3Client()
	controllerManager := manager.NewManager(ctx, postgresClient, starrocksClient, temporalClient, kafkaProducer, appsflyerClient, emrClient, featureStoreClient, s3Client)

	// Public routes
	setHealthCheckRoutes(r.Group("api/health"), controllerManager)
	setAuthRoutes(r.Group("api/auth"), controllerManager)

	// Protected routes - all these routes will require authentication
	protected := r.Group("")
	isLocalDev := config.GetString(ctx, "service.environment") == "local"
	if !isLocalDev {
		log.Info("Running in production mode - authentication checks enabled")
		protected.Use(middleware.CheckLoggedIn())
	} else {
		log.Info("Running in local mode - authentication checks disabled")
	}
	{
		setDataSourceConfigRoutes(protected.Group("api/datasource-configs"), controllerManager)
		setDataSourcesRoutes(protected.Group("api/datasources"), controllerManager)
		setEngineRoutes(protected.Group("api/query-engine"), controllerManager)
		setEntityRoutes(protected.Group("api/entities"), controllerManager)
		setAttributeRoutes(protected.Group("api/attributes"), controllerManager)
		setAttributeQueriesRoutes(protected.Group("api/attribute-queries"), controllerManager)
		setSegmentsRoutes(protected.Group("api/segment"), controllerManager)
		setAppsflyerRoutes(protected.Group("api/appsflyer"), controllerManager)
		setDestinationSyncRoutes(protected.Group("api/destination-sync"), controllerManager)
		setDestinationRoutes(protected.Group("api/destinations"), controllerManager)
		setFeatureStoreRoutes(protected.Group("api/feature-store"), controllerManager)
		setCleanupRoutes(protected.Group("api/cleanup"), controllerManager)
	}

	return r
}

// Add new auth routes
func setAuthRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("/google/login", WrapHandlerWithManager(handlers.InitiateGoogleLogin, controllerManager))
	r.GET("/google/callback", WrapHandlerWithManager(handlers.OauthGoogleCallback, controllerManager))
	r.POST("/refresh", WrapHandlerWithManager(handlers.RefreshJWTRequest, controllerManager))
	r.POST("/logout", gin.HandlerFunc(handlers.LogoutUser))
}

func WrapHandlerWithManager(handler func(*gin.Context, *manager.Manager), controllerManager *manager.Manager) gin.HandlerFunc {
	return func(c *gin.Context) {
		handler(c, controllerManager)
	}
}

func setHealthCheckRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("", WrapHandlerWithManager(handlers.Health, controllerManager))
}

func setDataSourceConfigRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("", WrapHandlerWithManager(handlers.AddDataSourceConfig, controllerManager))
	r.GET("/types", WrapHandlerWithManager(handlers.GetAllDataSourceTypes, controllerManager))
	r.GET("/required-keys", WrapHandlerWithManager(handlers.GetConfigFields, controllerManager))
}

func setDataSourcesRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("", WrapHandlerWithManager(handlers.AddDataSource, controllerManager))
	r.DELETE("", WrapHandlerWithManager(handlers.DeleteDataSource, controllerManager))   // To be implemented
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllDataSources, controllerManager)) // Get All Datasources
	r.GET("", WrapHandlerWithManager(handlers.GetDataSourceByID, controllerManager))     // Get Datasource by ID
}

func setEngineRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("preview-query", WrapHandlerWithManager(handlers.GetQueryPreview, controllerManager))
	r.POST("trigger-database-backup", WrapHandlerWithManager(handlers.TriggerDatabaseBackup, controllerManager))
	r.POST("schedule-database-backup", WrapHandlerWithManager(handlers.ScheduleDatabaseBackup, controllerManager))
	r.POST("update-database-backup-cron", WrapHandlerWithManager(handlers.UpdateDatabaseBackupCron, controllerManager))
}

func setEntityRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllEntities, controllerManager))
	r.GET("", WrapHandlerWithManager(handlers.GetEntityByID, controllerManager))
	r.GET("/total-users", WrapHandlerWithManager(handlers.GetTotalUsers, controllerManager))
}

func setAttributeRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("register", WrapHandlerWithManager(handlers.RegisterAttribute, controllerManager))
	r.GET("", WrapHandlerWithManager(handlers.GetAttributeByID, controllerManager))
	r.PUT("", WrapHandlerWithManager(handlers.UpdateAttribute, controllerManager))
	r.GET("/all/base", WrapHandlerWithManager(handlers.GetAllBaseAttributes, controllerManager))
	r.GET("/all/derived", WrapHandlerWithManager(handlers.GetAllDerivedAttributes, controllerManager))
	r.GET("/schema", WrapHandlerWithManager(handlers.GetAttributeSchemaDetails, controllerManager))
	r.GET("/schema/tg-cg", WrapHandlerWithManager(handlers.GetAttributeSchemaDetailsTgCg, controllerManager))
	r.POST("validate/name", WrapHandlerWithManager(handlers.ValidateAttributeNames, controllerManager))
	r.GET("/distinct-values", WrapHandlerWithManager(handlers.GetAttributeDistinctValues, controllerManager))
}

func setAttributeQueriesRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("", WrapHandlerWithManager(handlers.GetAttributeQueryByID, controllerManager))
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllAttributeQueries, controllerManager))
	r.POST("/validate/query", WrapHandlerWithManager(handlers.ValidateAttributeQuery, controllerManager))
	r.GET("/manual-trigger", WrapHandlerWithManager(handlers.ManualTrigger, controllerManager))
	r.POST("/update/query", WrapHandlerWithManager(handlers.UpdateQuery, controllerManager))
	r.POST("/update/cron", WrapHandlerWithManager(handlers.UpdateCron, controllerManager))
	r.POST("/update/end-date", WrapHandlerWithManager(handlers.UpdateEndDate, controllerManager))
}

func setSegmentsRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("/register", WrapHandlerWithManager(handlers.AddSegment, controllerManager))
	r.PUT("", WrapHandlerWithManager(handlers.UpdateSegment, controllerManager))
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllSegments, controllerManager))
	r.GET("/overview", WrapHandlerWithManager(handlers.GetSegmentOverview, controllerManager))
	r.GET("/upstream-attributes", WrapHandlerWithManager(handlers.GetUpstreamAttributeInfo, controllerManager))
	r.GET("/names", WrapHandlerWithManager(handlers.GetAllSegmentNames, controllerManager))
	r.POST("/size", WrapHandlerWithManager(handlers.GetSegmentSize, controllerManager))
	r.POST("/size/tg-cg", WrapHandlerWithManager(handlers.GetSegmentSizeTgCg, controllerManager))
	r.GET("/overview/size", WrapHandlerWithManager(handlers.GetSegmentSizeById, controllerManager))
	r.GET("/destinations", WrapHandlerWithManager(handlers.GetDownstreamDestinationsOfSegment, controllerManager))
}

func setAppsflyerRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("create-audience", WrapHandlerWithManager(handlers.CreateAudience, controllerManager))
	r.GET("audiences", WrapHandlerWithManager(handlers.GetAllAudiences, controllerManager))
}

func setDestinationSyncRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllDestinationSyncs, controllerManager))
	r.POST("/register", WrapHandlerWithManager(handlers.RegisterDestinationSync, controllerManager))
	r.GET("/overview", WrapHandlerWithManager(handlers.GetDestinationSyncOverview, controllerManager))
	r.POST("/validate/segment", WrapHandlerWithManager(handlers.ValidateDestinationSyncSegment, controllerManager))
	r.POST("/validate/audience-name", WrapHandlerWithManager(handlers.ValidateDestinationSyncAppsflyerAudienceName, controllerManager))
	r.POST("/validate/feature-name", WrapHandlerWithManager(handlers.ValidateDestinationSyncFeatureStoreSegment, controllerManager))
	r.POST("/validate/size", WrapHandlerWithManager(handlers.ValidateDestinationSyncSize, controllerManager))
	r.POST("/update/cron", WrapHandlerWithManager(handlers.UpdateDestinationSyncCron, controllerManager))
	r.POST("/update/tg-cg", WrapHandlerWithManager(handlers.UpdateDestinationSyncTgCg, controllerManager))
	r.POST("/update/end-date", WrapHandlerWithManager(handlers.UpdateDestinationSyncEndDate, controllerManager))
}

func setDestinationRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.GET("/all", WrapHandlerWithManager(handlers.GetAllDestinations, controllerManager))
}

func setFeatureStoreRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("/trigger-spark-job", WrapHandlerWithManager(handlers.ManualTriggerSparkJob, controllerManager))
	r.GET("/feature", WrapHandlerWithManager(handlers.GetFeature, controllerManager))
	r.POST("/schedule-spark-job", WrapHandlerWithManager(handlers.ScheduleSparkJob, controllerManager))
	r.POST("/schedule-status-checker", WrapHandlerWithManager(handlers.ScheduleFeatureStoreStatusChecker, controllerManager))
	r.POST("/trigger-status-checker", WrapHandlerWithManager(handlers.ManualTriggerFeatureStoreStatusChecker, controllerManager))
}

func setCleanupRoutes(r *gin.RouterGroup, controllerManager *manager.Manager) {
	r.POST("/attribute-syncs/schedule", WrapHandlerWithManager(handlers.ScheduleAttributeQueriesCleanup, controllerManager))
	r.POST("/attribute-syncs/trigger", WrapHandlerWithManager(handlers.TriggerAttributeQueriesCleanup, controllerManager))
}
