package authorization

import (
	"errors"

	"github.com/gin-gonic/gin"
)

func ValidateOwnership(c *gin.Context, resourceOwnerID int64) error {
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		return errors.New("user not authenticated")
	}

	userID, ok := userIDInterface.(int64)
	if !ok {
		return errors.New("invalid user ID format")
	}

	if userID != resourceOwnerID {
		return errors.New("unauthorized: only owner can perform this action")
	}

	return nil
}
