package env

import (
	"context"
	"errors"

	"github.com/Zomato/cdp-platform/internal/database/postgres"
	"github.com/Zomato/cdp-platform/internal/database/starrocks"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/cdp-platform/pkg/emr"
	featurestore "github.com/Zomato/cdp-platform/pkg/feature-store"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/cdp-platform/pkg/s3"
	"github.com/Zomato/cdp-platform/pkg/temporal"
	"github.com/gin-gonic/gin"
)

const (
	// EnvCtxKey is the key to set and retrieve Env in context
	EnvCtxKey string = "env"
)

// Env is the application environment exposing application scoped instances
// in request handlers
type Env struct {
	postgresClient  *postgres.DB
	temporalClient  *temporal.Client
	starrocksClient *starrocks.DB
	kafkaProducer   *kafka.Producer
	appsflyerClient *appsflyer.Client
	emrClient       *emr.Client
	featureStore    *featurestore.Client
	s3Client        *s3.Client
}

// NewEnv returns a new Env instance
func NewEnv(options ...func(env *Env)) Env {
	env := Env{}

	for _, option := range options {
		option(&env)
	}

	return env
}

func MiddleWare(env *Env) func(*gin.Context) {
	return func(ctx *gin.Context) {
		ctx.Set(EnvCtxKey, env)
		ctx.Next()
	}
}

// WithpostgresClient stores sqs-client in env
func WithPostgresClient(postgresClient *postgres.DB) func(*Env) {
	return func(env *Env) {
		env.postgresClient = postgresClient
	}
}

// postgresClient retrieves the postgres connection from the Env
func (env *Env) PostgresClient() *postgres.DB {
	return env.postgresClient
}

func WithTemporalClient(client *temporal.Client) func(*Env) {
	return func(e *Env) {
		e.temporalClient = client
	}
}

// TemporalClient retrieves the Temporal client from the Env
func (env *Env) TemporalClient() *temporal.Client {
	return env.temporalClient
}

func WithStarRocksClient(starrocksClient *starrocks.DB) func(*Env) {
	return func(env *Env) {
		env.starrocksClient = starrocksClient
	}
}

func (env *Env) StarRocksClient() *starrocks.DB {
	return env.starrocksClient
}

func WithKafkaProducer(kafkaProducer *kafka.Producer) func(*Env) {
	return func(env *Env) {
		env.kafkaProducer = kafkaProducer
	}
}

func (env *Env) KafkaProducer() *kafka.Producer {
	return env.kafkaProducer
}

func WithAppsflyerClient(appsflyerClient *appsflyer.Client) func(*Env) {
	return func(env *Env) {
		env.appsflyerClient = appsflyerClient
	}
}

func (env *Env) AppsflyerClient() *appsflyer.Client {
	return env.appsflyerClient
}

func WithEMRClient(emrClient *emr.Client) func(*Env) {
	return func(env *Env) {
		env.emrClient = emrClient
	}
}

func WithS3Client(s3Client *s3.Client) func(*Env) {
	return func(env *Env) {
		env.s3Client = s3Client
	}
}

func WithFeatureStoreClient(featureStore *featurestore.Client) func(*Env) {
	return func(env *Env) {
		env.featureStore = featureStore
	}
}

func (env *Env) FeatureStoreClient() *featurestore.Client {
	return env.featureStore
}

func (env *Env) EMRClient() *emr.Client {
	return env.emrClient
}

func (env *Env) S3Client() *s3.Client {
	return env.s3Client
}

// WithContext returns a context containing the env Value
func (env *Env) WithContext(ctx context.Context) context.Context {
	nctx := context.WithValue(ctx, EnvCtxKey, env)
	return nctx
}

// FromContext retrieves the Env from the current context, if no Env is found
// in the context and error is returned
func FromContext(ctx context.Context) (*Env, error) {
	env, ok := ctx.Value(EnvCtxKey).(*Env)
	if !ok {
		return env, errors.New("failed to get environment from context")
	}
	return env, nil
}
