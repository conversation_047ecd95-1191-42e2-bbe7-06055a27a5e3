package manager

import (
	"context"

	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	appsflyercontroller "github.com/Zomato/cdp-platform/internal/controllers/appsflyer"
	attributequeriescontroller "github.com/Zomato/cdp-platform/internal/controllers/attributequeries"
	attributecontroller "github.com/Zomato/cdp-platform/internal/controllers/attributes"
	attributesyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/attributesyncruns"
	cleanupcontroller "github.com/Zomato/cdp-platform/internal/controllers/cleanup"
	"github.com/Zomato/cdp-platform/internal/controllers/configcontroller"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	datasourceconfigcontroller "github.com/Zomato/cdp-platform/internal/controllers/datasourceconfig"
	destinationcontroller "github.com/Zomato/cdp-platform/internal/controllers/destinations"
	destinationsynccontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsync"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	destinationsyncrunspartitioncontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncrunspartition"
	emrcontroller "github.com/Zomato/cdp-platform/internal/controllers/emr"
	entitycontroller "github.com/Zomato/cdp-platform/internal/controllers/entity"
	featurestorecontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestore"
	featurestoresparkcontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestorespark"
	kafkacontroller "github.com/Zomato/cdp-platform/internal/controllers/kafka"
	queryenginecontroller "github.com/Zomato/cdp-platform/internal/controllers/queryengine.go"
	segmentcontroller "github.com/Zomato/cdp-platform/internal/controllers/segment"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	tagcontroller "github.com/Zomato/cdp-platform/internal/controllers/tag"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/controllers/trinocontroller"
	usercontroller "github.com/Zomato/cdp-platform/internal/controllers/user"
	"github.com/Zomato/cdp-platform/internal/controllers/userattributes"
	"github.com/Zomato/cdp-platform/internal/database/postgres"
	"github.com/Zomato/cdp-platform/internal/database/starrocks"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/cdp-platform/internal/temporal/workflows"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/cdp-platform/pkg/emr"
	featurestore "github.com/Zomato/cdp-platform/pkg/feature-store"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/cdp-platform/pkg/s3"
	"github.com/Zomato/cdp-platform/pkg/temporal"
)

type Manager struct {
	BaseController                         *basecontroller.Controller
	ConfigController                       *configcontroller.Controller
	AttributeController                    *attributecontroller.Controller
	AttributeQueriesController             *attributequeriescontroller.Controller
	AttributeSyncRunsController            *attributesyncrunscontroller.Controller
	CleanupController                      *cleanupcontroller.Controller
	DatasourceController                   *datasourcecontroller.Controller
	DatasourceConfigController             *datasourceconfigcontroller.Controller
	TagController                          *tagcontroller.Controller
	UserAttributesController               *userattributes.Controller
	SegmentController                      *segmentcontroller.Controller
	TrinoController                        *trinocontroller.Controller
	DestinationSyncRunsController          *destinationsyncrunscontroller.Controller
	DestinationSyncController              *destinationsynccontroller.Controller
	StarrocksController                    *starrockscontroller.Controller
	AppsflyerController                    *appsflyercontroller.Controller
	KafkaController                        *kafkacontroller.Controller
	DestinationSyncRunsPartitionController *destinationsyncrunspartitioncontroller.Controller
	QueryEngineController                  *queryenginecontroller.Controller
	TemporalController                     *temporalcontroller.Controller
	DestinationController                  *destinationcontroller.Controller
	UserController                         *usercontroller.Controller
	EMRController                          *emrcontroller.Controller
	EntityController                       *entitycontroller.Controller
	FeatureStoreController                 *featurestorecontroller.Controller
	FeatureStoreSparkController            *featurestoresparkcontroller.Controller
}

func NewManager(ctx context.Context, postgresClient *postgres.DB, starrocksClient *starrocks.DB, temporalClient *temporal.Client, kafkaProducer *kafka.Producer, appsflyerClient *appsflyer.Client, emrClient *emr.Client, featureStoreClient *featurestore.Client, s3Client *s3.Client) *Manager {
	// Create base controller that will be shared among all controllers
	baseCtrl := basecontroller.NewController(ctx, postgresClient, starrocksClient, temporalClient, kafkaProducer, appsflyerClient, s3Client, emrClient, featureStoreClient)

	configCtrl := &configcontroller.Controller{
		Controller: baseCtrl,
	}

	// Initialize controllers that don't have dependencies first
	trinoCtrl := &trinocontroller.Controller{
		Controller: baseCtrl,
	}

	userCtrl := &usercontroller.Controller{
		Controller: baseCtrl,
	}

	kafkaCtrl := &kafkacontroller.Controller{
		Controller: baseCtrl,
	}

	datasourceConfigCtrl := &datasourceconfigcontroller.Controller{
		Controller: baseCtrl,
	}

	datasourceCtrl := &datasourcecontroller.Controller{
		Controller: baseCtrl,
	}

	entityCtrl := &entitycontroller.Controller{
		Controller: baseCtrl,
	}

	attributeSyncRunsCtrl := &attributesyncrunscontroller.Controller{
		Controller: baseCtrl,
	}

	tagCtrl := &tagcontroller.Controller{
		Controller: baseCtrl,
	}

	// Initialize EMR controller
	emrCtrl := emrcontroller.NewController(baseCtrl, emrClient)

	// Initialize controllers with dependencies
	temporalCtrl := &temporalcontroller.Controller{
		Controller: baseCtrl,
		Workflows: map[enum.WorkflowType]interface{}{
			enum.SyncAttributeQueryWorkflowSpark:     workflows.SyncAttributeQueryWorkflowSpark,
			enum.SyncAttributeQueryWorkflow:          workflows.SyncAttributeQueryWorkflow,
			enum.DestinationSyncWorkflow:             workflows.DestinationSyncWorkflow,
			enum.DestinationSyncCompletedWorkflow:    workflows.DestinationSyncCompletedWorkflow,
			enum.StarrocksDatabaseBackupWorkflow:     workflows.StarrocksDatabaseBackupWorkflow,
			enum.DestinationSyncWorkflowFeatureStore: workflows.DestinationSyncWorkflowFeatureStore,
			enum.FeatureStoreSparkWorkflow:           workflows.FeatureStoreSparkWorkflow,
			enum.AttributeQueriesCleanupWorkflow:     workflows.AttributeQueriesCleanupWorkflow,
			enum.FeatureStoreStatusSchedulerWorkflow: workflows.FeatureStoreStatusSchedulerWorkflow,
		},
	}

	starrocksCtrl := &starrockscontroller.Controller{
		Controller:         baseCtrl,
		TemporalController: temporalCtrl,
	}

	destinationCtrl := &destinationcontroller.Controller{
		Controller: baseCtrl,
	}

	queryEngineCtrl := &queryenginecontroller.Controller{
		Controller:           baseCtrl,
		TrinoController:      trinoCtrl,
		DataSourceController: datasourceCtrl,
	}

	userAttributesCtrl := &userattributes.Controller{
		Controller:                  baseCtrl,
		AttributeSyncRunsController: attributeSyncRunsCtrl,
		StarrocksController:         starrocksCtrl,
	}

	attributeQueriesCtrl := &attributequeriescontroller.Controller{
		Controller:                  baseCtrl,
		AttributeSyncRunsController: attributeSyncRunsCtrl,
		QueryEngineController:       queryEngineCtrl,
		TemporalController:          temporalCtrl,
		DataSourceController:        datasourceCtrl,
	}

	attributeCtrl := &attributecontroller.Controller{
		Controller:                 baseCtrl,
		AttributeQueriesController: attributeQueriesCtrl,
		TemporalController:         temporalCtrl,
		StarrocksController:        starrocksCtrl,
		DataSourceController:       datasourceCtrl,
		EntityController:           entityCtrl,
	}

	segmentCtrl := &segmentcontroller.Controller{
		Controller:           baseCtrl,
		AttributesController: attributeCtrl,
		EntityController:     entityCtrl, // ! IMP otherwise will be nil and no method will work
	}

	appsflyerCtrl := &appsflyercontroller.Controller{
		Controller:          baseCtrl,
		StarrocksController: starrocksCtrl,
		KafkaController:     kafkaCtrl,
		AppsflyerClient:     appsflyerClient,
	}

	destinationSyncRunsPartitionCtrl := &destinationsyncrunspartitioncontroller.Controller{
		Controller:      baseCtrl,
		KafkaController: kafkaCtrl,
	}

	featureStoreCtrl := &featurestorecontroller.Controller{
		Controller:            baseCtrl,
		FeatureStoreClient:    featureStoreClient,
		DestinationController: destinationCtrl,
		UserController:        userCtrl,
	}
	destinationSyncRunsCtrl := &destinationsyncrunscontroller.Controller{
		Controller:             baseCtrl,
		SegmentController:      segmentCtrl,
		FeatureStoreController: featureStoreCtrl,
	}

	destinationSyncCtrl := &destinationsynccontroller.Controller{
		Controller:                    baseCtrl,
		SegmentController:             segmentCtrl,
		AppsflyerController:           appsflyerCtrl,
		DestinationSyncRunsController: destinationSyncRunsCtrl,
		TemporalController:            temporalCtrl,
		FeatureStoreController:        featureStoreCtrl,
	}

	featureStoreSparkCtrl := &featurestoresparkcontroller.Controller{
		Controller:                   baseCtrl,
		TemporalController:           temporalCtrl,
		DestinationSyncRunController: destinationSyncRunsCtrl,
		ConfigController:             configCtrl,
	}

	cleanupCtrl := &cleanupcontroller.Controller{
		Controller:           baseCtrl,
		DatasourceController: datasourceCtrl,
		TemporalController:   temporalCtrl,
		TrinoController:      trinoCtrl,
	}

	return &Manager{
		BaseController:                         baseCtrl,
		AttributeController:                    attributeCtrl,
		AttributeQueriesController:             attributeQueriesCtrl,
		AttributeSyncRunsController:            attributeSyncRunsCtrl,
		CleanupController:                      cleanupCtrl,
		DatasourceController:                   datasourceCtrl,
		DatasourceConfigController:             datasourceConfigCtrl,
		TagController:                          tagCtrl,
		UserAttributesController:               userAttributesCtrl,
		SegmentController:                      segmentCtrl,
		TrinoController:                        trinoCtrl,
		DestinationSyncRunsController:          destinationSyncRunsCtrl,
		DestinationSyncController:              destinationSyncCtrl,
		StarrocksController:                    starrocksCtrl,
		AppsflyerController:                    appsflyerCtrl,
		KafkaController:                        kafkaCtrl,
		DestinationSyncRunsPartitionController: destinationSyncRunsPartitionCtrl,
		QueryEngineController:                  queryEngineCtrl,
		TemporalController:                     temporalCtrl,
		DestinationController:                  destinationCtrl,
		UserController:                         userCtrl,
		ConfigController:                       configCtrl,
		EMRController:                          emrCtrl,
		EntityController:                       entityCtrl,
		FeatureStoreController:                 featureStoreCtrl,
		FeatureStoreSparkController:            featureStoreSparkCtrl,
	}
}
