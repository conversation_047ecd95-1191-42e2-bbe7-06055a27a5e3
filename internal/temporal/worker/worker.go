package temporalworker

import (
	"context"
	"errors"

	"github.com/Zomato/cdp-platform/internal/env"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/internal/temporal/workflows"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/logger"
	"go.temporal.io/sdk/worker"
)

func SetupTemporalWorker(ctx context.Context) error {
	environment, err := env.FromContext(ctx)
	if err != nil {
		return errors.New("unable to get environment from context")
	}
	temporalClient := environment.TemporalClient()
	if temporalClient == nil {
		return errors.New("temporal client not available in the environment")
	}

	taskQueue := config.GetString(ctx, "temporal.task_queue")

	// Create a worker
	worker := temporalClient.NewWorker(taskQueue)

	// Setup Controller Manager
	postgresClient := environment.PostgresClient()
	starrocksClient := environment.StarRocksClient()
	kafkaProducer := environment.KafkaProducer()
	appsflyerClient := environment.AppsflyerClient()
	emrClient := environment.EMRClient()
	featureStoreClient := environment.FeatureStoreClient()
	s3Client := environment.S3Client()
	controllerManager := manager.NewManager(ctx, postgresClient, starrocksClient, temporalClient, kafkaProducer, appsflyerClient, emrClient, featureStoreClient, s3Client)

	// Register all controllers as activities
	registerAllControllersAsActivity(worker, controllerManager)

	// Register workflows
	registerWorkflows(worker)

	// Run the worker
	err = temporalClient.RunWorker(worker)
	if err != nil {
		logger.Panic("Error while fetching App context", err)
		return err
	}
	return nil
}

func registerAllControllersAsActivity(temporalWorker worker.Worker, controllerManager *manager.Manager) {
	// temporalWorker.RegisterActivity(controllerManager) // THis wont work as controllerManager itself does not have methods, its objects has methods
	temporalWorker.RegisterActivity(controllerManager.ConfigController)
	temporalWorker.RegisterActivity(controllerManager.AttributeController)
	temporalWorker.RegisterActivity(controllerManager.AttributeQueriesController)
	temporalWorker.RegisterActivity(controllerManager.AttributeSyncRunsController)
	temporalWorker.RegisterActivity(controllerManager.DatasourceController)
	temporalWorker.RegisterActivity(controllerManager.DatasourceConfigController)
	temporalWorker.RegisterActivity(controllerManager.TagController)
	temporalWorker.RegisterActivity(controllerManager.UserAttributesController)
	temporalWorker.RegisterActivity(controllerManager.SegmentController)
	temporalWorker.RegisterActivity(controllerManager.TrinoController)
	temporalWorker.RegisterActivity(controllerManager.DestinationSyncController)
	temporalWorker.RegisterActivity(controllerManager.StarrocksController)
	temporalWorker.RegisterActivity(controllerManager.DestinationSyncRunsController)
	temporalWorker.RegisterActivity(controllerManager.AppsflyerController)
	temporalWorker.RegisterActivity(controllerManager.KafkaController)
	temporalWorker.RegisterActivity(controllerManager.DestinationSyncRunsPartitionController)
	temporalWorker.RegisterActivity(controllerManager.EMRController)
	temporalWorker.RegisterActivity(controllerManager.EntityController)
	temporalWorker.RegisterActivity(controllerManager.FeatureStoreController)
	temporalWorker.RegisterActivity(controllerManager.FeatureStoreSparkController)
	temporalWorker.RegisterActivity(controllerManager.CleanupController)
}

func registerWorkflows(temporalWorker worker.Worker) {
	registerTestWorkflow(temporalWorker)
	registerAttributeSyncWorkflow(temporalWorker)
	registerDestinationSyncWorkflowAppsflyer(temporalWorker)
	registerDestinationSyncCompletedWorkflow(temporalWorker)
	registerStarrocksDatabaseBackupWorkflow(temporalWorker)
	registerFeatureStoreSparkWorkflow(temporalWorker)
	registerDestinationSyncWorkflowFeatureStore(temporalWorker)
	registerFeatureStoreStatusSchedulerWorkflow(temporalWorker)
	registerFeatureStoreLogTableProcessingWorkflow(temporalWorker)
	registerAttributeQueriesCleanupWorkflow(temporalWorker)
}

func registerTestWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.TestTemporalWorkflow)
}

func registerAttributeSyncWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.SyncAttributeQueryWorkflow)
	temporalWorker.RegisterWorkflow(workflows.SyncAttributeQueryWorkflowSpark)
}

func registerDestinationSyncWorkflowAppsflyer(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.DestinationSyncWorkflow)
}

func registerDestinationSyncCompletedWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.DestinationSyncCompletedWorkflow)
}

func registerStarrocksDatabaseBackupWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.StarrocksDatabaseBackupWorkflow)
}

func registerFeatureStoreSparkWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.FeatureStoreSparkWorkflow)
}

func registerDestinationSyncWorkflowFeatureStore(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.DestinationSyncWorkflowFeatureStore)
}

func registerFeatureStoreStatusSchedulerWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.FeatureStoreStatusSchedulerWorkflow)
}

func registerFeatureStoreLogTableProcessingWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.FeatureStoreLogTableProcessingWorkflow)
}

func registerAttributeQueriesCleanupWorkflow(temporalWorker worker.Worker) {
	temporalWorker.RegisterWorkflow(workflows.AttributeQueriesCleanupWorkflow)
}
