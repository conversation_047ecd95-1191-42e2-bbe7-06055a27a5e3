package workflows

import (
	"fmt"
	"time"

	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// FeatureStoreStatusSchedulerWorkflow runs every 20 minutes to check and update
// feature store destination sync runs that are in DATA_PUSHED_FOR_CONSUMPTION state
func FeatureStoreStatusSchedulerWorkflow(ctx workflow.Context) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting FeatureStoreStatusSchedulerWorkflow")

	// Configure retry policy: maximum 1 retry attempt, then fail
	retryPolicy := &temporal.RetryPolicy{
		MaximumAttempts:        2, // Initial attempt + 1 retry = 2 total attempts
		InitialInterval:        time.Second * 30,
		BackoffCoefficient:     2.0,
		MaximumInterval:        time.Minute * 2,
		NonRetryableErrorTypes: []string{}, // All errors are retryable
	}

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 10, // Shorter timeout for status checks
		RetryPolicy:         retryPolicy,
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var destinationSyncRunsController destinationsyncrunscontroller.Controller

	// Execute the status update activity
	var successfulRunIDs []int64
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdatePendingFeatureStoreRuns).Get(ctx, &successfulRunIDs)
	if err != nil {
		logger.Error("Failed to update pending feature store runs", "error", err)
		return fmt.Errorf("failed to update pending feature store runs: %w", err)
	}

	// Trigger log processing workflow for each successful run
	triggeredCount := 0
	for _, runID := range successfulRunIDs {
		// Create unique workflow ID for each child workflow
		childWorkflowOptions := workflow.ChildWorkflowOptions{
			WorkflowID: fmt.Sprintf("FeatureStoreLogTableProcessing_%d_%d", runID, workflow.Now(ctx).Unix()),
		}
		childCtx := workflow.WithChildOptions(ctx, childWorkflowOptions)

		// Execute child workflow directly
		err := workflow.ExecuteChildWorkflow(childCtx, FeatureStoreLogTableProcessingWorkflow, runID).Get(ctx, nil)
		if err != nil {
			// Log error but don't fail the main workflow
			logger.Error("Failed to trigger log table processing workflow",
				"runID", runID, "error", err)
		} else {
			logger.Info("Successfully triggered log table processing workflow", "runID", runID)
			triggeredCount++
		}
	}

	logger.Info("FeatureStoreStatusSchedulerWorkflow completed successfully",
		"updatedRunsCount", len(successfulRunIDs),
		"triggeredLogProcessing", triggeredCount)

	return nil
}
