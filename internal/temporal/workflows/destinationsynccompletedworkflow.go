package workflows

import (
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

func markDestinationSyncCompletedFailed(ctx workflow.Context, destinationSyncRunID int64, errMsg string) error {
	now := workflow.Now(ctx)
	destinationSyncRun := dtos.DestinationSyncRun{
		Base:         dtos.Base{Id: destinationSyncRunID},
		Status:       "FAILED",
		ExecutionEnd: &now,
		ErrorMessage: &errMsg,
	}
	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to mark destination sync run as failed: %s", err)
	}
	return nil
}

func DestinationSyncCompletedWorkflow(ctx workflow.Context, destinationSyncRunID int64) error {
	logger.Infof("Starting DestinationSyncCompletedWorkflow for destination sync run id : %d", destinationSyncRunID)

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 30,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 5,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	var starrocksController starrockscontroller.Controller

	// Get Ongoing Table MV name from destinationSyncRunID
	var syncTableName string
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.GetOngoingSegmentTableNameByDestinationSyncRunID, destinationSyncRunID).Get(ctx, &syncTableName)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get SyncTableName from destination_sync_run: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to get SyncTableName from destination_sync_run, %s", err)
	}

	// Switch Table names in Destination Sync
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.CommitSegmentTableName, destinationSyncRunID).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to switch table names in Destination Sync: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to switch table names in Destination Sync, %s", err)
	}

	// Mark the Destination Sync As Complete | So that other process can process doesn't impact next run
	executionEnd := workflow.Now(ctx)
	destinationSyncRun := dtos.DestinationSyncRun{
		Base:         dtos.Base{Id: destinationSyncRunID},
		Status:       "SUCCESSFUL",
		ExecutionEnd: &executionEnd,
	}
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update destination sync run status to SUCCESSFUL: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to update destination sync run, %s", err)
	}

	// Get Audience Name
	var audienceName string
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.GetAudienceNameByDestinationSyncRunID, destinationSyncRunID).Get(ctx, &audienceName)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get audienceName from destination_sync_run: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to get audienceName from destination_sync_run, %s", err)
	}

	// Create hive Table from MV (this can happen in Async manner and next destination sync run can start as it is dt and hr partitioned)
	var appsflyerTableName = strings.ReplaceAll(audienceName, " ", "_")
	if len(appsflyerTableName) > 50 {
		appsflyerTableName = appsflyerTableName[:50] // Required otherwise CREATE TABLE FAILS
	}
	err = workflow.ExecuteActivity(ctx, starrocksController.CreateHiveLogTableFromHiveRunTable, syncTableName, appsflyerTableName).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create hive log table from hive run table: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to create hive log table from hive run table, %s", err)
	}

	// Add this table name in the AppsFlyer Table
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateSnapshotTableNameInAppsflyer, destinationSyncRunID, appsflyerTableName).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update snapshot table name in Appsflyer: %v", err)
		markDestinationSyncCompletedFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("failed to update snapshot table name in Appsflyer, %s", err)
	}

	return nil
}
