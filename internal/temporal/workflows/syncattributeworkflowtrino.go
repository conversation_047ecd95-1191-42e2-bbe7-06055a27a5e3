package workflows

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	attributequeriescontroller "github.com/Zomato/cdp-platform/internal/controllers/attributequeries"
	attributesyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/attributesyncruns"
	entitycontroller "github.com/Zomato/cdp-platform/internal/controllers/entity"
	"github.com/Zomato/cdp-platform/internal/controllers/trinocontroller"
	"github.com/Zomato/cdp-platform/internal/controllers/userattributes"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const USER_ENTITY_ID = 1

func markSyncAttributeFailed(ctx workflow.Context, attributeSyncRunDTO *dtos.AttributeSyncRuns, errMsg string) error {
	now := workflow.Now(ctx)
	attributeSyncRunDTO.Status = "FAILED"
	attributeSyncRunDTO.ExecutionEnd = &now
	attributeSyncRunDTO.ErrorMessage = &errMsg
	var attributeSyncRunsController attributesyncrunscontroller.Controller
	err := workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	return err
}

func SyncAttributeQueryWorkflow(ctx workflow.Context, attributeQueryID int64) (string, error) {
	logger.Infof("Starting SyncAttributeQueryWorkflow for attributeQueryID id : %d", attributeQueryID)

	var attributeSyncRunsController attributesyncrunscontroller.Controller
	var attributeQueryController attributequeriescontroller.Controller
	var trinoController trinocontroller.Controller
	var userattributesController userattributes.Controller
	var entityController entitycontroller.Controller

	var trinoConfigMap json.RawMessage
	var attributeQueryText string

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 60,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	// STEP 1: Insert in Attribute Sync Runs
	executionStart := workflow.Now(ctx)
	attributeSyncRunDTO := &dtos.AttributeSyncRuns{
		AttributeQueryID: attributeQueryID,
		Status:           "STARTED",
		ExecutionStart:   &executionStart,
	}
	err := workflow.ExecuteActivity(ctx, attributeSyncRunsController.AddAttributeSyncRun, attributeSyncRunDTO).Get(ctx, &attributeSyncRunDTO)
	if err != nil {
		logger.Error("Activity AddAttributeSyncRun failed", "error", err)
		return "Failure", err
	}

	// STEP 2: Get Attribute Query Text using attributeQueryID
	err = workflow.ExecuteActivity(ctx, attributeQueryController.GetAttributeQuery, attributeQueryID).Get(ctx, &attributeQueryText)
	if err != nil {
		logger.Error("Activity GetAttributeQuery failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get attribute query text: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	// STEP 3: Get Trino Config to connect to trino
	err = workflow.ExecuteActivity(ctx, attributeQueryController.GetConfigMapByAttributeQueryID, attributeQueryID).Get(ctx, &trinoConfigMap)
	if err != nil {
		logger.Error("Activity GetConfigMapByAttributeQueryID failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get Trino config map: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	// STEP 4: Create interim Hive table
	var interimTableName string
	err = workflow.ExecuteActivity(ctx, trinoController.CreateHiveTable, trinoConfigMap, attributeQueryText, attributeQueryID).Get(ctx, &interimTableName)
	if err != nil {
		logger.Error("Activity trinoController.CreateHiveTable failed", "error", err)
		errMsg := fmt.Sprintf("Failed to intrim table: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "", err
	}

	// STEP 5: Mark status in attribute sync to => INTERM_TABLE_CREATED and update syncTableName
	attributeSyncRunDTO.Status = "INTERM_TABLE_CREATED"
	attributeSyncRunDTO.SyncTableName = &interimTableName
	err = workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity UpdateAttributeSyncRun for status INTERM_TABLE_CREATED failed", "error", err)
		errMsg := fmt.Sprintf("Failed to update attribute sync run status to INTERM_TABLE_CREATED: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	// STEP 6: Sync Schema
	syncAttibute := &dtos.SyncAttibute{
		SyncTableName: interimTableName,
	}
	err = workflow.ExecuteActivity(ctx, userattributesController.SyncAttributeSchema, syncAttibute).Get(ctx, &syncAttibute.SyncColumns)
	if err != nil {
		logger.Error("Activity userattributesController.SyncTableSchema failed", "error", err)
		errMsg := fmt.Sprintf("Failed to sync attribute schema: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	// STEP 7: Submit broker load job to sync data
	err = workflow.ExecuteActivity(ctx, userattributesController.SyncAttributeData, syncAttibute).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity userattributesController.SyncTableData failed", "error", err)
		errMsg := fmt.Sprintf("Failed to sync attribute data: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	var loadJobStatus *dtos.LoadJobStatus
	// STEP 8: Poll for load job status
	err = workflow.ExecuteActivity(ctx, userattributesController.CheckSyncDataStatus, syncAttibute.SyncTableName).Get(ctx, &loadJobStatus)
	// mark activity as failed if load job is still stuck in loading state or we aren't able to get the status even after retries
	if err != nil {
		logger.Error("Activity userattributesController.CheckSyncDataStatus failed", "error", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, err.Error())
		return "Failure", err
	}

	// STEP 9: Mark status in attribute sync to => SUCCESSFUL
	executionEnd := workflow.Now(ctx)
	attributeSyncRunDTO.ExecutionEnd = &executionEnd
	attributeSyncRunDTO.RowsAffected = &loadJobStatus.ScanRows
	attributeSyncRunDTO.Status = "SUCCESSFUL"
	err = workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity UpdateAttributeSyncRun to SUCCESSFUL failed", "error", err)
		errMsg := fmt.Sprintf("Failed to update attribute sync run status to SUCCESSFUL: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	// STEP 10: Refresh all dependent entity views
	err = workflow.ExecuteActivity(ctx, entityController.RefreshDependentViews, USER_ENTITY_ID).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity RefreshDependentViews failed", "error", err)
		errMsg := fmt.Sprintf("Failed to refresh dependent views: %v", err)
		markSyncAttributeFailed(ctx, attributeSyncRunDTO, errMsg)
		return "Failure", err
	}

	return "Success", nil
}
