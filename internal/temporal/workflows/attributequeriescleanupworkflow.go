package workflows

import (
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	cleanupcontroller "github.com/Zomato/cdp-platform/internal/controllers/cleanup"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	"github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// AttributeQueriesCleanupWorkflow handles cleanup of temporary tables created during attribute sync operations
func AttributeQueriesCleanupWorkflow(ctx workflow.Context, datasourceID int64, retentionRunCount int) (*dtos.AttributeQueriesCleanupWorkflowResult, error) {
	logger.Infof("Starting AttributeQueriesCleanupWorkflow for datasourceID: %d, retentionRunCount: %d", datasourceID, retentionRunCount)

	var datasourceController datasourcecontroller.Controller

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 30,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	result := &dtos.AttributeQueriesCleanupWorkflowResult{
		TablesDropped: 0,
		Errors:        []string{},
	}

	var dataSourceInfo *dtos.DataSourceInfo
	err := workflow.ExecuteActivity(ctx, datasourceController.GetDataSourceConfigMapAndType, datasourceID).Get(ctx, &dataSourceInfo)
	if err != nil {
		logger.Error("Activity GetDataSourceConfigMapAndType failed", "error", err)
		result.Message = fmt.Sprintf("Failed to get datasource information: %v", err)
		result.Errors = append(result.Errors, err.Error())
		return result, err
	}

	logger.Infof("Processing cleanup for datasource type: %s", dataSourceInfo.DataSourceType)
	var expiredTablesResponse *dtos.ExpiredSyncRunTablesResponse
	err = workflow.ExecuteActivity(ctx, datasourceController.GetExpiredAttributeSyncRunTablesByDataSource, datasourceID, retentionRunCount).Get(ctx, &expiredTablesResponse)
	if err != nil {
		logger.Error("Activity GetExpiredAttributeSyncRunTablesByDataSource failed", "error", err)
		result.Message = fmt.Sprintf("Failed to get expired attribute sync run table names: %v", err)
		result.Errors = append(result.Errors, err.Error())
		return result, err
	}

	if len(expiredTablesResponse.ExpiredTables) == 0 {
		logger.Info("No old attribute sync run tables found for cleanup")
		result.Message = "No old attribute sync run tables found for cleanup"
		return result, nil
	}

	logger.Infof("Found %d tables to drop for datasource %d", len(expiredTablesResponse.ExpiredTables), datasourceID)

	var cleanupController cleanupcontroller.Controller
	err = workflow.ExecuteActivity(ctx, cleanupController.DropExpiredTables, expiredTablesResponse, dataSourceInfo.DataSourceType, dataSourceInfo.ConfigMap).Get(ctx, result)
	if err != nil {
		logger.Error("Activity DropExpiredTables failed", "error", err)
		result.Message = fmt.Sprintf("Failed to drop expired tables: %v", err)
		result.Errors = append(result.Errors, err.Error())
		return result, err
	}

	if len(result.Errors) == 0 {
		result.Message = fmt.Sprintf("Cleanup completed successfully. Dropped %d tables.", result.TablesDropped)
	} else {
		result.Message = fmt.Sprintf("Cleanup completed with %d errors. Dropped %d tables.", len(result.Errors), result.TablesDropped)
	}

	logger.Infof("AttributeQueriesCleanupWorkflow completed: %s", result.Message)
	return result, nil
}
