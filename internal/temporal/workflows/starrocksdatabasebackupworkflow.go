package workflows

import (
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

func StarrocksDatabaseBackupWorkflow(ctx workflow.Context, backupRequestIdentifier string, databaseName string, backUpRepo string) error {
	logger := workflow.GetLogger(ctx)

	var starrocksController starrockscontroller.Controller
	starrocksBackupRequest := dtos.DatabaseBackupRequest{DatabaseName: databaseName, BackupRepo: backUpRepo}

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Hour * 1,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)
	var snapshotName *string
	err := workflow.ExecuteActivity(ctx, starrocksController.BackupDatabase, &starrocksBackupRequest).Get(ctx, &snapshotName)
	if err != nil {
		logger.Error("Backup failed",
			"error", err,
			"database", starrocksBackupRequest.DatabaseName)
		return fmt.Errorf("backup failed: %w", err)
	}

	logger.Info("Database backup completed successfully",
		"database", starrocksBackupRequest.DatabaseName,
		"snapshot", snapshotName)

	return nil
}
