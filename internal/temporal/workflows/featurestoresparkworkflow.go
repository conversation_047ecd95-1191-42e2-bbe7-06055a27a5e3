package workflows

import (
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/controllers/configcontroller"
	destinationsynccontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsync"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	emrcontroller "github.com/Zomato/cdp-platform/internal/controllers/emr"
	featurestoresparkcontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestorespark"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const FEATURE_STORE_DESTINATION_TYPE = "FEATURE_STORE"

// getPropertiesS3Path generates the S3 path based on environment
// For now, we'll use a default environment since temporal workflows have limited access to config
// This should be enhanced to get the environment from the workflow context or passed as a parameter
func getPropertiesS3Path(env string) string {
	if env == "" {
		env = "local" // fallback to local if environment is not provided
	}
	return fmt.Sprintf("s3://blinkit-analytics/config/cdp/%s/spark/user/audiences_cdp_1.yml", env)
}

// markFeatureStoreSparkRunFailed is a helper function to mark a feature store spark run as failed
func markFeatureStoreSparkRunFailed(ctx workflow.Context, featureStoreSparkRun *dtos.FeatureStoreSparkRun, clusterId *string, errMsg string) error {
	var featureStoreSparkController featurestoresparkcontroller.Controller

	if clusterId != nil {
		var emrController emrcontroller.Controller
		err := workflow.ExecuteActivity(ctx, emrController.TerminateCluster, *clusterId).Get(ctx, nil)
		if err != nil {
			logger.Error("Failed to terminate EMR cluster", "error", err)
			return err
		}
	}

	featureStoreSparkRun.Status = "FAILED"
	featureStoreSparkRun.ErrorMessage = &errMsg

	executionEnd := workflow.Now(ctx)
	featureStoreSparkRun.ExecutionEnd = &executionEnd

	err := workflow.ExecuteActivity(ctx, featureStoreSparkController.UpdateFeatureStoreSparkRun, featureStoreSparkRun).Get(ctx, nil)
	if err != nil {
		logger.Error("Failed to mark feature store spark run as failed", "error", err)
		return err
	}

	return nil
}

// FeatureStoreSparkWorkflow handles the processing of feature store spark jobs using Spark
func FeatureStoreSparkWorkflow(ctx workflow.Context, featureStoreSparkRunId int) (string, error) {
	logger.Infof("Starting FeatureStoreSparkWorkflow")

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Hour * 1,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var featureStoreSparkController featurestoresparkcontroller.Controller
	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	var destinationSyncController destinationsynccontroller.Controller
	var emrController emrcontroller.Controller
	var configController configcontroller.Controller
	DESTINATION_SYNC_RUN_INTERM_TABLE_CREATED := "INTERM_TABLE_CREATED"

	// Get the maximum successful execution start time
	var maxExecutionStart *time.Time
	err := workflow.ExecuteActivity(ctx, featureStoreSparkController.GetMaxSuccessfulExecutionStart).Get(ctx, &maxExecutionStart)
	if err != nil {
		return "Failure", fmt.Errorf("[FAILURE] failed to get max successful execution start: %s", err)
	}

	// Check if any destination sync runs completed after the last execution start time
	var completed bool
	if maxExecutionStart == nil {
		completed = true
	} else {
		err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.DestinationSyncRunCompletedAfterGivenTime, *maxExecutionStart, FEATURE_STORE_DESTINATION_TYPE, DESTINATION_SYNC_RUN_INTERM_TABLE_CREATED).Get(ctx, &completed)
		if err != nil {
			return "Failure", fmt.Errorf("[FAILURE] failed to check destination sync run completion: %s", err)
		}
	}
	if !completed {
		return fmt.Sprintf("[SKIPPED] no successful destination sync runs found after max execution start"), nil
	}

	// Create a new feature store run record
	executionStart := workflow.Now(ctx)
	featureStoreSparkRunDTO := &dtos.FeatureStoreSparkRun{
		ExecutionStart: &executionStart,
		Status:         "STARTED",
	}

	// Insert the feature store spark run
	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.AddFeatureStoreSparkRun, featureStoreSparkRunDTO).Get(ctx, featureStoreSparkRunDTO)
	if err != nil {
		return "Failure", fmt.Errorf("[FAILURE] failed to insert feature store spark run: %s", err)
	}

	// Get feature store segment infomation
	var featureStoreSegmentInfo []dtos.FeatureStoreSegmentInfo
	err = workflow.ExecuteActivity(ctx, destinationSyncController.GetFeatureStoreSegmentInfo, DESTINATION_SYNC_RUN_INTERM_TABLE_CREATED).Get(ctx, &featureStoreSegmentInfo)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get feature store segment info: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	// Format the segment info into YAML
	var segmentYAML *dtos.SegmentYAML
	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.FormatSegmentInfoToYAML, featureStoreSegmentInfo).Get(ctx, &segmentYAML)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to format segment info to YAML: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	// Upload segment YAML to S3
	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.UploadSegmentYAMLToS3, segmentYAML).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to upload segment YAML to S3: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	// Create EMR Cluster for feature store processing
	var createClusterResponse dtos.CreateEMRClusterResponse
	err = workflow.ExecuteActivity(ctx, emrController.CreateEMRCluster, &dtos.CreateEMRClusterRequest{
		WorkflowName: "FeatureStoreSpark",
	}).Get(ctx, &createClusterResponse)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create EMR cluster: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	var env string
	err = workflow.ExecuteActivity(ctx, configController.GetEnvironment).Get(ctx, &env)
	if err != nil {
		logger.Warn("Failed to get environment, using default", "error", err)
		env = "local" // fallback to local if environment fetch fails
	}

	propertiesS3Path := getPropertiesS3Path(env)

	var submitJobResponse dtos.SubmitSparkJobResponse
	err = workflow.ExecuteActivity(ctx, emrController.SubmitSparkJob, &dtos.SubmitSparkJobRequest{
		WorkflowName: "FeatureStoreSpark",
		ClusterID:    createClusterResponse.ClusterID,
		JobName:      "FeatureStoreSparkJob",
		JarURI:       "s3://blinkit-analytics/config/cdp/feature_store_spark_cdp.py",
		Args: []string{
			"--properties_s3_path", propertiesS3Path,
			"--tenant", "BLINKIT",
			"--feature_store_spark_run_id", *featureStoreSparkRunDTO.RunID,
			"--environment", env,
		},
		AdditionalJars: []string{
			"s3://blinkit-analytics/artifacts/iceberg/iceberg-spark-runtime-3.3_2.12-1.3.0.jar",
		},
		ConfigOverrides: map[string]string{
			// Add any additional Spark configs that aren't in the template
			"spark.sql.extensions":                                "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
			"spark.sql.iceberg.handle-timestamp-without-timezone": "true",
			"spark.sql.catalog.iceberg":                           "org.apache.iceberg.spark.SparkCatalog",
			"spark.sql.catalog.iceberg.type":                      "hive",
			"spark.sql.catalog.iceberg.uri":                       "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9084",
			"spark.sql.catalog.iceberg_staging":                   "org.apache.iceberg.spark.SparkCatalog",
			"spark.sql.catalog.iceberg_staging.type":              "hive",
			"spark.sql.catalog.iceberg_staging.uri":               "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9083",
		},
	}).Get(ctx, &submitJobResponse)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to submit Spark job: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	retryOptions := temporal.RetryPolicy{
		InitialInterval:    time.Second * 10,
		MaximumInterval:    time.Second * 15,
		BackoffCoefficient: 1.5,
		MaximumAttempts:    2,
	}
	retryCtx := workflow.WithRetryPolicy(ctx, retryOptions)

	err = workflow.ExecuteActivity(retryCtx, emrController.WaitForStepCompletion, createClusterResponse.ClusterID, submitJobResponse.StepID, 6*time.Hour).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity WaitForStepCompletion failed", "error", err)
		errMsg := fmt.Sprintf("Spark job failed: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	err = workflow.ExecuteActivity(ctx, emrController.TerminateCluster, createClusterResponse.ClusterID).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity TerminateCluster failed", "error", err)
		logger.Warn("Failed to terminate EMR cluster, it may need manual cleanup", "cluster_id", createClusterResponse.ClusterID)
	}

	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.AddDestinationSyncRunForFeatureStoreSparkRun, featureStoreSegmentInfo, featureStoreSparkRunDTO.Id).Get(ctx, &featureStoreSegmentInfo)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to insert in destination sync run: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	DESTINATION_SYNC_RUN_DATA_PUSHED_FOR_CONSUMPTION := "DATA_PUSHED_FOR_CONSUMPTION"
	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.MarkLastDestinationSyncRunStatus, featureStoreSegmentInfo, DESTINATION_SYNC_RUN_DATA_PUSHED_FOR_CONSUMPTION).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to mark last destination sync run status: %v", err)
		markFeatureStoreSparkRunFailed(ctx, featureStoreSparkRunDTO, nil, errMsg)
		return "Failure", err
	}

	executionEnd := workflow.Now(ctx)
	featureStoreSparkRunDTO.Status = "SUCCESSFUL"
	featureStoreSparkRunDTO.ExecutionEnd = &executionEnd

	err = workflow.ExecuteActivity(ctx, featureStoreSparkController.UpdateFeatureStoreSparkRun, featureStoreSparkRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity UpdateFeatureStoreSparkRun for status SUCCESSFUL failed", "error", err)
		return "Failure", err
	}

	return fmt.Sprintf("Successfully ran feature store spark with id %d and run_id %s", featureStoreSparkRunDTO.Id, *featureStoreSparkRunDTO.RunID), nil
}
