package workflows

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	attributequeriescontroller "github.com/Zomato/cdp-platform/internal/controllers/attributequeries"
	attributesyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/attributesyncruns"
	configcontroller "github.com/Zomato/cdp-platform/internal/controllers/configcontroller"
	emrcontroller "github.com/Zomato/cdp-platform/internal/controllers/emr"
	"github.com/Zomato/cdp-platform/pkg/emr"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// markSyncAttributeFailed is a helper function to mark an attribute sync run as failed
func markSyncAttributeFailedSpark(ctx workflow.Context, attributeSyncRunDTO *dtos.AttributeSyncRuns, clusterId *string, errMsg string) {
	var attributeSyncRunsController attributesyncrunscontroller.Controller

	attributeSyncRunDTO.Status = "FAILED"

	errorMessage := errMsg
	attributeSyncRunDTO.ErrorMessage = &errorMessage

	executionEnd := workflow.Now(ctx)
	attributeSyncRunDTO.ExecutionEnd = &executionEnd

	err := workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Failed to mark attribute sync as failed", "error", err)
	}

	if clusterId != nil {
		var emrController emrcontroller.Controller
		err = workflow.ExecuteActivity(ctx, emrController.TerminateCluster, *clusterId).Get(ctx, nil)
		if err != nil {
			logger.Error("Failed to terminate EMR cluster", "error", err)
		}
	}
}

// SyncAttributeQueryWorkflowSpark is the workflow function for syncing attribute queries using Spark
func SyncAttributeQueryWorkflowSpark(ctx workflow.Context, attributeQueryID int64) (string, error) {
	logger.Infof("Starting SyncAttributeQueryWorkflowSpark for attributeQueryID: %d", attributeQueryID)

	var attributeSyncRunsController attributesyncrunscontroller.Controller
	var attributeQueryController attributequeriescontroller.Controller
	var configController configcontroller.Controller
	var emrController emrcontroller.Controller

	var attributeQueryText string
	var configMap json.RawMessage

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 120,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	executionStart := workflow.Now(ctx)
	attributeSyncRunDTO := &dtos.AttributeSyncRuns{
		AttributeQueryID: attributeQueryID,
		Status:           "STARTED",
		ExecutionStart:   &executionStart,
	}
	err := workflow.ExecuteActivity(ctx, attributeSyncRunsController.AddAttributeSyncRun, attributeSyncRunDTO).Get(ctx, &attributeSyncRunDTO)
	if err != nil {
		logger.Error("Activity AddAttributeSyncRun failed", "error", err)
		return "Failure", err
	}

	err = workflow.ExecuteActivity(ctx, attributeQueryController.GetAttributeQuery, attributeQueryID).Get(ctx, &attributeQueryText)
	if err != nil {
		logger.Error("Activity GetAttributeQuery failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get attribute query text: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, nil, errMsg)
		return "Failure", err
	}

	err = workflow.ExecuteActivity(ctx, attributeQueryController.GetConfigMapByAttributeQueryID, int(attributeQueryID)).Get(ctx, &configMap)
	if err != nil {
		logger.Error("Activity GetConfigMapByAttributeQueryID failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get config map: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, nil, errMsg)
		return "Failure", err
	}

	// STEP 4: Determine workflow name based on org
	var orgName string
	err = workflow.ExecuteActivity(ctx, configController.GetOrg).Get(ctx, &orgName)
	if err != nil {
		logger.Error("Activity GetEMROrg failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get EMR org from config: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, nil, errMsg)
		return "Failure", err
	}

	// Determine workflow name based on org
	var workflowName string
	if orgName == "zomato" {
		workflowName = "SyncAttributeSparkZomato"
	} else if orgName == "blinkit" {
		workflowName = "SyncAttributeSpark"
	}

	// STEP 5: Create EMR Cluster
	var createClusterResponse dtos.CreateEMRClusterResponse
	err = workflow.ExecuteActivity(ctx, emrController.CreateEMRCluster, &dtos.CreateEMRClusterRequest{
		WorkflowName: workflowName,
	}).Get(ctx, &createClusterResponse)
	if err != nil {
		logger.Error("Activity CreateEMRCluster failed", "error", err)
		errMsg := fmt.Sprintf("Failed to create EMR cluster: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, nil, errMsg)
		return "Failure", err
	}

	// TODO: Remove this table name hardcoding
	tableName := "user_pid_properties"
	var sparkConfig emr.SparkConfig
	err = workflow.ExecuteActivity(ctx, emrController.GetSparkConfig, configMap).Get(ctx, &sparkConfig)
	if err != nil {
		logger.Error("Activity GetSparkConfig failed", "error", err)
		errMsg := fmt.Sprintf("Failed to get Spark config: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	catalog := sparkConfig.Catalog
	schema := sparkConfig.Schema

	var submitJobResponse dtos.SubmitSparkJobResponse
	err = workflow.ExecuteActivity(ctx, emrController.SubmitSparkJob, &dtos.SubmitSparkJobRequest{
		WorkflowName: workflowName,
		ClusterID:    createClusterResponse.ClusterID,
		JobName:      "AttributeSyncSparkJob",
		JarURI:       "s3://blinkit-analytics/config/cdp/attribute_sync_spark.py",
		Args: []string{
			"--query", attributeQueryText,
			"--table_name", tableName,
			"--attribute_query_id", fmt.Sprintf("%d", attributeQueryID),
			"--catalog", catalog,
			"--schema", schema,
		},
		AdditionalJars: []string{
			"s3://blinkit-analytics/artifacts/iceberg/iceberg-spark-runtime-3.3_2.12-1.3.0.jar",
		},
	}).Get(ctx, &submitJobResponse)
	if err != nil {
		logger.Error("Activity SubmitSparkJob failed", "error", err)
		errMsg := fmt.Sprintf("Failed to submit Spark job: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	retryOptions := temporal.RetryPolicy{
		InitialInterval:    time.Second * 10,
		MaximumInterval:    time.Second * 15,
		BackoffCoefficient: 1.5,
		MaximumAttempts:    2,
	}
	retryCtx := workflow.WithRetryPolicy(ctx, retryOptions)

	err = workflow.ExecuteActivity(retryCtx, emrController.WaitForStepCompletion, createClusterResponse.ClusterID, submitJobResponse.StepID, 6*time.Hour).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity WaitForStepCompletion failed", "error", err)
		errMsg := fmt.Sprintf("Spark job failed: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	attributeSyncRunDTO.Status = "INTERM_TABLE_CREATED"
	attributeSyncRunDTO.SyncTableName = &tableName
	err = workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity UpdateAttributeSyncRun for status INTERM_TABLE_CREATED failed", "error", err)
		errMsg := fmt.Sprintf("Failed to update attribute sync run status to INTERM_TABLE_CREATED: %v", err)
		markSyncAttributeFailedSpark(ctx, attributeSyncRunDTO, &createClusterResponse.ClusterID, errMsg)
		return "Failure", err
	}

	err = workflow.ExecuteActivity(ctx, emrController.TerminateCluster, createClusterResponse.ClusterID).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity TerminateCluster failed", "error", err)
		logger.Warn("Failed to terminate EMR cluster, it may need manual cleanup", "cluster_id", createClusterResponse.ClusterID)
	}

	executionEnd := workflow.Now(ctx)
	attributeSyncRunDTO.Status = "SUCCESSFUL"
	attributeSyncRunDTO.ExecutionEnd = &executionEnd

	err = workflow.ExecuteActivity(ctx, attributeSyncRunsController.UpdateAttributeSyncRun, attributeSyncRunDTO).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity UpdateAttributeSyncRun for status SUCCESSFUL failed", "error", err)
		return "Failure", err
	}

	return fmt.Sprintf("Successfully synced attribute query %d to table %s", attributeQueryID, tableName), nil
}
