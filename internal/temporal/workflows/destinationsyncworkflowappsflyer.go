package workflows

import (
	"crypto/sha512"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	appsflyercontroller "github.com/Zomato/cdp-platform/internal/controllers/appsflyer"
	"github.com/Zomato/cdp-platform/internal/controllers/configcontroller"
	destinationsynccontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsync"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	destinationsyncrunspartitioncontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncrunspartition"
	kafkacontroller "github.com/Zomato/cdp-platform/internal/controllers/kafka"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const (
	DESTINATION_SYNC_HIVE_PREFIX     = "destination_sync_"
	DESTINATION_SYNC_RUN_HIVE_PREFIX = "destination_sync_run_"
)

func markDestinationSyncRunFailed(ctx workflow.Context, destinationSyncRun *dtos.DestinationSyncRun, errMsg string) error {
	destinationSyncRun.Status = "FAILED"
	destinationSyncRun.ErrorMessage = &errMsg
	executionEnd := workflow.Now(ctx)
	destinationSyncRun.ExecutionEnd = &executionEnd
	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("[Failure] failed to update destination sync run, %s", err)
	}
	return nil
}

func cleanDestinationSyncOngoingSegmentTableField(ctx workflow.Context, destinationSyncID int64) error {
	var destinationSyncController destinationsynccontroller.Controller
	err := workflow.ExecuteActivity(ctx, destinationSyncController.CleanOngoingSegmentTable, destinationSyncID).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("[Failure] failed to clean destination sync ongoing segment table field, %s", err)
	}
	return nil
}

func DestinationSyncWorkflow(ctx workflow.Context, destinationSyncID int64, syncStrategy string) error {
	logger.Infof("Starting DestinationSyncWorkflow for destination id : %d and syncStrategy : %s", destinationSyncID, syncStrategy)

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 60,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 5,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var configController configcontroller.Controller
	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	var destinationSyncController destinationsynccontroller.Controller
	var starrocksController starrockscontroller.Controller
	var appsflyercontroller appsflyercontroller.Controller
	var kafkacontroller kafkacontroller.Controller
	var destinationSyncRunsPartitionController destinationsyncrunspartitioncontroller.Controller

	var config dtos.Config
	err := workflow.ExecuteActivity(ctx, configController.GetConfig).Get(ctx, &config)
	if err != nil {
		return fmt.Errorf("failed to get config values: %s", err)
	}

	// // Verify last run was successful before proceeding
	// err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.VerifyLastRunTerminalState, destinationSyncID).Get(ctx, nil)
	// if err != nil {
	// 	return fmt.Errorf("[Failure] last run verification failed: %s", err)
	// }

	switch syncStrategy {
	case "INCREMENTAL":
		// Check whether it is First Time Sync for INCREMENTAL FLOW
		var isFirstTimeDestinationSync bool
		err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.IsFirstTimeDestinationSync, destinationSyncID).Get(ctx, &isFirstTimeDestinationSync)
		if err != nil {
			return fmt.Errorf("[Failure] failed to check first time destination sync, %s", err)
		}
		if isFirstTimeDestinationSync {
			syncStrategy = "FULL"
		}
		logger.Info("[Success] Switched to FULL mode as first time sync.")
	}

	var segmentQuery string
	executionStart := workflow.Now(ctx)
	destinationSyncRun := &dtos.DestinationSyncRun{
		DestinationSyncID: destinationSyncID,
		ExecutionStart:    &executionStart,
		Status:            "STARTED",
	}

	// Insert into Desitnation Sync Run to get Id for this destination sync run
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.AddDestinationSyncRun, destinationSyncRun).Get(ctx, &destinationSyncRun.Id)
	if err != nil {
		return fmt.Errorf("[FAILURE] insert destination sync run activity failed, %s", err)
	}

	// Get audience details
	var audienceDetails dtos.AppsflyerAudienceDetails
	err = workflow.ExecuteActivity(ctx, destinationSyncController.GetAppsflyerAudienceDetails, destinationSyncID).Get(ctx, &audienceDetails)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get audience details (import key): %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed to get audience Details (import key) , %s", err)
	}

	// Get segment query -> contains the `is_tg` field
	err = workflow.ExecuteActivity(ctx, destinationSyncController.GetSegmentQueryByDestinationSyncId, destinationSyncID).Get(ctx, &segmentQuery)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get segment query: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed get to segment query, %s", err)
	}

	// Create an hive run table for current segment state using the above segment query
	queryHash := sha512.Sum512([]byte(segmentQuery))
	var currentSegmentTableName string = DESTINATION_SYNC_HIVE_PREFIX + fmt.Sprintf("%x", queryHash[:16]) + "_" + fmt.Sprintf("%d", destinationSyncRun.Id)
	currentSegmentHiveTable := &dtos.CreateHiveTable{
		TableName: currentSegmentTableName,
		Query:     segmentQuery,
	}
	err = workflow.ExecuteActivity(ctx, starrocksController.CreateHiveTableFromQuery, currentSegmentHiveTable).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create hive run table for current segment: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] create hive run table activity failed for current Segment Hive Run Table, %s", err)
	}

	// Update OngoingSegmentTable in Destination Sync to currentSegmentTableName(Hive Table) [STATE CHANGE ACTIVITY - TO BE REVERTED IN CASE OF FAILURE]
	destinationSyncDto := &dtos.DestinationSync{
		Base:                dtos.Base{Id: destinationSyncID},
		OngoingSegmentTable: currentSegmentTableName,
	}
	err = workflow.ExecuteActivity(ctx, destinationSyncController.UpdateDestinationSync, destinationSyncDto).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update OngoingSegmentTable in destination sync: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed to update OngoingSegmentTable in destination sync, %s", err)
	}

	// Calculate Add Remove Hive Table in case of Incremental and Nothing in case of Full
	switch syncStrategy {
	case "INCREMENTAL":
		// Get Commited Segment Table Name [For diff calculation]
		var commitedSegmentTableName string // this will also have `is_tg` field
		err = workflow.ExecuteActivity(ctx, destinationSyncController.GetCommittedSegmentTableName, destinationSyncID).Get(ctx, &commitedSegmentTableName)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get committed segment table name: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			return fmt.Errorf("[FAILURE] Failed to get committed segment table name, %s", err)
		}

		// Get Incremental Query
		var incrementalQuery string // this will have `is_tg` field but only value true
		err = workflow.ExecuteActivity(ctx, destinationSyncController.GetIncrementalQueryByDestinationSyncId, destinationSyncID, currentSegmentTableName, commitedSegmentTableName).Get(ctx, &incrementalQuery)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get incremental query: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			return fmt.Errorf("[FAILURE] failed get to incrementalQuery query, %s", err)
		}

		// Create the incremental hive table using the Incremental Query
		incrementalQueryHash := sha512.Sum512([]byte(incrementalQuery))
		var incrementalTable string = DESTINATION_SYNC_RUN_HIVE_PREFIX + fmt.Sprintf("%x", incrementalQueryHash[:16]) + "_" + fmt.Sprintf("%d", destinationSyncRun.Id)
		incrementalHiveTable := dtos.CreateHiveTable{
			TableName: incrementalTable,
			Query:     incrementalQuery,
		}
		err = workflow.ExecuteActivity(ctx, starrocksController.CreateHiveTableFromQuery, incrementalHiveTable).Get(ctx, nil)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to create incremental hive table: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			return fmt.Errorf("[FAILURE] create hive run table activity failed for Incremental Hive Run Table, %s", err)
		}

		destinationSyncRun.SyncTableName = &incrementalTable // In case of INCREMENTAL: SyncTableName is the incrementalTable as incremental state is synced in case of FULL
	case "FULL":
		destinationSyncRun.SyncTableName = &currentSegmentTableName // In case of FULL: SyncTableName is the currentSegmentTableName as complete current state is synced in case of FULL
	}

	// Update to INTERMEDIATE status and syncTableName in Destination Sync Runs Table
	destinationSyncRun.Status = "INTERM_TABLE_CREATED"
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update destination sync run: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		return fmt.Errorf("[Failure] failed to update destination sync run, %s", err)
	}

	switch syncStrategy {
	case "INCREMENTAL":

		// [ADD OPERATION] Querying Hive Run Table + Transformation Rows + Batching + Sync to Kafka
		addOperationHiveRunQuery := fmt.Sprintf("select advertising_id, phone, email, phone_plus_91, trx_recent_device_platform from %s.%s.%s where status = 1 and is_tg = true", config.StarrocksHiveCatalog, config.StarrocksHiveDatabase, *destinationSyncRun.SyncTableName)
		addAppsflyerOperation := "ADD"
		err = workflow.ExecuteActivity(ctx, appsflyercontroller.SyncDataToAppsflyer, addOperationHiveRunQuery, audienceDetails, addAppsflyerOperation).Get(ctx, nil)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to sync ADD operation data to Appsflyer: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			logger.Errorf("[Failure] SYNC DATA TO APPSFLYER KAKFA HAS FAILED FOR ADD")
			return err
		}

		// [REMOVE OPERATION] Querying Hive Run Table + Transformation Rows + Batching + Sync to Kafka
		removeOperationHiveRunQuery := fmt.Sprintf("select advertising_id, phone, email, phone_plus_91, trx_recent_device_platform from %s.%s.%s where status = 0 and is_tg = true", config.StarrocksHiveCatalog, config.StarrocksHiveDatabase, *destinationSyncRun.SyncTableName)
		removeAppsflyerOperation := "REMOVE"
		err = workflow.ExecuteActivity(ctx, appsflyercontroller.SyncDataToAppsflyer, removeOperationHiveRunQuery, audienceDetails, removeAppsflyerOperation).Get(ctx, nil)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to sync REMOVE operation data to Appsflyer: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			logger.Errorf("[Failure] SYNC DATA TO APPSFLYER KAKFA HAS FAILED FOR REMOVE")
			return err
		}

	case "FULL":

		// [ADD OPERATION] Querying Hive Run Table + Transformation Rows + Batching + Sync to Kafka
		addOperationHiveRunQuery := fmt.Sprintf("select advertising_id, phone, email, phone_plus_91, trx_recent_device_platform from %s.%s.%s WHERE is_tg = true", config.StarrocksHiveCatalog, config.StarrocksHiveDatabase, *destinationSyncRun.SyncTableName)
		addAppsflyerOperation := "ADD"
		err = workflow.ExecuteActivity(ctx, appsflyercontroller.SyncDataToAppsflyer, addOperationHiveRunQuery, audienceDetails, addAppsflyerOperation).Get(ctx, nil)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to sync FULL mode data to Appsflyer: %v", err)
			markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
			cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
			logger.Errorf("[Failure] SYNC DATA TO APPSFLYER KAKFA HAS FAILED FOR ADD")
			return err
		}

	}

	// STEP AFTER APPSLYER SYNC | DUMMY MSG LOGIC ------
	// Get all partitions
	var partitions []int32
	err = workflow.ExecuteActivity(ctx, kafkacontroller.GetAllPartitions, config.AppsflyerKafkaTopic).Get(ctx, &partitions)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to fetch Kafka partitions: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		logger.Errorf("[Failure] ERROR FETCHING ALL PARTITIONS")
		return err
	}
	logger.Info("[Success] Fetching All Partitions completed", partitions)

	// Insert all the partitions into destination sync partition table
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsPartitionController.InsertDestinationSyncPartitionStatusAll, destinationSyncRun.Id, partitions).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to insert destination sync partition status: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		logger.Errorf("[Failure] Inserted into destination sync partition table")
		return err
	}
	logger.Info("[Success] Inserted into destination sync partition table ")

	// Send dummy msg to all partitions
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsPartitionController.SendDummyMessageAllPartitions, destinationSyncRun.Id, config.AppsflyerKafkaTopic, partitions).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to send dummy messages to partitions: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		logger.Errorf("[Failure] Dummy Msg push falied")
		return err
	}
	logger.Info("[Success] Dummy Msg push success ")

	// Update to INTERMEDIATE status and syncTableName in Destination Sync Runs Table
	destinationSyncRun.Status = "DATA_PUSHED_FOR_CONSUMPTION"
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update destination sync run status to DATA_PUSHED_FOR_CONSUMPTION: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		return fmt.Errorf("[Failure] failed to update destination sync run, %s", err)
	}
	logger.Info("[Success] Destination Sync Runs Table update success ")

	return nil
}
