package workflows

import (
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	"go.temporal.io/sdk/workflow"
)

func TestTemporalWorkflow(ctx workflow.Context, dataSource *dtos.DataSource) (*dtos.DataSource, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting TestTemporalWorkflow", "input", dataSource)

	// Define Activity options
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 1,
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	var result *dtos.DataSource
	// err := workflow.ExecuteActivity(ctx, "AddDataSource", dataSource).Get(ctx, &result) // WORKS
	var datasourcecontroller datasourcecontroller.Controller
	err := workflow.ExecuteActivity(ctx, datasourcecontroller.AddDataSource, dataSource).Get(ctx, &result) // ALSO WORKS (More type safe)
	if err != nil {
		logger.Error("Activity failed", "error", err)
		return nil, err
	}

	logger.Info("TestTemporalWorkflow completed", "result", result)
	return result, err
}
