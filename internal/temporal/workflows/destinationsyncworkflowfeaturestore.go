package workflows

import (
	"crypto/sha512"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	destinationsynccontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsync"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	featurestorecontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestore"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const (
	DESTINATION_SYNC_PREFIX_FS = "destination_sync_fs"
)

func DestinationSyncWorkflowFeatureStore(ctx workflow.Context, destinationSyncID int64) error {
	logger.Infof("Starting DestinationSyncWorkflowFeatureStore for destination id : %d", destinationSyncID)

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 60,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 5,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	var destinationSyncController destinationsynccontroller.Controller
	var starrocksController starrockscontroller.Controller
	var featureStoreController featurestorecontroller.Controller
	var segmentQuery string

	// Create a new destination sync run
	executionStart := workflow.Now(ctx)
	destinationSyncRun := &dtos.DestinationSyncRun{
		DestinationSyncID: destinationSyncID,
		ExecutionStart:    &executionStart,
		Status:            "STARTED",
	}
	// Insert into Desitnation Sync Run to get Id for this destination sync run
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.AddDestinationSyncRun, destinationSyncRun).Get(ctx, &destinationSyncRun.Id)
	if err != nil {
		return fmt.Errorf("[FAILURE] insert destination sync run activity failed, %s", err)
	}

	// Get the feature name for this destination sync
	var featureName string
	err = workflow.ExecuteActivity(ctx, featureStoreController.GetFeatureNameByDestinationSyncID, destinationSyncID).Get(ctx, &featureName)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get feature name: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed to get feature name, %s", err)
	}

	// Get segment query -> contains the `is_tg` field
	err = workflow.ExecuteActivity(ctx, destinationSyncController.GetSegmentQueryByDestinationSyncId, destinationSyncID).Get(ctx, &segmentQuery)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get segment query: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed get to segment query, %s", err)
	}

	// Modify the segment query to only select user_id and add a boolean feature column
	modifiedSegmentQuery := fmt.Sprintf("SELECT user_id, true as %s FROM (%s) as segment_users", featureName, segmentQuery)

	// Create an iceberg table for current segment state using the modified segment query
	queryHash := sha512.Sum512([]byte(modifiedSegmentQuery))
	hashStr := fmt.Sprintf("%x", queryHash[:])
	truncatedHash := hashStr[:20]
	var currentSegmentTableName string = DESTINATION_SYNC_PREFIX_FS + "_" + featureName + "_" + truncatedHash + "_" + fmt.Sprintf("%d", destinationSyncRun.Id)

	// Update OngoingSegmentTable in Destination Sync to currentSegmentTableName [STATE CHANGE ACTIVITY - TO BE REVERTED IN CASE OF FAILURE]
	destinationSyncDto := &dtos.DestinationSync{
		Base:                dtos.Base{Id: destinationSyncID},
		OngoingSegmentTable: currentSegmentTableName,
	}
	err = workflow.ExecuteActivity(ctx, destinationSyncController.UpdateDestinationSync, destinationSyncDto).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update OngoingSegmentTable in destination sync: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		return fmt.Errorf("[FAILURE] failed to update OngoingSegmentTable in destination sync, %s", err)
	}

	createIcebergTableDTO := &dtos.CreateIcebergTable{
		TableName: currentSegmentTableName,
		Query:     modifiedSegmentQuery,
	}
	err = workflow.ExecuteActivity(ctx, starrocksController.CreateIcebergTable, createIcebergTableDTO).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create iceberg table for current segment: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		return fmt.Errorf("[FAILURE] create iceberg table activity failed for current Segment, %s", err)
	}

	// Update committed segment table and clean ongoing segment table in Destination Sync
	destinationSyncCommitDto := &dtos.DestinationSync{
		Base:                  dtos.Base{Id: destinationSyncID},
		CommittedSegmentTable: currentSegmentTableName,
		OngoingSegmentTable:   "",
	}
	err = workflow.ExecuteActivity(ctx, destinationSyncController.UpdateDestinationSync, destinationSyncCommitDto).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update CommitedSegmentTable in destination sync: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		return fmt.Errorf("[FAILURE] failed to update CommitedSegmentTable in destination sync, %s", err)
	}

	// Now update the Destination Sync Run with INTERM_TABLE_CREATED status and the table name
	destinationSyncRun.Status = "INTERM_TABLE_CREATED"
	destinationSyncRun.SyncTableName = &currentSegmentTableName
	executionEnd := workflow.Now(ctx)
	destinationSyncRun.ExecutionEnd = &executionEnd
	err = workflow.ExecuteActivity(ctx, destinationSyncRunsController.UpdateDestinationSyncRun, destinationSyncRun).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to update destination sync run status to INTERM_TABLE_CREATED: %v", err)
		markDestinationSyncRunFailed(ctx, destinationSyncRun, errMsg)
		cleanDestinationSyncOngoingSegmentTableField(ctx, destinationSyncID)
		return fmt.Errorf("[FAILURE] failed to update destination sync run, %s", err)
	}

	return nil
}
