package workflows

import (
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	featurestorecontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestore"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	logger "github.com/Zomato/go/logger"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const (
	FEATURE_STORE_LOG_TABLE_PREFIX = "fs_log"
)

func markFeatureStoreLogTableProcessingFailed(ctx workflow.Context, destinationSyncRunID int64, errMsg string) {
	logger.Errorf("FeatureStoreLogTableProcessingWorkflow failed for run ID %d: %s", destinationSyncRunID, errMsg)
}

// FeatureStoreLogTableProcessingWorkflow processes successful feature store runs
// and creates corresponding log tables with dt/hr partitioning
func FeatureStoreLogTableProcessingWorkflow(ctx workflow.Context, destinationSyncRunID int64) error {
	logger.Infof("Starting FeatureStoreLogTableProcessingWorkflow for destination sync run ID: %d", destinationSyncRunID)

	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Hour * 2, // Heavy operations need longer timeout
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	var destinationSyncRunsController destinationsyncrunscontroller.Controller
	var featureStoreController featurestorecontroller.Controller
	var starrocksController starrockscontroller.Controller

	// STEP 1: Get the destination sync run details
	var destinationSyncRun *dtos.DestinationSyncRun
	err := workflow.ExecuteActivity(ctx, destinationSyncRunsController.GetDestinationSyncRunByID, destinationSyncRunID).Get(ctx, &destinationSyncRun)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get destination sync run: %v", err)
		markFeatureStoreLogTableProcessingFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("[FAILURE] %s", errMsg)
	}

	// STEP 2: Validate sync table name exists
	if destinationSyncRun.SyncTableName == nil || *destinationSyncRun.SyncTableName == "" {
		errMsg := fmt.Sprintf("sync_table_name is empty for run ID: %d", destinationSyncRunID)
		markFeatureStoreLogTableProcessingFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("[FAILURE] %s", errMsg)
	}
	icebergTableName := *destinationSyncRun.SyncTableName

	// STEP 3: Get feature name from destination sync
	var featureName string
	err = workflow.ExecuteActivity(ctx, featureStoreController.GetFeatureNameByDestinationSyncID, destinationSyncRun.DestinationSyncID).Get(ctx, &featureName)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get feature name: %v", err)
		markFeatureStoreLogTableProcessingFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("[FAILURE] %s", errMsg)
	}

	// STEP 4: Validate execution start time
	if destinationSyncRun.ExecutionStart == nil {
		errMsg := fmt.Sprintf("execution_start is null for run ID: %d", destinationSyncRunID)
		markFeatureStoreLogTableProcessingFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("[FAILURE] %s", errMsg)
	}

	// STEP 5: Extract dt and hr from execution_start
	dt := destinationSyncRun.ExecutionStart.Format("2006-01-02")
	hr := destinationSyncRun.ExecutionStart.Format("15")

	// STEP 6: Generate log table name following pattern fs_log_{feature_name}
	cleanFeatureName := strings.ReplaceAll(featureName, " ", "_")
	cleanFeatureName = strings.ToLower(cleanFeatureName)
	logTableName := fmt.Sprintf("%s_%s", FEATURE_STORE_LOG_TABLE_PREFIX, cleanFeatureName)

	// Ensure table name is not too long (StarRocks limit)
	if len(logTableName) > 50 {
		logTableName = logTableName[:50]
	}

	// STEP 7: Create log table and insert data in single operation
	createLogTableRequest := &dtos.CreateFeatureStoreLogTable{
		SourceIcebergTable: icebergTableName,
		LogTableName:       logTableName,
		FeatureName:        featureName,
		PartitionDt:        dt,
		PartitionHr:        hr,
	}
	err = workflow.ExecuteActivity(ctx, starrocksController.CreateFeatureStoreLogTable, createLogTableRequest).Get(ctx, nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create and populate log table: %v", err)
		markFeatureStoreLogTableProcessingFailed(ctx, destinationSyncRunID, errMsg)
		return fmt.Errorf("[FAILURE] %s", errMsg)
	}

	// STEP 8: Update feature_store_segments table with log table name
	err = workflow.ExecuteActivity(ctx, featureStoreController.UpdateLogTableName, destinationSyncRun.DestinationSyncID, logTableName).Get(ctx, nil)
	if err != nil {
		// Log warning but don't fail the workflow since table creation was successful
		logger.Warn("Failed to update log table name in feature_store_segments", "error", err, "runID", destinationSyncRunID)
	}

	logger.Infof("Successfully completed FeatureStoreLogTableProcessingWorkflow for run ID: %d, table: %s, partition: dt=%s/hr=%s",
		destinationSyncRunID, logTableName, dt, hr)

	return nil
}
