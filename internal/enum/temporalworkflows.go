package enum

type WorkflowType string

const (
	SyncAttributeQueryWorkflow             WorkflowType = "SyncAttributeQueryWorkflow"
	DestinationSyncWorkflow                WorkflowType = "DestinationSyncWorkflow"
	DestinationSyncCompletedWorkflow       WorkflowType = "DestinationSyncCompletedWorkflow"
	StarrocksDatabaseBackupWorkflow        WorkflowType = "StarrocksDatabaseBackupWorkflow"
	PersonasWorkflow                       WorkflowType = "PersonasWorkflow"
	SyncAttributeQueryWorkflowSpark        WorkflowType = "SyncAttributeQueryWorkflowSpark"
	DestinationSyncWorkflowFeatureStore    WorkflowType = "DestinationSyncWorkflowFeatureStore"
	FeatureStoreSparkWorkflow              WorkflowType = "FeatureStoreSparkWorkflow"
	FeatureStoreStatusSchedulerWorkflow    WorkflowType = "FeatureStoreStatusSchedulerWorkflow"
	FeatureStoreLogTableProcessingWorkflow WorkflowType = "FeatureStoreLogTableProcessingWorkflow"
	AttributeQueriesCleanupWorkflow        WorkflowType = "AttributeQueriesCleanupWorkflow"
)
