package middleware

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"time"

	auth "github.com/Zomato/cdp-platform/internal/auth"
	HttpStatus "github.com/Zomato/cdp-platform/utils/http"

	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
)

func CheckLoggedIn() gin.HandlerFunc {
	return func(c *gin.Context) {
		w := c.Writer
		r := c.Request

		/* Layer 1 of Authentication - User Claims */
		claims, err := auth.ValidateTokenRequest(c)
		if err != nil {
			log.Info("Unable to validate token", err)

			// Try one more time with refresh token directly
			refreshRequest, refreshErr := r.<PERSON>ie("refresh_token")
			if refreshErr == nil {
				refreshRequestStr := refreshRequest.Value
				refreshClaims, refreshErr := auth.ValidateToken(refreshRequestStr)
				if refreshErr == nil {
					// Safely extract email from refresh claims
					var email string
					if emailClaim, exists := refreshClaims["email"]; exists {
						if emailStr, ok := emailClaim.(string); ok {
							email = emailStr
						}
					}

					// Generate new access token
					updatedAccessTokenString, refreshErr := auth.RefreshJWT(refreshClaims)
					if refreshErr == nil {
						// Set new cookies
						updatedSessionToken := auth.GenerateSessionToken(updatedAccessTokenString)
						auth.SetSessionCookie(c, updatedSessionToken, email, updatedAccessTokenString, refreshRequestStr, false)

						// Update claims and continue
						claims = refreshClaims
						err = nil
					}
				}
			}

			// If we still have an error after trying to refresh, return unauthorized
			if err != nil {
				HttpStatus.StatusUnauthorized(w, r, err)
				c.Abort()
				return
			}
		}

		// Check if user is allowed - gracefully handle missing is_allowed field
		isAllowed := false
		if isAllowedClaim, exists := claims["is_allowed"]; exists {
			if allowedBool, ok := isAllowedClaim.(bool); ok {
				isAllowed = allowedBool
			}
		}

		if !isAllowed {
			// Safely extract email for logging
			var emailForLog interface{} = "unknown"
			if emailClaim, exists := claims["email"]; exists {
				emailForLog = emailClaim
			}
			log.Info("User is not allowed to access the system", emailForLog)
			HttpStatus.StatusUnauthorized(w, r, errors.New("User is not allowed to access the system"))
			c.Abort()
			return
		}

		// Safely extract email from claims
		var email string
		if emailClaim, exists := claims["email"]; exists {
			if emailStr, ok := emailClaim.(string); ok {
				email = strings.ToLower(emailStr)
			}
		}

		// Safely extract user_id from claims
		var userID int64
		if userIDClaim, exists := claims["user_id"]; exists {
			if userIDFloat, ok := userIDClaim.(float64); ok {
				userID = int64(userIDFloat)
			}
		}

		// Set context values only if they exist
		ctx := r.Context()
		if email != "" {
			ctx = context.WithValue(ctx, "email", email)
		}
		ctx = context.WithValue(ctx, "is_allowed", isAllowed)
		if userID != 0 {
			ctx = context.WithValue(ctx, "user_id", userID)
		}
		c.Request = c.Request.WithContext(ctx)

		// Set gin context values only if they exist
		if email != "" {
			c.Set("email", email)
		}
		c.Set("is_allowed", isAllowed)
		if userID != 0 {
			c.Set("user_id", userID)
		}

		/* Layer 2 of Authentication - Session Token Validation */
		sessionCookie, err := r.Cookie("session_token")
		if err != nil {
			if err == http.ErrNoCookie {
				log.Info("No session cookie found, generating a new one", email)

				// Get the access token to generate a session token
				accessTokenCookie, accessErr := r.Cookie("access_token")
				if accessErr == nil {
					// We have an access token, use it to generate a session token
					accessToken := accessTokenCookie.Value
					sessionToken := auth.GenerateSessionToken(accessToken)

					// Set the session cookie
					http.SetCookie(w, &http.Cookie{
						Name:     "session_token",
						Value:    sessionToken,
						Expires:  time.Now().Add(auth.ACCESS_TOKEN_EXPIRY),
						Path:     "/",
						HttpOnly: true,
						Secure:   true,
						SameSite: http.SameSiteLaxMode,
					})

					// Continue with the request since we've created a valid session
					c.Next()
					return
				} else {
					// No access token either, try with refresh token
					refreshTokenCookie, refreshErr := r.Cookie("refresh_token")
					if refreshErr == nil {
						refreshToken := refreshTokenCookie.Value
						refreshClaims, refreshErr := auth.ValidateToken(refreshToken)

						if refreshErr == nil {
							// Generate new access token from refresh token
							newAccessToken, refreshErr := auth.RefreshJWT(refreshClaims)
							if refreshErr == nil {
								// Generate session token from new access token
								sessionToken := auth.GenerateSessionToken(newAccessToken)

								// Set all cookies
								auth.SetSessionCookie(c, sessionToken, email, newAccessToken, refreshToken, false)

								// Continue with the request
								c.Next()
								return
							}
						}
					}
				}

				// If we get here, we couldn't create a session
				log.Info("Could not generate a session token", email)
				HttpStatus.StatusUnauthorized(w, r, errors.New("Session token required"))
				c.Abort()
				return
			}

			// For any other type of error, return a bad request status
			log.Info("Unable to parse session cookie", err)
			HttpStatus.StatusBadRequest(w, r, err)
			c.Abort()
			return
		}

		// We have a session token, validate it
		sessionToken := sessionCookie.Value
		log.Info("Session Token for user received", sessionToken, email)
		sessionValid := auth.ValidateSession(c, sessionToken, email)

		if !sessionValid {
			log.Info("Invalid session token, attempting to regenerate", email)

			// Try to regenerate the session using the access token
			accessTokenCookie, accessErr := r.Cookie("access_token")
			if accessErr == nil {
				accessToken := accessTokenCookie.Value
				newSessionToken := auth.GenerateSessionToken(accessToken)

				// Set the new session cookie
				http.SetCookie(w, &http.Cookie{
					Name:     "session_token",
					Value:    newSessionToken,
					Expires:  time.Now().Add(auth.ACCESS_TOKEN_EXPIRY),
					Path:     "/",
					HttpOnly: true,
					Secure:   true,
					SameSite: http.SameSiteLaxMode,
				})

				// Continue with the request
				c.Next()
				return
			}

			// Try with refresh token as a last resort
			refreshTokenCookie, refreshErr := r.Cookie("refresh_token")
			if refreshErr == nil {
				refreshToken := refreshTokenCookie.Value
				refreshClaims, refreshErr := auth.ValidateToken(refreshToken)

				if refreshErr == nil {
					// Generate new access token from refresh token
					newAccessToken, refreshErr := auth.RefreshJWT(refreshClaims)
					if refreshErr == nil {
						// Generate session token from new access token
						sessionToken := auth.GenerateSessionToken(newAccessToken)

						// Set all cookies
						auth.SetSessionCookie(c, sessionToken, email, newAccessToken, refreshToken, false)

						// Continue with the request
						c.Next()
						return
					}
				}
			}

			// If we can't regenerate the session, clear the cookie and return unauthorized
			http.SetCookie(w, &http.Cookie{
				Name:     "session_token",
				Value:    "",
				Expires:  time.Now(),
				HttpOnly: true,
				Secure:   true,
				Path:     "/",
			})
			HttpStatus.StatusUnauthorized(w, r, errors.New("Invalid session, please login again"))
			c.Abort()
			return
		}

		c.Next()
	}
}
