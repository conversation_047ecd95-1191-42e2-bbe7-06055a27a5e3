package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/go/logger"
)

type AppsflyerHandler struct {
	controllerManager *manager.Manager
	maxRetries        int
	retryDelay        time.Duration
}

func NewAppsflyerHandler(controllerManager *manager.Manager) ConsumerHandler {
	return &AppsflyerHandler{
		controllerManager: controllerManager,
		maxRetries:        3,
		retryDelay:        time.Second * 5,
	}
}

func (h *AppsflyerHandler) HandleMessage(ctx context.Context, consumedMessage *kafka.ConsumedMessage) error {
	if consumedMessage == nil || consumedMessage.Value == nil {
		logger.Warn("Received nil message or message value, skipping processing")
		return nil // Return nil to continue processing next messages
	}

	// Create a context with timeout
	processCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()

	var err error
	for attempt := 0; attempt <= h.maxRetries; attempt++ {
		if attempt > 0 {
			logger.Infof("Retry attempt %d/%d for message: %s", attempt, h.maxRetries, consumedMessage.Key)
			time.Sleep(h.retryDelay * time.Duration(attempt)) // Exponential backoff
		}

		err = h.processMessage(processCtx, consumedMessage)
		if err == nil {
			return nil // Success, continue processing next messages
		}

		// Check if context is cancelled or deadline exceeded
		if processCtx.Err() != nil {
			logger.Errorf("Context error during processing: %v", processCtx.Err())
			break
		}

		logger.Errorf("Error processing message (attempt %d/%d): %v", attempt+1, h.maxRetries+1, err)
	}

	if err != nil {
		h.handleFailedMessage(ctx, consumedMessage, err)
	}

	return nil // Always return nil so consumer doesn't stop
}

func (h *AppsflyerHandler) processMessage(ctx context.Context, consumedMessage *kafka.ConsumedMessage) error {
	var rawMap map[string]interface{}
	if err := json.Unmarshal(consumedMessage.Value, &rawMap); err != nil {
		return fmt.Errorf("failed to unmarshal message into map: %w", err)
	}

	if _, hasDestinationSyncRunID := rawMap["destination_sync_run_id"]; hasDestinationSyncRunID {
		return h.handleSyncRunMessage(ctx, consumedMessage.Value)
	} else if _, hasImportKey := rawMap["import_key"]; hasImportKey {
		return h.handleAudienceRequestMessage(ctx, consumedMessage.Value)
	}

	return fmt.Errorf("unknown message format: neither destination sync run partition msg nor appsflyer audience API msg")
}

func (h *AppsflyerHandler) handleFailedMessage(ctx context.Context, message *kafka.ConsumedMessage, processingErr error) {
	logger.Errorf("Message processing failed after %d retries: %v", h.maxRetries, processingErr)
	logger.Errorf("Failed message key: %s", message.Key)

	var originalMessageJSON interface{}
	if err := json.Unmarshal(message.Value, &originalMessageJSON); err != nil {
		logger.Errorf("Failed to unmarshal original message as JSON: %v", err)
		originalMessageJSON = string(message.Value) // fallback to string if not valid JSON
	}

	// Create DLQ payload with original message as JSON
	dlqPayload := struct {
		OriginalMessage interface{} `json:"original_message"`
		OriginalTopic   string      `json:"original_topic"`
		ErrorMessage    string      `json:"error_message"`
		RetryCount      int         `json:"retry_count"`
		FailedAt        time.Time   `json:"failed_at"`
	}{
		OriginalMessage: originalMessageJSON,
		OriginalTopic:   message.Topic,
		ErrorMessage:    processingErr.Error(),
		RetryCount:      h.maxRetries,
		FailedAt:        time.Now().UTC(),
	}

	messageKey := string(message.Key)
	dlqTopic := h.controllerManager.BaseController.AppsflyerDLQTopic

	if err := h.controllerManager.BaseController.KafkaProducer.SendMessage(messageKey, &dlqPayload, dlqTopic); err != nil {
		logger.Errorf("Failed to send message to DLQ: %v", err)
	} else {
		logger.Infof("Successfully sent failed message to DLQ topic. Key: %s", messageKey)
	}
}

func (h *AppsflyerHandler) handleSyncRunMessage(ctx context.Context, messageValue []byte) error {
	var syncRunMessage struct {
		DestinationSyncRunID int64 `json:"destination_sync_run_id"`
		Partition            int   `json:"partition"`
		Ts                   int64 `json:"ts"`
	}

	if err := json.Unmarshal(messageValue, &syncRunMessage); err != nil {
		return fmt.Errorf("failed to unmarshal sync run message: %w", err)
	}

	if err := h.processSyncRun(ctx, syncRunMessage.DestinationSyncRunID, syncRunMessage.Partition, syncRunMessage.Ts); err != nil {
		return fmt.Errorf("failed to process sync run: %w", err)
	}

	return nil
}

func (h *AppsflyerHandler) processSyncRun(ctx context.Context, destinationSyncRunID int64, partition int, timestamp int64) error {
	// STEP 1: Mark the current partition as COMPLETED
	destinationSyncPartitionStatusDTO := &dtos.DestinationSyncPartitionStatus{
		DestinationSyncRunID: destinationSyncRunID,
		PartitionNumber:      int32(partition),
		Status:               "COMPLETED",
	}
	if err := h.controllerManager.DestinationSyncRunsPartitionController.UpdateDestinationSyncPartitionStatus(ctx, destinationSyncPartitionStatusDTO); err != nil {
		return fmt.Errorf("failed to update destination sync partition status table: %w", err)
	}

	// STEP 2: Check if all paritions are complete
	allPartitionConsumed, err := h.controllerManager.DestinationSyncRunsPartitionController.CheckAllPartitionDone(ctx, destinationSyncPartitionStatusDTO)
	if err != nil {
		logger.Errorf("Failed to check if all partitions are completed for sync run ID: %d, error: %v", destinationSyncRunID, err)
		return fmt.Errorf("failed to check all partition done: %w", err)
	}

	// STEP 3: if allPartitionConsumed then trigger the completed workflow
	if allPartitionConsumed {
		triggerWorkflowInputDTO := dtos.TriggerWorkflowInput{
			WorkflowType: enum.DestinationSyncCompletedWorkflow,
			Args:         []interface{}{destinationSyncRunID},
		}
		err := h.controllerManager.TemporalController.TriggerWorkflow(ctx, triggerWorkflowInputDTO)
		if err != nil {
			return fmt.Errorf("failed to trigger destination sync run complete workflow: %w", err)
		}
	}

	return nil
}

func (h *AppsflyerHandler) handleAudienceRequestMessage(ctx context.Context, messageValue []byte) error {
	var request appsflyer.AudienceRequest
	if err := json.Unmarshal(messageValue, &request); err != nil {
		return fmt.Errorf("failed to unmarshal audience request message: %w", err)
	}

	if err := h.validateRequest(&request); err != nil {
		return fmt.Errorf("invalid audience request: %w", err)
	}

	operation := strings.ToLower(request.Operation)
	if operation == "" {
		operation = "add"
	}

	if err := h.controllerManager.BaseController.AppsflyerClient.ProcessAudienceRequest(ctx, &request, operation); err != nil {
		return fmt.Errorf("failed to process audience request: %w", err)
	}

	return nil
}

func (h *AppsflyerHandler) validateRequest(request *appsflyer.AudienceRequest) error {
	if request == nil {
		return fmt.Errorf("request is nil")
	}
	if request.ImportKey == "" {
		return fmt.Errorf("import key is missing")
	}
	if request.Platform == "" {
		return fmt.Errorf("platform is missing")
	}
	if len(request.Devices) == 0 {
		return fmt.Errorf("devices array is empty")
	}
	return nil
}
