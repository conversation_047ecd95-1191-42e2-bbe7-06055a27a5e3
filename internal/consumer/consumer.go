package consumer

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"
	"github.com/Zomato/cdp-platform/internal/consumer/handler"
	"github.com/Zomato/cdp-platform/internal/env"
	"github.com/Zomato/cdp-platform/internal/manager"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/go/config"
)

type ConsumerType string

const (
	AppsflyerConsumer ConsumerType = "appsflyer_consumer"
)

func SetUpConsumer(ctx context.Context, appMode string) error {
	consumerType, err := ValidateConsumerType(appMode)
	if err != nil {
		return err
	}
	kafkaConfig := &kafka.KafkaConfig{
		Brokers:          config.GetStringSlice(ctx, "kafka.brokers"),
		SecurityProtocol: config.GetString(ctx, "kafka.security.protocol"),
		SaslMechanism:    config.GetString(ctx, "kafka.security.sasl.mechanism"),
		SaslUsername:     config.GetString(ctx, "kafka.security.sasl.username"),
		SaslPassword:     config.GetString(ctx, "kafka.security.sasl.password"),
	}

	consumerHandler, err := getConsumerHandler(ctx, consumerType)
	if err != nil {
		return err
	}

	topic := config.GetString(ctx, string(consumerType)+".kafka_topic")
	consumerGroup := config.GetString(ctx, string(consumerType)+".consumer_group")

	kafkaConsumer, err := kafka.NewConsumer(
		kafkaConfig,
		consumerGroup,
		topic,
		sarama.OffsetOldest,
		consumerHandler.HandleMessage,
	)
	if err != nil {
		return err
	}
	defer kafkaConsumer.Close()

	err = kafkaConsumer.Start(ctx)
	if err != nil {
		return err
	}
	return nil
}

func getConsumerHandler(ctx context.Context, consumerType ConsumerType) (handler.ConsumerHandler, error) {
	environment, err := env.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unable to get environment from context")
	}
	postgresClient := environment.PostgresClient()
	starrocksClient := environment.StarRocksClient()
	temporalClient := environment.TemporalClient()
	kafkaProducer := environment.KafkaProducer()
	appsflyerClient := environment.AppsflyerClient()
	emrClient := environment.EMRClient()
	featureStoreClient := environment.FeatureStoreClient()
	controllerManager := manager.NewManager(ctx, postgresClient, starrocksClient, temporalClient, kafkaProducer, appsflyerClient, emrClient, featureStoreClient, nil)

	switch consumerType {
	case AppsflyerConsumer:
		appsflyerConsumerHandler := handler.NewAppsflyerHandler(controllerManager)
		return appsflyerConsumerHandler, nil
	default:
		return nil, fmt.Errorf("unsupported consumer type: %s", consumerType)
	}
}

func ValidateConsumerType(consumerType string) (ConsumerType, error) {
	switch ConsumerType(consumerType) {
	case AppsflyerConsumer:
		return ConsumerType(consumerType), nil
	default:
		return "", fmt.Errorf("invalid consumer type: %s", consumerType)
	}
}
