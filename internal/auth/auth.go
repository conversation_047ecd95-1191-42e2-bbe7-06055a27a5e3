package auth

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"crypto/rand"

	"github.com/Zomato/cdp-platform/entities/postgres"
	log "github.com/Zomato/go/logger"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
)

// Constants for token expiration
const (
	ACCESS_TOKEN_EXPIRY  = 15 * time.Minute // 15 minutes
	REFRESH_TOKEN_EXPIRY = 24 * time.Hour   // 1 day
	AUTO_REFRESH_WINDOW  = 5 * time.Minute  // 5 minutes before expiry
)

var secretKey []byte

func SetSigningKey(secret string) {
	secretKey = []byte(secret)
}

/* Return a JWT Pair of AccessToken & RefreshToken */
func GetJWTPair(user *postgres.User) (string, string, error) {
	/* Access Token Generation */
	accessToken := jwt.New(jwt.SigningMethodHS256)
	accessClaims := accessToken.Claims.(jwt.MapClaims)

	accessClaims["authorized"] = true
	accessClaims["email"] = user.Email
	accessClaims["user_id"] = user.Id
	accessClaims["is_allowed"] = user.IsAllowed
	accessClaims["iat"] = time.Now().Unix() // Issued at time
	accessClaims["exp"] = time.Now().Add(ACCESS_TOKEN_EXPIRY).Unix()

	accessTokenString, err := accessToken.SignedString(secretKey)
	if err != nil {
		log.Info("Unable to generate the access token", err)
		return "", "", err
	}

	/* Refresh Token Generation */
	refreshToken := jwt.New(jwt.SigningMethodHS256)
	refreshClaims := refreshToken.Claims.(jwt.MapClaims)

	refreshClaims["authorized"] = true
	refreshClaims["email"] = user.Email
	refreshClaims["user_id"] = user.Id  // Add user_id to refresh token too
	refreshClaims["is_allowed"] = user.IsAllowed
	refreshClaims["iat"] = time.Now().Unix() // Issued at time
	refreshClaims["exp"] = time.Now().Add(REFRESH_TOKEN_EXPIRY).Unix()

	refreshTokenString, err := refreshToken.SignedString(secretKey)
	if err != nil {
		log.Info("Unable to generate the refresh token", err)
		return "", "", err
	}
	return accessTokenString, refreshTokenString, nil
}

/* Validate JWT Token and parse claims */
func ValidateToken(jwtToken string) (claims jwt.MapClaims, err error) {
	token, err := jwt.Parse(jwtToken, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("Invalid token signing method")
		}
		return secretKey, nil
	})

	if err != nil {
		log.Info("Invalid token found in request", jwtToken)
		return nil, err
	}

	if !token.Valid {
		log.Info("Invalid token found in request", jwtToken)
		return nil, errors.New("Invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		log.Info("Unable to parse Token claims", jwtToken)
		return nil, errors.New("Token Parsing Error")
	}

	exp := claims["exp"].(float64)
	if int64(exp) < time.Now().Unix() {
		log.Info("Sent token is expired", jwtToken)
		return nil, errors.New("Token is expired")
	}

	return claims, nil
}

/* Internal function to generate AT for a valid RT */
func RefreshJWT(refreshClaims jwt.MapClaims) (string, error) {
	accessToken := jwt.New(jwt.SigningMethodHS256)
	accessClaims := accessToken.Claims.(jwt.MapClaims)

	accessClaims["authorized"] = refreshClaims["authorized"]
	accessClaims["email"] = refreshClaims["email"]
	accessClaims["user_id"] = refreshClaims["user_id"]  // Now available from refresh token
	accessClaims["is_allowed"] = refreshClaims["is_allowed"]
	accessClaims["iat"] = time.Now().Unix()
	accessClaims["exp"] = time.Now().Add(ACCESS_TOKEN_EXPIRY).Unix()

	accessTokenString, err := accessToken.SignedString(secretKey)
	if err != nil {
		log.Info("Unable to generate the access token", err)
		return "", err
	}

	return accessTokenString, nil
}

func GenerateSessionToken(accessToken string) string {
	hash := md5.New()
	io.WriteString(hash, accessToken)
	return hex.EncodeToString(hash.Sum(nil))
}

func SetSessionCookie(ctx *gin.Context, sessionToken string, userEmail string, accessToken string, refreshToken string, updateRefreshExpiry bool) {
	w := ctx.Writer

	var accessExpiration = time.Now().Add(ACCESS_TOKEN_EXPIRY)
	var sessionExpiration = time.Now().Add(ACCESS_TOKEN_EXPIRY)
	var refreshExpiration = time.Now().Add(REFRESH_TOKEN_EXPIRY)

	// Set cookies with SameSite attribute
	cookie1 := http.Cookie{
		Name:     "session_token",
		Value:    sessionToken,
		Expires:  sessionExpiration,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode, // Add SameSite attribute
	}

	cookie2 := http.Cookie{
		Name:     "access_token",
		Value:    accessToken,
		Expires:  accessExpiration,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode, // Add SameSite attribute
	}

	http.SetCookie(w, &cookie1)
	http.SetCookie(w, &cookie2)

	if updateRefreshExpiry {
		cookie3 := http.Cookie{
			Name:     "refresh_token",
			Value:    refreshToken,
			Expires:  refreshExpiration,
			Path:     "/",
			HttpOnly: true,
			Secure:   true,
			SameSite: http.SameSiteLaxMode, // Add SameSite attribute
		}
		http.SetCookie(w, &cookie3)
	}

	// Log cookie setting for debugging
	log.Info("Setting cookies for user",
		"email", userEmail,
		"session_expiry", sessionExpiration,
		"access_expiry", accessExpiration,
		"update_refresh", updateRefreshExpiry)
}

func ValidateSession(ctx *gin.Context, sessionToken string, email string) bool {
	// The session token is derived from the access token
	// If the access token is valid (which we already checked in ValidateTokenRequest),
	// and the session token matches what we expect, then the session is valid

	// Get the access token from the request
	r := ctx.Request
	accessTokenCookie, err := r.Cookie("access_token")
	if err != nil {
		log.Info("No access token cookie found", err)
		return false
	}

	accessToken := accessTokenCookie.Value

	// Generate the expected session token from the access token
	expectedSessionToken := GenerateSessionToken(accessToken)

	// Compare with the provided session token
	if sessionToken != expectedSessionToken {
		log.Info("Session token mismatch", sessionToken, expectedSessionToken)
		return false
	}

	return true
}

/* Validate whether access token is valid */
func ValidateTokenRequest(ctx *gin.Context) (claims jwt.MapClaims, err error) {
	r := ctx.Request

	// Get the access token from cookies
	jwtRequest, err := r.Cookie("access_token")
	if err != nil {
		if err == http.ErrNoCookie {
			// Try to use refresh token instead
			refreshRequest, refreshErr := r.Cookie("refresh_token")
			if refreshErr != nil {
				log.Info("No access token or refresh token cookie found", err)
				return nil, errors.New("No access token found")
			}

			// We have a refresh token, try to use it to generate a new access token
			refreshRequestStr := refreshRequest.Value
			refreshClaims, refreshErr := ValidateToken(refreshRequestStr)
			if refreshErr != nil {
				log.Info("Invalid refresh token", refreshErr)
				return nil, errors.New("Invalid refresh token")
			}

			// Generate new access token
			updatedAccessTokenString, refreshErr := RefreshJWT(refreshClaims)
			if refreshErr != nil {
				log.Info("Unable to refresh the token", refreshErr)
				return nil, refreshErr
			}

			// Set new cookies
			updatedSessionToken := GenerateSessionToken(updatedAccessTokenString)
			SetSessionCookie(ctx, updatedSessionToken, refreshClaims["email"].(string), updatedAccessTokenString, refreshRequestStr, false)

			// Return the claims from the refresh token
			return refreshClaims, nil
		}
		log.Info("Unable to parse access token cookie", err)
		return nil, err
	}
	jwtRequestStr := jwtRequest.Value

	// Validate the token
	token, err := jwt.Parse(jwtRequestStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("There was an error in parsing")
		}
		return secretKey, nil
	})

	if token == nil {
		log.Info("Invalid Token found in request", jwtRequestStr)
		return nil, errors.New("Invalid Token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		log.Info("Unable to parse Token claims", r)
		return nil, errors.New("Token Parsing Error")
	}

	exp := claims["exp"].(float64)
	if int64(exp) < time.Now().Unix() {
		log.Info("Sent token is expired", r)
		return nil, errors.New("Access Token is expired, try sending Refresh Token")
	}

	// Auto-refresh if token is close to expiration
	if int64(exp) < time.Now().Add(AUTO_REFRESH_WINDOW).Unix() {
		refreshRequest, err := r.Cookie("refresh_token")
		if err != nil {
			log.Info("Cannot find refresh token in cookie, user will expire soon", r)
			return claims, nil
		}
		refreshRequestStr := refreshRequest.Value
		log.Info("Only a few minutes left before JWT expires, refreshing with new creds", r)
		refreshClaims, err := ValidateToken(refreshRequestStr)
		if err != nil {
			log.Info("Unable to refresh the token, user will expire soon", err)
			return claims, nil
		} else {
			updatedAccessTokenString, err := RefreshJWT(refreshClaims)
			if err != nil {
				log.Info("Unable to refresh the token, user will expire soon", err)
				return claims, nil
			}
			updatedSessionToken := GenerateSessionToken(updatedAccessTokenString)
			SetSessionCookie(ctx, updatedSessionToken, refreshClaims["email"].(string), updatedAccessTokenString, refreshRequestStr, false)
		}
	}
	return claims, nil
}

// GenerateStateToken creates a random token for OAuth state parameter
func GenerateStateToken() string {
	b := make([]byte, 16)
	rand.Read(b)
	return hex.EncodeToString(b)
}
