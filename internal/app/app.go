package app

import (
	"context"
	"errors"
	"log"
	"net/http"
	"strings"

	consumer "github.com/Zomato/cdp-platform/internal/consumer"
	temporalworker "github.com/Zomato/cdp-platform/internal/temporal/worker"
)

var (
	ErrInvalidModeConfig error  = errors.New("invalid app mode config")
	HTTP                 string = "http"
	DEV                  string = "dev"
	WORKER               string = "worker"
	APPSFLYER_CONSUMER   string = "appsflyer_consumer"
)

// App Struct
type App struct {
	mode   string
	server *http.Server
}

// NewApp returns App
func NewApp() *App {
	return &App{}
}

// WithRouter sets up Gin
func (app *App) WithRouter(s *http.Server) *App {
	app.server = s
	return app
}

// WithMode sets app mode
func (app *App) WithMode(mode string) *App {
	app.mode = mode
	return app
}

// Run starts the app according to mode
func (app *App) Run(ctx context.Context) error {
	switch strings.ToLower(app.mode) {
	case HTTP:
		return app.runHTTPServer()
	case WORKER:
		return app.runTemporalWorker(ctx)
	case APPSFLYER_CONSUMER:
		return app.runConsumer(ctx, app.mode)
	default:
		return ErrInvalidModeConfig
	}
}

// runHTTPServer starts the http server
func (app *App) runHTTPServer() error {
	if app.server == nil {
		log.Panicf("HTTP Server not registered...")
	}
	return app.server.ListenAndServe()
}

// runTemporalWorker starts the Temporal worker
func (app *App) runTemporalWorker(ctx context.Context) error {
	return temporalworker.SetupTemporalWorker(ctx)
}

func (app *App) runConsumer(ctx context.Context, consumerType string) error {
	return consumer.SetUpConsumer(ctx, consumerType)
}
