package postgres

import (
	"context"
	"database/sql"

	"github.com/Zomato/cdp-platform/internal/database"
	zsql "github.com/Zomato/cdp-platform/pkg/sqlx"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// Ensure DB implements the Querier interface
var _ database.Querier = (*DB)(nil)

type DB struct {
	pool        *zsql.DBPool
	Consistency zsql.Consistency
}

func NewClient(strongConsistency bool, masterConfig, replicaConfig *zsql.Config) (*DB, error) {
	pool, err := zsql.New("postgres", masterConfig, replicaConfig)
	if err != nil {
		return nil, err
	}

	var consistency zsql.Consistency
	if strongConsistency {
		consistency = zsql.ConsistencyStrong{}
	} else {
		consistency = zsql.ConsistencyEventual{}
	}

	return &DB{
		pool:        pool,
		Consistency: consistency,
	}, nil
}

func (db *DB) Close() error {
	return db.pool.Close()
}

func (db *DB) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	if db.shouldShutdownPostgres(ctx) {
		return nil
	}
	return db.Consistency.Reader(db.pool).QueryRowContext(ctx, query, args...)
}

// Used for fetching multiple rows.
func (db *DB) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed Postgres querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).QueryContext(ctx, query, args...)
}

// You have to pass all the args comma separated as compared to NamedExecContext
// where you can pass struct having relevant data
func (db *DB) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed Postgres querying via killswitch")
	}
	return db.Consistency.Writer(db.pool).ExecContext(ctx, query, args...)
}

// Wrapper over QueryRowContext func. It is also used for fetching a single row. But you don't need to worry
// about closing rows etc. It is handled internally.
func (db *DB) GetContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
	if db.shouldShutdownPostgres(ctx) {
		return errors.New("killed Postgres querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).GetContext(ctx, dest, query, args...)
}

// Wrapper over QueryContext func. It is also used for fetching multiple row. But you don't need to worry
// about closing rows etc. It is handled internally.
func (db *DB) SelectContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
	if db.shouldShutdownPostgres(ctx) {
		return errors.New("killed Postgres querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).SelectContext(ctx, dest, query, args...)
}

// You have to pass args as a struct or array of struct(for multiple inserts)
// as compared to ExecContext where you have to pass each args comma separated.
func (db *DB) NamedExecContext(ctx context.Context, query string, arg interface{}) (sql.Result, error) {
	var emptyStruct sql.Result
	if db.shouldShutdownPostgres(ctx) {
		return emptyStruct, errors.New("killed postgres quering via killswitch")
	}

	return db.Consistency.Writer(db.pool).NamedExecContext(ctx, query, arg)
}

func (db *DB) NamedQueryContext(ctx context.Context, query string, arg interface{}) (*sqlx.Rows, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed postgres quering via killswitch")
	}

	return db.Consistency.Reader(db.pool).NamedQueryContext(ctx, query, arg)
}

// wrapper over stmt is probably needed to add stmt metrics in newrelic
// can use this approach to capture exec for stmts: https://github.com/Zomato/reviews-service/blob/master/pkg/zomato/sql/zdb/zdb.go
func (db *DB) PrepareContext(ctx context.Context, query string) (*sqlx.Stmt, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed postgres quering via killswitch")
	}

	return db.Consistency.Reader(db.pool).PreparexContext(ctx, query)
}

func (db *DB) PrepareNamedContext(ctx context.Context, query string) (*sqlx.NamedStmt, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed postgres quering via killswitch")
	}

	return db.Consistency.Reader(db.pool).PrepareNamedContext(ctx, query)
}

func (db *DB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sqlx.Tx, error) {
	if db.shouldShutdownPostgres(ctx) {
		return nil, errors.New("killed postgres quering via killswitch")
	}

	if opts != nil && opts.ReadOnly {
		return db.Consistency.Reader(db.pool).BeginTxx(ctx, opts)
	}

	return db.Consistency.Writer(db.pool).BeginTxx(ctx, opts)
}

func (db *DB) shouldShutdownPostgres(ctx context.Context) bool {
	return db == nil || db.pool == nil
	// config.GetBool(ctx, "postgres.kill_switch")  # don't check from config as used in temporal
}
