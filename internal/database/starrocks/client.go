package starrocks

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Zomato/cdp-platform/internal/database"
	zsql "github.com/Zomato/cdp-platform/pkg/sqlx"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// Ensure DB implements the Querier interface
var _ database.Querier = (*DB)(nil)

type DB struct {
	pool        *zsql.DBPool
	Consistency zsql.Consistency
}

func NewClient(strongConsistency bool, masterConfig *zsql.Config, replicaConfig *zsql.Config) (*DB, error) {
	if masterConfig == nil {
		return nil, errors.New("master config is required")
	}

	// Validate minimum required fields
	if masterConfig.Host == "" || masterConfig.Port == 0 || masterConfig.User == "" {
		return nil, errors.New("host, port and user are required fields for StarRocks connection")
	}

	// Empty password and database are allowed for StarRocks
	if masterConfig.Password == "" {
		masterConfig.Password = "" // Explicitly set to empty string
	}

	// If replica config is provided, validate it as well
	if replicaConfig != nil {
		if replicaConfig.Host == "" || replicaConfig.Port == 0 || replicaConfig.User == "" {
			return nil, errors.New("host, port and user are required fields for StarRocks replica connection")
		}

		// Match master's empty fields
		if replicaConfig.Password == "" {
			replicaConfig.Password = ""
		}
		if replicaConfig.DBName == "" {
			replicaConfig.DBName = masterConfig.DBName
		}
	}

	pool, err := zsql.New("mysql", masterConfig, replicaConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create StarRocks connection pool: %w", err)
	}

	var consistency zsql.Consistency
	if strongConsistency {
		consistency = zsql.ConsistencyStrong{}
	} else {
		consistency = zsql.ConsistencyEventual{}
	}

	return &DB{
		pool:        pool,
		Consistency: consistency,
	}, nil
}

func (db *DB) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	if db.shouldShutdownStarRocks(ctx) {
		return nil
	}
	return db.Consistency.Reader(db.pool).QueryRowContext(ctx, query, args...)
}

func (db *DB) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).QueryContext(ctx, query, args...)
}

func (db *DB) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Writer(db.pool).ExecContext(ctx, query, args...)
}

func (db *DB) GetContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
	if db.shouldShutdownStarRocks(ctx) {
		return errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).GetContext(ctx, dest, query, args...)
}

func (db *DB) SelectContext(ctx context.Context, dest interface{}, query string, args ...interface{}) error {
	if db.shouldShutdownStarRocks(ctx) {
		return errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).SelectContext(ctx, dest, query, args...)
}

func (db *DB) NamedExecContext(ctx context.Context, query string, arg interface{}) (sql.Result, error) {
	var emptyStruct sql.Result
	if db.shouldShutdownStarRocks(ctx) {
		return emptyStruct, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Writer(db.pool).NamedExecContext(ctx, query, arg)
}

func (db *DB) NamedQueryContext(ctx context.Context, query string, arg interface{}) (*sqlx.Rows, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).NamedQueryContext(ctx, query, arg)
}

func (db *DB) PrepareContext(ctx context.Context, query string) (*sqlx.Stmt, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).PreparexContext(ctx, query)
}

func (db *DB) PrepareNamedContext(ctx context.Context, query string) (*sqlx.NamedStmt, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}
	return db.Consistency.Reader(db.pool).PrepareNamedContext(ctx, query)
}

func (db *DB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sqlx.Tx, error) {
	if db.shouldShutdownStarRocks(ctx) {
		return nil, errors.New("killed StarRocks querying via killswitch")
	}

	if opts != nil && opts.ReadOnly {
		return db.Consistency.Reader(db.pool).BeginTxx(ctx, opts)
	}
	return db.Consistency.Writer(db.pool).BeginTxx(ctx, opts)
}

func (db *DB) shouldShutdownStarRocks(ctx context.Context) bool {
	if db == nil {
		fmt.Println("StarRocks client is nil")
		return true
	}
	if db.pool == nil {
		fmt.Println("StarRocks connection pool is nil")
		return true
	}
	// if config.GetBool(ctx, "starrocks.kill_switch") {
	// 	fmt.Println("StarRocks killswitch is enabled")
	// 	return true
	// }
	return false
}

func (db *DB) Close() error {
	if db == nil || db.pool == nil {
		return nil
	}
	return db.pool.Close()
}

func (db *DB) Ping() error {
	if db == nil {
		return errors.New("StarRocks client is nil")
	}
	if db.pool == nil {
		return errors.New("StarRocks connection pool is nil")
	}
	return db.pool.Ping()
}
