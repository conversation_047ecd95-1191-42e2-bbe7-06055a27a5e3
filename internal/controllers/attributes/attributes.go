package attributecontroller

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	entity "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	attributequeriescontroller "github.com/Zomato/cdp-platform/internal/controllers/attributequeries"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	entitycontroller "github.com/Zomato/cdp-platform/internal/controllers/entity"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/cdp-platform/utils"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	AttributeQueriesController *attributequeriescontroller.Controller
	TemporalController         *temporalcontroller.Controller
	StarrocksController        *starrockscontroller.Controller
	DataSourceController       *datasourcecontroller.Controller
	EntityController           *entitycontroller.Controller
}

func (c Controller) GetAttributesByQueryID(ctx context.Context, queryID int64) ([]dtos.AttributeInfo, error) {
	query := `
        SELECT
            a.name,
            bai.query_column_name,
            bai.data_type
        FROM attributes a
        JOIN base_attribute_info bai ON a.id = bai.attribute_id
        JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
        WHERE aq.id = $1
    `

	var attributes []dtos.AttributeInfo
	rows, err := c.PostgresClient.QueryContext(ctx, query, queryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var attr dtos.AttributeInfo
		if err := rows.Scan(&attr.Name, &attr.QueryColumnName, &attr.DataType); err != nil {
			return nil, err
		}
		attributes = append(attributes, attr)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return attributes, nil
}

// prepareFunctionalAttributeExpression replaces placeholders in a functional attribute expression
// with actual column names while keeping the {params} placeholder intact for later substitution.
func (c Controller) prepareFunctionalAttributeExpression(ctx context.Context, expression string, attributeId int64, applyPrefix bool) (string, error) {
	// Get source attributes to replace placeholders in expression
	sourceQuery := `
        SELECT dam.source_attribute_id, dam.position
        FROM derived_attribute_mapping dam
        WHERE dam.derived_attribute_id = $1
        ORDER BY dam.position
    `

	type sourceAttr struct {
		SourceAttributeID int64 `db:"source_attribute_id"`
		Position          int   `db:"position"`
	}

	var sourceAttrs []sourceAttr
	err := c.PostgresClient.SelectContext(ctx, &sourceAttrs, sourceQuery, attributeId)
	if err != nil {
		return "", fmt.Errorf("error getting source attributes for functional attribute: %w", err)
	}

	// Replace placeholders with column expressions except for {params}
	result := expression
	for _, sa := range sourceAttrs {
		// Call GetAttributeColumnExpression with the prefix
		colExpr, err := c.GetAttributeColumnExpression(ctx, sa.SourceAttributeID, applyPrefix)
		if err != nil {
			return "", err
		}
		placeholder := fmt.Sprintf("{%d}", sa.Position)
		result = strings.ReplaceAll(result, placeholder, colExpr)
	}

	return result, nil
}

// Helper function to check if an attribute is functional
func (c Controller) IsAttributeFunctional(ctx context.Context, attributeID int64) (bool, error) {
	query := `
        SELECT attribute_type
        FROM derived_attribute_info
        WHERE attribute_id = $1
    `

	var attrType string
	err := c.PostgresClient.GetContext(ctx, &attrType, query, attributeID)
	if err != nil {
		if err == sql.ErrNoRows {
			// If no record found in derived_attribute_info, it's not a derived attribute
			return false, nil
		}
		return false, err
	}

	return attrType == "FUNCTIONAL", nil
}

func (c Controller) GetAttributeColumnExpression(ctx context.Context, id int64, applyPrefix bool) (string, error) {
	// First, determine the attribute type
	typeQuery := `
        SELECT attribute_type
        FROM attributes
        WHERE id = $1
    `

	var attrType dtos.AttributeType
	err := c.PostgresClient.GetContext(ctx, &attrType, typeQuery, id)
	if err != nil {
		logger.Info("Error fetching attribute type from postgres", err)
		return "", err
	}

	// Based on attribute type, fetch just the expression or column name needed for the condition
	if attrType == dtos.AttributeTypeBase {
		columnQuery := `
            SELECT 
                bai.query_column_name,
                e.table_name
            FROM base_attribute_info bai
            JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
            JOIN entities e ON aq.entity_id = e.id
            WHERE bai.attribute_id = $1
        `
		var result struct {
			QueryColumnName string `db:"query_column_name"`
			TableName       string `db:"table_name"`
		}

		err = c.PostgresClient.GetContext(ctx, &result, columnQuery, id)
		if err != nil {
			logger.Info("Error fetching query column name from postgres", err)
			return "", err
		}

		if applyPrefix {
			return result.TableName + "_" + result.QueryColumnName, nil
		}

		return result.QueryColumnName, nil
	} else if attrType == dtos.AttributeTypeDerived {
		// For derived attributes, check derived_attribute_type
		attrQuery := `
            SELECT expression, attribute_type
            FROM derived_attribute_info
            WHERE attribute_id = $1
        `

		var result struct {
			Expression    string `db:"expression"`
			AttributeType string `db:"attribute_type"`
		}

		err = c.PostgresClient.GetContext(ctx, &result, attrQuery, id)
		if err != nil {
			logger.Info("Error fetching derived attribute info from postgres", err)
			return "", err
		}

		// Handle different derived attribute types
		switch result.AttributeType {
		case "FUNCTIONAL":
			// For functional attributes, replace column placeholders with actual column names
			// but keep {params} placeholder intact for later substitution
			preparedExpr, err := c.prepareFunctionalAttributeExpression(ctx, result.Expression, id, applyPrefix)
			if err != nil {
				return "", fmt.Errorf("error preparing derived expression: %w", err)
			}
			return preparedExpr, nil

		case "GENERAL", "CONDITIONAL", "TGCG":
			// For non-functional derived attributes, prepare the expression by replacing all placeholders
			preparedExpr, err := c.prepareNonFunctionalDerivedAttributeExpression(ctx, result.Expression, id, applyPrefix)
			if err != nil {
				return "", fmt.Errorf("error preparing derived expression: %w", err)
			}
			return preparedExpr, nil

		default:
			return "", fmt.Errorf("unsupported derived attribute type: %s", result.AttributeType)
		}
	} else {
		return "", fmt.Errorf("unknown attribute type: %s", attrType)
	}
}

func (c Controller) GetAttributeSchemaDetails(ctx context.Context) ([]dtos.AttributeInfo, error) {
	// Query for base attributes
	baseQuery := `
		SELECT
			a.id,
			a.name,
			a.description,
			a.attribute_type,
			a.attribute_category,
			a.attribute_purpose,
			bai.query_column_name,
			bai.data_type,
			aq.entity_id
		FROM attributes a
		JOIN base_attribute_info bai ON a.id = bai.attribute_id
		JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		WHERE 
			a.attribute_type = 'BASE' 
			AND a.attribute_category <> 'PROPERTY'
	`

	// Query for derived attributes excluding TGCG type
	// ! This is returning only 1 top row, change if we have derived attributes spanning over MULTIPLE ENTITIES
	derivedQuery := `
        SELECT DISTINCT ON (a.id)
            a.id,
            a.name,
            a.description,
            a.attribute_type,
            a.attribute_category,
            a.attribute_purpose,
            dai.data_type,
            dai.readable_expression,
            dai.attribute_type as derived_attribute_type,
            dam.entity_id
        FROM attributes a
        JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        JOIN derived_attribute_mapping dam ON a.id = dam.derived_attribute_id
        WHERE 
            a.attribute_type = 'DERIVED' 
            AND dai.attribute_type <> 'TGCG'
            AND a.attribute_category <> 'PROPERTY'
        ORDER BY a.id, dam.entity_id
	`

	// Execute base attributes query
	var baseAttributes []dtos.AttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &baseAttributes, baseQuery)
	if err != nil {
		logger.Info("Error fetching base attributes from postgres", err)
		return nil, err
	}

	// Execute derived attributes query (only TGCG)
	var derivedAttributes []dtos.AttributeInfo
	err = c.PostgresClient.SelectContext(ctx, &derivedAttributes, derivedQuery)
	if err != nil {
		logger.Info("Error fetching TGCG derived attributes from postgres", err)
		return nil, err
	}

	// For each derived attribute, fetch just the source attribute IDs
	for i := range derivedAttributes {
		sourceQuery := `
            SELECT source_attribute_id
            FROM derived_attribute_mapping
            WHERE derived_attribute_id = $1
            ORDER BY position
        `
		var sourceAttributeIds []int64
		err = c.PostgresClient.SelectContext(ctx, &sourceAttributeIds, sourceQuery, derivedAttributes[i].Id)
		if err != nil {
			logger.Info("Error fetching source attribute IDs for derived attribute", err)
			return nil, err
		}
		derivedAttributes[i].SourceAttributeIds = sourceAttributeIds
	}

	// Combine both attribute types
	attributes := make([]dtos.AttributeInfo, 0, len(baseAttributes)+len(derivedAttributes))
	attributes = append(attributes, baseAttributes...)
	attributes = append(attributes, derivedAttributes...)

	return attributes, nil
}

func (c Controller) GetAttributeSchemaDetailsTgCg(ctx context.Context) ([]dtos.AttributeInfo, error) {
	// Query for TGCG derived attributes
	derivedQuery := `
        SELECT DISTINCT ON (a.id)
            a.id,
            a.name,
            a.attribute_type,
            a.attribute_category,
            a.attribute_purpose,
            dai.data_type,
            dai.readable_expression,
            dai.attribute_type as derived_attribute_type,
            dam.entity_id
        FROM attributes a
        JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        JOIN derived_attribute_mapping dam ON a.id = dam.derived_attribute_id
        WHERE 
            a.attribute_type = 'DERIVED' 
            AND dai.attribute_type = 'TGCG'
            AND a.attribute_category <> 'PROPERTY'
        ORDER BY a.id, dam.entity_id
	`

	// Execute derived attributes query (only TGCG)
	var derivedAttributes []dtos.AttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &derivedAttributes, derivedQuery)
	if err != nil {
		logger.Error("Error fetching TGCG derived attributes from Postgres: ", err)
		return []dtos.AttributeInfo{}, err
	}

	return derivedAttributes, nil
}

func (c Controller) GetAllBaseAttributes(ctx context.Context) ([]dtos.BaseAttributeInfo, error) {
	// Query for base attributes
	baseQuery := `
		SELECT 
			a.id, 
			a.name, 
			a.description,
			a.attribute_type,
			a.owner_id,
			a.created_at, 
			a.updated_at,
			a.attribute_category,
			a.attribute_purpose,
			bai.query_column_name, 
			bai.data_type,
			bai.attribute_query_id,
			aq.query_text, 
			aq.cron_expression,
			aq.data_source_id,
			aq.entity_id
		FROM attributes a
		JOIN base_attribute_info bai ON a.id = bai.attribute_id
		JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		WHERE 
			a.attribute_type = 'BASE'
			AND a.attribute_category <> 'PROPERTY'
		ORDER BY a.name
	`

	// Execute base attributes query
	var baseAttributes []dtos.BaseAttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &baseAttributes, baseQuery)
	if err != nil {
		logger.Info("Error fetching base attributes from postgres", err)
		return nil, err
	}

	return baseAttributes, nil
}

func (c Controller) GetAllDerivedAttributes(ctx context.Context) ([]dtos.DerivedAttributeInfo, error) {
	// Query for derived attributes
	derivedQuery := `
        SELECT DISTINCT ON (a.id)
            a.id, 
            a.name, 
            a.description,
            a.attribute_type,
            a.owner_id,
            a.created_at, 
            a.updated_at,
            a.attribute_category,
            a.attribute_purpose,
            dai.data_type,
            dai.readable_expression,
            dai.attribute_type as derived_attribute_type,
            dam.entity_id
        FROM attributes a
        JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        JOIN derived_attribute_mapping dam ON a.id = dam.derived_attribute_id
        WHERE 
            a.attribute_type = 'DERIVED'
            AND a.attribute_category != 'PROPERTY'
        ORDER BY a.id, dam.entity_id, a.name
	`

	// Execute derived attributes query
	var derivedAttributes []dtos.DerivedAttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &derivedAttributes, derivedQuery)
	if err != nil {
		logger.Info("Error fetching derived attributes from postgres", err)
		return nil, err
	}

	// Get source attributes for derived attributes
	for i := range derivedAttributes {
		sourceQuery := `
			SELECT a.id, a.name
			FROM derived_attribute_mapping dam
			JOIN attributes a ON dam.source_attribute_id = a.id
			WHERE dam.derived_attribute_id = $1
		`
		var sourceAttributes []dtos.SourceAttributeRef
		err = c.PostgresClient.SelectContext(ctx, &sourceAttributes, sourceQuery, derivedAttributes[i].Id)
		if err != nil {
			logger.Info("Error fetching source attributes for derived attribute", err)
			return nil, err
		}
		derivedAttributes[i].SourceAttributes = sourceAttributes
	}

	return derivedAttributes, nil
}

func (c Controller) GetAttributeByID(ctx context.Context, id int64) (*dtos.AttributeInfo, error) {
	// First, determine the attribute type
	typeQuery := `
		SELECT attribute_type
		FROM attributes
		WHERE id = $1
	`

	var attrType dtos.AttributeType
	err := c.PostgresClient.GetContext(ctx, &attrType, typeQuery, id)
	if err != nil {
		logger.Info("Error fetching attribute type from postgres", err)
		return nil, err
	}

	// Fetch attribute details based on type
	var attribute *dtos.AttributeInfo
	if attrType == dtos.AttributeTypeBase {
		attribute, err = c.fetchBaseAttributeDetails(ctx, id)
	} else if attrType == dtos.AttributeTypeDerived {
		attribute, err = c.fetchDerivedAttributeDetails(ctx, id)
	} else {
		return nil, fmt.Errorf("unknown attribute type: %s", attrType)
	}

	if err != nil {
		return nil, err
	}

	// Fetch associated segments
	segments, err := c.fetchAttributeSegments(ctx, id)
	if err != nil {
		return nil, err
	}
	attribute.Segments = segments

	return attribute, nil
}

func (c Controller) fetchBaseAttributeDetails(ctx context.Context, id int64) (*dtos.AttributeInfo, error) {
	query := `
		SELECT 
			a.id, 
			a.name, 
			a.description,
			a.attribute_type,
			a.owner_id,
			a.created_at, 
			a.updated_at,
			a.attribute_category,
			a.attribute_purpose,
			bai.query_column_name, 
			bai.data_type,
			bai.attribute_query_id,
			aq.query_text, 
			aq.cron_expression,
			aq.data_source_id,
			aq.entity_id
		FROM attributes a
		JOIN base_attribute_info bai ON a.id = bai.attribute_id
		JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		WHERE a.id = $1 AND a.attribute_type = 'BASE'
	`

	var baseAttr dtos.BaseAttributeInfo
	err := c.PostgresClient.GetContext(ctx, &baseAttr, query, id)
	if err != nil {
		logger.Info("Error fetching base attribute by ID from postgres", err)
		return nil, err
	}

	// Convert BaseAttributeInfo to AttributeInfo
	attributeInfo := &dtos.AttributeInfo{
		Base:              baseAttr.Base,
		Name:              baseAttr.Name,
		Description:       baseAttr.Description,
		AttributeType:     baseAttr.AttributeType,
		AttributeCategory: baseAttr.AttributeCategory,
		OwnerID:           baseAttr.OwnerID,
		QueryColumnName:   baseAttr.QueryColumnName,
		DataType:          baseAttr.DataType,
		AttributeQueryId:  baseAttr.AttributeQueryId,
		QueryText:         baseAttr.QueryText,
		CronExpression:    baseAttr.CronExpression,
		DataSourceID:      baseAttr.DataSourceID,
		EntityID:          baseAttr.EntityID,
		AttributePurpose:  baseAttr.AttributePurpose,
	}

	return attributeInfo, nil
}

func (c Controller) fetchDerivedAttributeDetails(ctx context.Context, id int64) (*dtos.AttributeInfo, error) {
	query := `
        SELECT DISTINCT ON (a.id)
            a.id, 
            a.name, 
            a.description,
            a.attribute_type,
            a.owner_id,
            a.created_at, 
            a.updated_at,
            a.attribute_category,
            a.attribute_purpose,
            dai.data_type,
            dai.readable_expression,
            dai.attribute_type as derived_attribute_type,
            dam.entity_id
        FROM attributes a
        JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        JOIN derived_attribute_mapping dam ON a.id = dam.derived_attribute_id
        WHERE a.id = $1 AND a.attribute_type = 'DERIVED'
        ORDER BY a.id, dam.entity_id
	`

	var derivedAttr dtos.DerivedAttributeInfo
	err := c.PostgresClient.GetContext(ctx, &derivedAttr, query, id)
	if err != nil {
		logger.Info("Error fetching derived attribute by ID from postgres", err)
		return nil, err
	}

	// Fetch source attributes
	sourceQuery := `
		SELECT a.id, a.name
		FROM derived_attribute_mapping dam
		JOIN attributes a ON dam.source_attribute_id = a.id
		WHERE dam.derived_attribute_id = $1
	`
	var sourceAttributes []dtos.SourceAttributeRef
	err = c.PostgresClient.SelectContext(ctx, &sourceAttributes, sourceQuery, id)
	if err != nil {
		logger.Info("Error fetching source attributes for derived attribute", err)
		return nil, err
	}
	derivedAttr.SourceAttributes = sourceAttributes

	// Convert DerivedAttributeInfo to AttributeInfo
	attributeInfo := &dtos.AttributeInfo{
		Base:                 derivedAttr.Base,
		Name:                 derivedAttr.Name,
		Description:          derivedAttr.Description,
		AttributeType:        derivedAttr.AttributeType,
		AttributeCategory:    derivedAttr.AttributeCategory,
		OwnerID:              derivedAttr.OwnerID,
		DataType:             derivedAttr.DataType,
		Expression:           derivedAttr.Expression,
		DerivedAttributeType: derivedAttr.DerivedAttributeType,
		SourceAttributes:     derivedAttr.SourceAttributes,
		AttributePurpose:     derivedAttr.AttributePurpose,
		EntityID:             derivedAttr.EntityID,
		ReadableExpression:   derivedAttr.ReadableExpression,
	}

	return attributeInfo, nil
}

func (c Controller) fetchAttributeSegments(ctx context.Context, id int64) ([]dtos.Segment, error) {
	query := `
		SELECT 
			s.id,
			s.name
		FROM segments s
		JOIN segment_attribute_mapping sam ON s.id = sam.segment_id
		WHERE sam.attribute_id = $1
		ORDER BY s.created_at DESC
	`

	var segments []dtos.Segment
	err := c.PostgresClient.SelectContext(ctx, &segments, query, id)
	if err != nil {
		logger.Info("Error fetching segments for attribute from postgres", err)
		return nil, err
	}

	return segments, nil
}

func (c Controller) UpdateAttribute(ctx context.Context, attributeDTO *dtos.Attributes) error {
	// Validations: Check if the new name already exists in another record
	if attributeDTO.Name != "" {
		var existingCount int
		checkQuery := `SELECT COUNT(*) FROM attributes WHERE name = $1`
		err := c.PostgresClient.GetContext(ctx, &existingCount, checkQuery, attributeDTO.Name)
		if err != nil {
			return err
		}
		if existingCount > 0 {
			return errors.New("attribute name already exists")
		}
	}

	query := "UPDATE attributes SET "
	var updates []string
	args := map[string]interface{}{"id": attributeDTO.Id}

	if attributeDTO.Name != "" {
		updates = append(updates, "name = :name")
		args["name"] = attributeDTO.Name
	}
	if attributeDTO.Description != "" {
		updates = append(updates, "description = :description")
		args["description"] = attributeDTO.Description
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += strings.Join(updates, ", ") + ", updated_at = NOW() WHERE id = :id"

	_, err := c.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (c Controller) ValidateAttributeNames(ctx context.Context, validateAttributeNamesDTO *dtos.ValidateAttributeNames) (*dtos.ValidateAttributeNameResponse, error) {
	// Step 1: Fetch all existing names from attributes table
	var existingAttributeNames []string
	query := "SELECT name FROM attributes"
	err := c.PostgresClient.SelectContext(ctx, &existingAttributeNames, query)
	if err != nil {
		logger.Info("Error fetching existing attribute names", err)
		return nil, err
	}

	// Step 2: Extract names from request DTO
	newAttributeNames := validateAttributeNamesDTO.AttributeNames
	existingNamesInCurrentAttributes := []string{}
	for _, name := range newAttributeNames {
		if utils.StringInSlice(name, existingAttributeNames) {
			existingNamesInCurrentAttributes = append(existingNamesInCurrentAttributes, name)
		}
	}

	// Step 3: Determine if the names are valid (should not match existing ones)
	isValid := len(existingNamesInCurrentAttributes) == 0

	return &dtos.ValidateAttributeNameResponse{
		ExistingColumns: existingNamesInCurrentAttributes,
		IsValid:         isValid,
	}, nil
}

func (c Controller) RegisterAttribute(ctx context.Context, registerAttributeRequestDTO *dtos.RegisterAttributeRequest) error {
	// Starting transaction
	tx, err := c.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	var endDate *time.Time
	parsedEndDate, err := c.validateAttributeQueryEndDate(registerAttributeRequestDTO.EndDate, nil)
	if err != nil {
		return fmt.Errorf("Error validating end date: %w", err)
	}
	endDate = parsedEndDate

	// 1. Insert into attribute_queries table
	attributeQueryQuery := `
        INSERT INTO attribute_queries (
            data_source_id,
            owner_id,
            cron_expression,
            query_text,
			entity_id,
			end_date
        ) VALUES (
            :data_source_id,
            :owner_id,
            :cron_expression,
            :query_text,
            :entity_id,
            :end_date
        ) RETURNING id, created_at, updated_at`

	attributeQuery := &entity.AttributeQueries{
		DataSourceID:   registerAttributeRequestDTO.DataSourceID,
		CronExpression: registerAttributeRequestDTO.CronExpression,
		QueryText:      registerAttributeRequestDTO.QueryText,
		OwnerID:        registerAttributeRequestDTO.OwnerID,
		EntityID:       registerAttributeRequestDTO.EntityID,
		EndDate:        endDate,
	}

	rows, err := tx.NamedQuery(attributeQueryQuery, attributeQuery)
	if err != nil {
		return fmt.Errorf("failed to insert attribute query: %w", err)
	}

	if !rows.Next() {
		return errors.New("no id returned from attribute query insert")
	}

	err = rows.Scan(&attributeQuery.Id, &attributeQuery.CreatedAt, &attributeQuery.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to scan attribute query result: %w", err)
	}
	rows.Close() // Need to explicitly Close in transaction

	// 2. For each attribute, insert into attributes table and then base_attribute_info
	for _, attrInfo := range registerAttributeRequestDTO.Attributes {
		// First insert into attributes table
		attributeInsertQuery := `
			INSERT INTO attributes (
				name,
				description,
				owner_id,
				attribute_type,
				attribute_category,
				attribute_purpose
			) VALUES (
				:name,
				:description,
				:owner_id,
				:attribute_type,
				:attribute_category,
				:attribute_purpose		
			) RETURNING id, created_at, updated_at`

		attribute := &entity.Attributes{
			Name:              attrInfo.Name,
			Description:       attrInfo.Description,
			OwnerID:           registerAttributeRequestDTO.OwnerID,
			AttributeType:     entity.AttributeTypeBase,
			AttributeCategory: entity.AttributeCategory(attrInfo.AttributeCategory),
			AttributePurpose:  entity.AttributePurpose(dtos.AttributePurposeCondition),
		}

		attrRows, err := tx.NamedQuery(attributeInsertQuery, attribute)
		if err != nil {
			return fmt.Errorf("failed to insert attribute: %w", err)
		}

		if !attrRows.Next() {
			attrRows.Close()
			return errors.New("no id returned from attribute insert")
		}

		err = attrRows.Scan(&attribute.Id, &attribute.CreatedAt, &attribute.UpdatedAt)
		if err != nil {
			attrRows.Close()
			return fmt.Errorf("failed to scan attribute result: %w", err)
		}
		attrRows.Close()

		// Then insert into base_attribute_info table
		baseAttrQuery := `
			INSERT INTO base_attribute_info (
				attribute_id,
				query_column_name,
				data_type,
				attribute_query_id
			) VALUES (
				:attribute_id,
				:query_column_name,
				:data_type,
				:attribute_query_id
			)`

		baseAttr := struct {
			AttributeID      int64  `db:"attribute_id"`
			QueryColumnName  string `db:"query_column_name"`
			DataType         string `db:"data_type"`
			AttributeQueryID int64  `db:"attribute_query_id"`
		}{
			AttributeID:      attribute.Id,
			QueryColumnName:  attrInfo.ColumnName,
			DataType:         attrInfo.DataType,
			AttributeQueryID: attributeQuery.Id,
		}

		_, err = tx.NamedExec(baseAttrQuery, baseAttr)
		if err != nil {
			return fmt.Errorf("failed to insert base attribute info: %w", err)
		}
	}

	// Get data source type to determine which workflow to run
	dataSourceInfo, err := c.DataSourceController.GetDataSourceConfigMapAndType(ctx, registerAttributeRequestDTO.DataSourceID)
	if err != nil {
		return fmt.Errorf("failed to get data source info: %w", err)
	}

	// Select workflow type based on data source type
	var workflowType enum.WorkflowType
	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		workflowType = enum.SyncAttributeQueryWorkflow
	case enum.Spark:
		workflowType = enum.SyncAttributeQueryWorkflowSpark
	default:
		return fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: workflowType,
		ScheduleSpec: dtos.ScheduleSpecRequest{
			CronExpressions: []string{registerAttributeRequestDTO.CronExpression},
			EndAt:           endDate,
		},
		Args: []interface{}{attributeQuery.Id},
	}

	err = c.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}

	// Commit transaction -> Will be committed only if all the above doesn't return err
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Now trigger the first sync immediately after data is committed
	triggerInput := dtos.TriggerWorkflowInput{
		WorkflowType: workflowType,
		Args:         []interface{}{attributeQuery.Id},
	}

	err = c.TemporalController.TriggerWorkflow(ctx, triggerInput)
	if err != nil {
		// Log warning but don't fail since registration is already complete
		logger.Warn("Failed to trigger immediate first sync for attribute, sync will start on next cron schedule",
			"attributeQueryId", attributeQuery.Id, "error", err)
	} else {
		logger.Info("Successfully triggered immediate first sync for attribute", "attributeQueryId", attributeQuery.Id)
	}

	return nil
}
func (c Controller) validateAttributeQueryEndDate(endDateStr *string, currentEndDate *time.Time) (*time.Time, error) {
	if endDateStr == nil || *endDateStr == "" {
		return nil, fmt.Errorf("end date is required")
	}

	// Parse the end date using the existing time utility
	endDate, err := utils.ParseISTDateToStartOfDay(*endDateStr)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format: %w", err)
	}

	// Validate that end date is in the future
	now := time.Now()
	if endDate.Before(now) {
		return nil, fmt.Errorf("end date must be in the future. Provided: %s, Current time: %s",
			endDate.Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05"))
	}

	// If currentEndDate is provided, validate that new end date is not decreased
	if currentEndDate != nil && endDate.Before(*currentEndDate) {
		return nil, fmt.Errorf("end date can only be increased, not decreased. Current: %s, New: %s",
			currentEndDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}

	return &endDate, nil
}

// prepareNonFunctionalDerivedAttributeExpression replaces placeholders in a derived attribute expression
// with actual column names for querying. This only works for non-functional derived attributes.
func (c Controller) prepareNonFunctionalDerivedAttributeExpression(ctx context.Context, expression string, attributeId int64, applyPrefix bool) (string, error) {
	// Get source attributes to replace placeholders in expression
	sourceQuery := `
        SELECT 
            dam.source_attribute_id,
            dam.position,
            bai.query_column_name,
            e.table_name
        FROM derived_attribute_mapping dam
        JOIN attributes a ON dam.source_attribute_id = a.id
        LEFT JOIN base_attribute_info bai ON a.id = bai.attribute_id
        LEFT JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
        LEFT JOIN entities e ON aq.entity_id = e.id
        WHERE dam.derived_attribute_id = $1
        ORDER BY dam.position
    `

	var sourceAttributes []struct {
		SourceAttributeID int64   `db:"source_attribute_id"`
		Position          int     `db:"position"`
		QueryColumnName   *string `db:"query_column_name"`
		TableName         *string `db:"table_name"`
	}

	err := c.PostgresClient.SelectContext(ctx, &sourceAttributes, sourceQuery, attributeId)
	if err != nil {
		return "", fmt.Errorf("error fetching source attributes: %w", err)
	}

	// Replace placeholders with actual column names for querying
	expr := expression
	for _, attr := range sourceAttributes {
		placeholder := fmt.Sprintf("{%d}", attr.Position)

		if attr.QueryColumnName != nil {
			var columnName string

			// For base attributes, we can directly use the column name with prefix if needed
			if applyPrefix && attr.TableName != nil {
				columnName = *attr.TableName + "_" + *attr.QueryColumnName
			} else if attr.QueryColumnName != nil {
				columnName = *attr.QueryColumnName
			}

			expr = strings.ReplaceAll(expr, placeholder, columnName)
		} else {
			// For derived attributes, recursively get their expression
			columnExpr, err := c.GetAttributeColumnExpression(ctx, attr.SourceAttributeID, applyPrefix)
			if err != nil {
				return "", err
			}
			expr = strings.ReplaceAll(expr, placeholder, columnExpr)
		}
	}

	return expr, nil
}

func (c Controller) GetAttributeDistinctValues(ctx context.Context, attributeId int64) (*dtos.AttributeDistinctValues, error) {
	query := `
        SELECT
            a.attribute_type,
            bai.query_column_name,
            bai.data_type as base_data_type,
            dai.expression,
            dai.data_type as derived_data_type,
            a.attribute_category,
            dai.attribute_type as derived_attribute_type,
            COALESCE(
                aq.entity_id,
                (
                    SELECT dam2.entity_id
                    FROM derived_attribute_mapping dam2
                    WHERE dam2.derived_attribute_id = a.id
                    ORDER BY dam2.entity_id
                    LIMIT 1
                )
            ) as entity_id
        FROM attributes a
        LEFT JOIN base_attribute_info bai ON a.id = bai.attribute_id
        LEFT JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
        LEFT JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        WHERE a.id = $1
    `

	var result struct {
		AttributeType        dtos.AttributeType `db:"attribute_type"`
		ColumnName           *string            `db:"query_column_name"`
		BaseDataType         *string            `db:"base_data_type"`
		Expression           *string            `db:"expression"`
		DerivedDataType      *string            `db:"derived_data_type"`
		Category             string             `db:"attribute_category"`
		DerivedAttributeType *string            `db:"derived_attribute_type"`
		EntityId             *int64             `db:"entity_id"`
	}

	err := c.PostgresClient.GetContext(ctx, &result, query, attributeId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("attribute not found")
		}
		return nil, fmt.Errorf("error fetching attribute details: %w", err)
	}

	if result.Category != "DIMENSION" {
		return nil, fmt.Errorf("only DIMENSION attributes support distinct values")
	}

	var columnExpr string
	var dataType string

	switch result.AttributeType {
	case dtos.AttributeTypeBase:
		if result.ColumnName == nil || result.BaseDataType == nil {
			return nil, fmt.Errorf("invalid base attribute configuration")
		}
		columnExpr = *result.ColumnName
		dataType = *result.BaseDataType

	case dtos.AttributeTypeDerived:
		if result.Expression == nil || result.DerivedDataType == nil {
			return nil, fmt.Errorf("invalid derived attribute configuration")
		}
		if *result.DerivedAttributeType == string(dtos.DerivedAttributeTypeFunctional) {
			return nil, fmt.Errorf("functional derived attributes are not supported")
		}

		// For derived attributes, use the helper function to prepare the expression
		preparedExpr, err := c.prepareNonFunctionalDerivedAttributeExpression(ctx, *result.Expression, attributeId, false)
		if err != nil {
			return nil, err
		}

		columnExpr = preparedExpr
		dataType = *result.DerivedDataType

	default:
		return nil, fmt.Errorf("unsupported attribute type: %s", result.AttributeType)
	}

	if result.EntityId == nil {
		return nil, fmt.Errorf("no entity ID for the attribute")
	}
	var entityId int64
	if result.EntityId != nil {
		entityId = *result.EntityId
	}
	segmentEntityDetails, err := c.EntityController.GetEntityDetails(ctx, entityId)
	if err != nil {
		return nil, fmt.Errorf("error getting Starrocks table name: %w", err)
	}

	distinctValues, err := c.StarrocksController.GetDistinctValuesForColumn(ctx, columnExpr, dataType, *segmentEntityDetails.CatalogName, *segmentEntityDetails.SchemaName, *segmentEntityDetails.TableName)
	if err != nil {
		return nil, fmt.Errorf("error getting distinct values: %w", err)
	}

	response := &dtos.AttributeDistinctValues{
		AttributeId:   attributeId,
		DistinctCount: distinctValues.DistinctCount,
		DataType:      distinctValues.DataType,
		Values:        distinctValues.Values,
	}

	return response, nil
}

func (c Controller) GetAttributeOwnerID(ctx context.Context, id int64) (int64, error) {
	query := `
        SELECT owner_id
        FROM attributes
        WHERE id = $1
    `

	var ownerID int64
	err := c.PostgresClient.GetContext(ctx, &ownerID, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("attribute not found")
		}
		logger.Info("Error fetching attribute owner_id from postgres", err)
		return 0, err
	}

	return ownerID, nil
}
