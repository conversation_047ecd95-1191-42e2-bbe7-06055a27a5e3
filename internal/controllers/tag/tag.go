package tagcontroller

import (
	"context"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	log "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) CreateTag(ctx context.Context, tagDTO *dtos.Tag) (*dtos.Tag, error) {
	query := `INSERT INTO tags (name) VALUES (:name) RETURNING id, created_at, updated_at`

	tagEntity := &postgres.Tag{
		Name: tagDTO.Name,
	}

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, tagEntity)
	if err != nil {
		log.Info("Error in adding tag in postgres ", err)
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&tagDTO.Id, &tagDTO.CreatedAt, &tagDTO.UpdatedAt)
		if err != nil {
			log.Info("Error scanning returned values", err)
			return nil, err
		}
	}

	return tagDTO, nil
}

func (c Controller) ListTags(ctx context.Context) (*dtos.TagList, error) {
	query := `SELECT name FROM tags ORDER BY name`

	var tags []string
	err := c.PostgresClient.SelectContext(ctx, &tags, query)
	if err != nil {
		log.Info("Error fetching tags from postgres", err)
		return nil, err
	}

	return &dtos.TagList{Tags: tags}, nil
}
