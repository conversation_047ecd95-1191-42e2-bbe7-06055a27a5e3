package datasourceconfigcontroller

import (
	"context"
	"encoding/json"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	logger "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) AddDataSourceConfig(ctx context.Context, config *dtos.DataSourceConfig) (*dtos.DataSourceConfig, error) {
	// Convert the map[string]bool to json.RawMessage
	configFieldsJSON, err := json.Marshal(config.ConfigFields)
	if err != nil {
		logger.Info("Error marshaling config fields", err)
		return nil, err
	}

	query := `INSERT INTO data_source_configs(
        data_source_type,
        config_fields
    )
    VALUES(
        :data_source_type,
        :config_fields
    ) ON CONFLICT (data_source_type) DO UPDATE
    SET config_fields = EXCLUDED.config_fields,
        updated_at = CURRENT_TIMESTAMP
    RETURNING id, created_at, updated_at`

	configEntity := &postgres.DataSourceConfig{
		DataSourceType: config.DataSourceType,
		ConfigFields:   configFieldsJSON,
	}

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, configEntity)
	if err != nil {
		logger.Info("Error in adding/updating data source config in postgres ", err)
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&config.Id, &config.CreatedAt, &config.UpdatedAt)
		if err != nil {
			logger.Info("Error scanning returned values", err)
			return nil, err
		}
	}

	return config, nil
}

func (c Controller) GetAllDataSourceTypes(ctx context.Context) ([]string, error) {
	var types []string
	err := c.PostgresClient.SelectContext(ctx, &types, `SELECT data_source_type FROM data_source_configs`)
	if err != nil {
		logger.Info("Error fetching data source types from postgres", err)
		return nil, err
	}

	return types, nil
}

func (c Controller) GetConfigFields(ctx context.Context, dataSourceType string) (map[string]bool, error) {
	var configFieldsJSON json.RawMessage
	err := c.PostgresClient.GetContext(ctx, &configFieldsJSON, `SELECT config_fields FROM data_source_configs WHERE data_source_type = $1`, dataSourceType)
	if err != nil {
		logger.Info("Error fetching config fields from postgres", err)
		return nil, err
	}

	var configFields map[string]bool
	err = json.Unmarshal(configFieldsJSON, &configFields)
	if err != nil {
		logger.Info("Error unmarshaling config fields", err)
		return nil, err
	}

	return configFields, nil
}
