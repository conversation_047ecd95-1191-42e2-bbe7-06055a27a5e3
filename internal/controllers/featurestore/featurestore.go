package featurestorecontroller

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	destinationcontroller "github.com/Zomato/cdp-platform/internal/controllers/destinations"
	usercontroller "github.com/Zomato/cdp-platform/internal/controllers/user"
	featurestore "github.com/Zomato/cdp-platform/pkg/feature-store"
	log "github.com/Zomato/go/logger"
	"github.com/lib/pq"
)

type Controller struct {
	*basecontroller.Controller
	FeatureStoreClient    *featurestore.Client
	DestinationController *destinationcontroller.Controller
	UserController        *usercontroller.Controller
}

const COMPLETION_THRESHOLD = 95

func (c Controller) CreateFeature(ctx context.Context, request *dtos.CreateFeatureRequest, ownerID, destinationID *int64) (*dtos.CreateFeatureResponse, error) {
	c.applyCreateFeatureDefaults(request)
	config, err := c.DestinationController.GetDestinationConfigById(ctx, *destinationID)
	if err != nil {
		return nil, err
	}
	user, err := c.UserController.GetUserByID(ctx, *ownerID)
	if err != nil {
		return nil, err
	}
	featureStoreConfig := &dtos.FeatureStoreConfig{}
	err = json.Unmarshal(config.ConfigMap, featureStoreConfig)
	if err != nil {
		return nil, err
	}
	featureRequest := featurestore.Feature{
		Tenant:       featureStoreConfig.Tenant,
		Namespace:    featureStoreConfig.Namespace,
		CreatedBy:    featurestore.User{User: user.Name, Email: user.Email},
		IsActive:     *request.IsActive,
		ContextType:  *request.ContextType,
		ContextName:  *request.ContextName,
		Entity:       featurestore.DefaultEntity,
		FeatureName:  *request.FeatureName,
		FeatureType:  *request.FeatureType,
		IsVerified:   *request.IsVerified,
		IsPII:        *request.IsPII,
		Destinations: *request.Destinations,
		Description:  *request.Description,
		Expiry:       *request.Expiry,
	}

	err = c.FeatureStoreClient.CreateFeature(ctx, featureRequest)
	if err != nil {
		log.Error("error creating feature in feature store", err)
		return nil, fmt.Errorf("failed to create feature in feature store: %w", err)
	}
	// Create a map for the named parameters
	params := map[string]interface{}{
		"context_name": featureRequest.ContextName,
		"feature_name": featureRequest.FeatureName,
		"feature_type": featureRequest.FeatureType,
		"is_verified":  featureRequest.IsVerified,
		"is_pii":       featureRequest.IsPII,
		"destinations": pq.Array(*request.Destinations), // Use pq.Array for the string slice
		"description":  featureRequest.Description,
		"expiry":       featureRequest.Expiry,
		"tenant":       featureStoreConfig.Tenant,
		"namespace":    featureStoreConfig.Namespace,
		"entity":       featurestore.DefaultEntity,
		"context_type": featureRequest.ContextType,
		"owner_id":     ownerID,
		"is_active":    featureRequest.IsActive,
	}

	query := `INSERT INTO feature_store_segments (
				context_name,
				feature_name,
				feature_type,
				is_verified,
				is_pii,
				destinations,
				description,
				expiry,
				tenant,
				namespace,
				entity,
				context_type,
				owner_id,
				is_active
			) VALUES (
				:context_name,
				:feature_name,
				:feature_type,
				:is_verified,
				:is_pii,
				:destinations,
				:description,
				:expiry,
				:tenant,
				:namespace,
				:entity,
				:context_type,
				:owner_id,
				:is_active
		) RETURNING id
		`

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, params)
	if err != nil {
		log.Info("Error inserting feature in postgres", err)
		return nil, fmt.Errorf("failed to insert feature in postgres: %w", err)
	}
	defer rows.Close()
	var featureId int64
	if rows.Next() {
		err = rows.Scan(&featureId)
		if err != nil {
			log.Info("Error scanning returned values", err)
			return nil, err
		}
	}
	return &dtos.CreateFeatureResponse{
		Status:    "success",
		Message:   "Feature created successfully",
		FeatureID: featureId,
	}, nil
}

func (c Controller) applyCreateFeatureDefaults(createFeatureRequest *dtos.CreateFeatureRequest) {
	if createFeatureRequest.IsActive == nil {
		isActive := featurestore.DefaultIsActive
		createFeatureRequest.IsActive = &isActive
	}
	if createFeatureRequest.ContextType == nil {
		contextType := featurestore.DefaultContextType
		createFeatureRequest.ContextType = &contextType
	}
	if createFeatureRequest.ContextName == nil {
		contextName := featurestore.DefaultContextName
		createFeatureRequest.ContextName = &contextName
	}
	if createFeatureRequest.FeatureType == nil {
		featureType := featurestore.DefaultFeatureType
		createFeatureRequest.FeatureType = &featureType
	}
	if createFeatureRequest.IsVerified == nil {
		isVerified := featurestore.DefaultIsVerified
		createFeatureRequest.IsVerified = &isVerified
	}
	if createFeatureRequest.IsPII == nil {
		isPII := featurestore.DefaultIsPII
		createFeatureRequest.IsPII = &isPII
	}
	if createFeatureRequest.Destinations == nil {
		destinations := []string{}
		createFeatureRequest.Destinations = &destinations
	}
}

func (c Controller) GetFeature(ctx context.Context, contextName, featureName string, destinationID int64) (*postgres.FeatureStoreSegment, error) {
	config, err := c.DestinationController.GetDestinationConfigById(ctx, destinationID)
	if err != nil {
		return nil, err
	}
	if config.DestinationType != "FEATURE_STORE" {
		return nil, fmt.Errorf("destination is not a feature store")
	}

	featureStoreConfig := &dtos.FeatureStoreConfig{}
	err = json.Unmarshal(config.ConfigMap, featureStoreConfig)
	if err != nil {
		return nil, err
	}

	// Create a feature object to store the result
	feature := &postgres.FeatureStoreSegment{}

	// Use GetContext to fetch a single row and populate the feature object with specific columns
	err = c.PostgresClient.GetContext(
		ctx,
		feature,
		`SELECT
			tenant,
			namespace,
			entity,
			context_name,
			context_type,
			feature_name,
			feature_type,
			is_verified,
			is_pii,
			destinations,
			description,
			expiry,
			owner_id,
			is_active,
			created_at,
			updated_at
		FROM feature_store_segments
		WHERE tenant = $1
		AND namespace = $2
		AND entity = $3
		AND context_name = $4
		AND feature_name = $5`,
		featureStoreConfig.Tenant,
		featureStoreConfig.Namespace,
		featurestore.DefaultEntity,
		contextName,
		featureName,
	)

	if err != nil {
		log.Info("Error getting feature from postgres", err)
		return nil, fmt.Errorf("failed to get feature from postgres: %w", err)
	}

	return feature, nil
}

func (c Controller) GetCompletionStatus(ctx context.Context, runID string) (bool, error) {
	observabilityResponse, err := c.FeatureStoreClient.GetObservabilityStatus(ctx, runID)
	if err != nil {
		return false, fmt.Errorf("failed to get completion status: %w", err)
	}
	if observabilityResponse.Data.ExpectedCount == 0 {
		return false, nil
	}
	completionPercentage := (float64(observabilityResponse.Data.SuccessCount) / float64(observabilityResponse.Data.ExpectedCount)) * 100
	return completionPercentage >= COMPLETION_THRESHOLD, nil
}

// GetFeatureNameByDestinationSyncID retrieves the feature name associated with a destination sync ID
func (c Controller) GetFeatureNameByDestinationSyncID(ctx context.Context, destinationSyncID int64) (string, error) {
	query := `
        SELECT fs.feature_name
        FROM feature_store_segments fs
        JOIN feature_store_sync_mapping fsm ON fsm.feature_id = fs.id
        WHERE fsm.destination_sync_id = $1
        LIMIT 1
    `

	var featureName string
	err := c.PostgresClient.GetContext(ctx, &featureName, query, destinationSyncID)
	if err != nil {
		log.Info("Error getting feature name from destination sync ID", err)
		return "", fmt.Errorf("failed to get feature name from destination sync ID: %w", err)
	}

	return featureName, nil
}

// ValidateFeatureExists checks if a feature exists in the feature store
func (c Controller) ValidateFeatureExists(ctx context.Context, featureName string, destinationID int64) (bool, error) {
	config, err := c.DestinationController.GetDestinationConfigById(ctx, destinationID)
	if err != nil {
		return false, fmt.Errorf("failed to get destination config: %w", err)
	}

	if config.DestinationType != "FEATURE_STORE" {
		return false, fmt.Errorf("destination is not a feature store")
	}

	featureStoreConfig := &dtos.FeatureStoreConfig{}
	err = json.Unmarshal(config.ConfigMap, featureStoreConfig)
	if err != nil {
		return false, fmt.Errorf("failed to unmarshal feature store config: %w", err)
	}

	// TODO: move entity and contexts as input when requirement comes, using constants from feature store package for now
	_, err = c.FeatureStoreClient.GetFeature(ctx, featurestore.DefaultEntity, featurestore.DefaultContextName, featureName, featureStoreConfig.Tenant, featureStoreConfig.Namespace)
	if err != nil {
		// If feature doesn't exist, return false (which means validation passes - feature is available)
		return false, nil
	}

	// Feature exists, return true
	return true, nil
}

func (c Controller) UpdateFeature(ctx context.Context, contextName, featureName string, request *dtos.UpdateFeatureRequest, destinationID int64) (*dtos.UpdateFeatureResponse, error) {
	// Get destination config
	config, err := c.DestinationController.GetDestinationConfigById(ctx, destinationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get destination config: %w", err)
	}

	if config.DestinationType != "FEATURE_STORE" {
		return nil, fmt.Errorf("destination is not a feature store")
	}

	featureStoreConfig := &dtos.FeatureStoreConfig{}
	err = json.Unmarshal(config.ConfigMap, featureStoreConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal feature store config: %w", err)
	}

	// Get user info if updatedBy is provided
	var updatedByUser featurestore.User
	if request.UpdatedByUserID != nil {
		user, err := c.UserController.GetUserByID(ctx, *request.UpdatedByUserID)
		if err != nil {
			return nil, fmt.Errorf("failed to get user: %w", err)
		}
		updatedByUser = featurestore.User{User: user.Name, Email: user.Email}
	}

	// Create update request for feature store
	updateRequest := featurestore.FeatureUpdateRequest{
		IsActive:  request.IsActive,
		Expiry:    request.Expiry,
		UpdatedBy: updatedByUser,
	}

	if request.CreatedAt != nil {
		updateRequest.CreatedAt = request.CreatedAt
	}

	// Update feature in external feature store
	err = c.FeatureStoreClient.UpdateFeature(ctx, featurestore.DefaultEntity, contextName, featureName, featureStoreConfig.Tenant, featureStoreConfig.Namespace, updateRequest)
	if err != nil {
		log.Error("error updating feature in feature store", err)
		return nil, fmt.Errorf("failed to update feature in feature store: %w", err)
	}

	// Update feature in local postgres database
	updateFields := []string{}
	params := map[string]interface{}{
		"tenant":       featureStoreConfig.Tenant,
		"namespace":    featureStoreConfig.Namespace,
		"entity":       featurestore.DefaultEntity,
		"context_name": contextName,
		"feature_name": featureName,
	}

	if request.IsActive != nil {
		updateFields = append(updateFields, "is_active = :is_active")
		params["is_active"] = *request.IsActive
	}

	if request.Expiry != nil {
		updateFields = append(updateFields, "expiry = :expiry")
		params["expiry"] = *request.Expiry
	}

	if len(updateFields) == 0 {
		return &dtos.UpdateFeatureResponse{
			Status:  "success",
			Message: "No fields to update",
		}, nil
	}

	query := fmt.Sprintf(`UPDATE feature_store_segments SET %s 
                         WHERE tenant = :tenant 
                         AND namespace = :namespace 
                         AND entity = :entity 
                         AND context_name = :context_name 
                         AND feature_name = :feature_name`,
		strings.Join(updateFields, ", "))

	_, err = c.PostgresClient.NamedExecContext(ctx, query, params)
	if err != nil {
		log.Info("Error updating feature in postgres", err)
		return nil, fmt.Errorf("failed to update feature in postgres: %w", err)
	}

	return &dtos.UpdateFeatureResponse{
		Status:  "success",
		Message: "Feature updated successfully",
	}, nil
}

// UpdateLogTableName updates the log table name for a feature store segment
func (c Controller) UpdateLogTableName(ctx context.Context, destinationSyncID int64, logTableName string) error {
	query := `
		UPDATE feature_store_segments 
		SET log_table_name = $1, updated_at = CURRENT_TIMESTAMP
		WHERE id = (
			SELECT fsm.feature_id 
			FROM feature_store_sync_mapping fsm 
			WHERE fsm.destination_sync_id = $2
		)
	`

	_, err := c.PostgresClient.ExecContext(ctx, query, logTableName, destinationSyncID)
	if err != nil {
		return fmt.Errorf("failed to update log table name: %w", err)
	}

	return nil
}
