package configcontroller

import (
	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
)

type Controller struct {
	*basecontroller.Controller
}

func (controller Controller) GetConfig() (dtos.Config, error) {
	return dtos.Config{
		AppsflyerKafkaTopic:      controller.AppsflyerKafkaTopic,
		StarrocksDefaultCatalog:  controller.StarrocksDefaultCatalog,
		StarrocksDefaultDatabase: controller.StarrocksDefaultDatabase,
		StarrocksHiveCatalog:     controller.StarrocksHiveCatalog,
		StarrocksHiveDatabase:    controller.StarrocksHiveDatabase,
	}, nil
}

func (controller Controller) GetEnvironment() (string, error) {
	return controller.ServiceEnvironment, nil
}

func (controller Controller) GetOrg() (string, error) {
	return controller.Controller.OrgName, nil
}
