package destinationsyncrunspartitioncontroller

import (
	"context"
	"time"

	"errors"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	kafkacontroller "github.com/Zomato/cdp-platform/internal/controllers/kafka"
)

type Controller struct {
	*basecontroller.Controller
	KafkaController *kafkacontroller.Controller
}

func (controller *Controller) InsertDestinationSyncPartitionStatusAll(ctx context.Context, destinationSyncRunID int64, kafkaPartitions []int32) error {
	for _, partition := range kafkaPartitions {
		statusDTO := &dtos.DestinationSyncPartitionStatus{
			DestinationSyncRunID: destinationSyncRunID,
			PartitionNumber:      partition,
			Status:               "PENDING",
		}

		err := controller.InsertDestinationSyncPartitionStatus(ctx, statusDTO)
		if err != nil {
			return err
		}
	}
	return nil
}

func (controller *Controller) SendDummyMessageAllPartitions(ctx context.Context, destinationSyncRunID int64, kafkaTopic string, kafkaPartitions []int32) error {
	for _, partition := range kafkaPartitions {
		dummyMessage := map[string]interface{}{
			"destination_sync_run_id": destinationSyncRunID,
			"ts":                      time.Now().Unix(),
			"partition":               partition,
		}

		if err := controller.KafkaProducer.SendMessageToPartition("", dummyMessage, partition, kafkaTopic); err != nil {
			return err
		}
	}
	return nil
}

func (controller *Controller) InsertDestinationSyncPartitionStatus(ctx context.Context, destinationSyncPartitionStatusDTO *dtos.DestinationSyncPartitionStatus) error {
	query := `INSERT INTO destination_sync_partition_status (
		destination_sync_run_id, partition_number, status
	) VALUES (
		$1, $2, $3
	) ON CONFLICT (destination_sync_run_id, partition_number) DO NOTHING`

	_, err := controller.PostgresClient.ExecContext(ctx, query,
		destinationSyncPartitionStatusDTO.DestinationSyncRunID, destinationSyncPartitionStatusDTO.PartitionNumber, destinationSyncPartitionStatusDTO.Status)
	return err
}

func (controller *Controller) UpdateDestinationSyncPartitionStatus(ctx context.Context, destinationSyncPartitionStatusDTO *dtos.DestinationSyncPartitionStatus) error {
	query := `UPDATE destination_sync_partition_status
	SET status = $1, updated_at = CURRENT_TIMESTAMP
	WHERE destination_sync_run_id = $2 AND partition_number = $3`

	result, err := controller.PostgresClient.ExecContext(ctx, query,
		destinationSyncPartitionStatusDTO.Status, destinationSyncPartitionStatusDTO.DestinationSyncRunID, destinationSyncPartitionStatusDTO.PartitionNumber)
	if err != nil {
		return err
	}

	numRows, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if numRows == 0 {
		return errors.New("no rows updated, invalid destination_sync_run_id or partition_number")
	}
	return nil
}

func (controller *Controller) CheckAllPartitionDone(ctx context.Context, destinationSyncPartitionStatusDTO *dtos.DestinationSyncPartitionStatus) (bool, error) {
	// Query to check if any partition is still PENDING.
	pendingQuery := `SELECT COUNT(1) FROM destination_sync_partition_status
	WHERE destination_sync_run_id = $1 AND status = 'PENDING'`

	var pendingCount int
	err := controller.PostgresClient.GetContext(ctx, &pendingCount, pendingQuery, destinationSyncPartitionStatusDTO.DestinationSyncRunID)
	if err != nil {
		return false, err
	}

	if pendingCount > 0 {
		return false, nil
	}
	return true, nil
}
