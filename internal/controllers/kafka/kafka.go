package kafkacontroller

import (
	"context"
	"fmt"

	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
)

type Controller struct {
	*basecontroller.Controller
}

func (controller Controller) ProduceToKafka(ctx context.Context, batchPayloadChan chan *appsflyer.AudienceRequest, kafkaTopic string) error {

	for payload := range batchPayloadChan {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context canceled: %w", ctx.Err())
		default:
			if err := controller.KafkaProducer.SendMessage("", &payload, kafkaTopic); err != nil {
				return fmt.Errorf("failed to send message: %w", err)
			}
		}
	}

	return nil
}

func (controller *Controller) GetAllPartitions(ctx context.Context, kafkaTopic string) ([]int32, error) {
	if kafkaTopic == "" {
		return nil, fmt.Errorf("topic must be specified")
	}

	partitions, err := controller.KafkaProducer.ManualProducerClient.Partitions(kafkaTopic)
	if err != nil {
		return nil, fmt.Errorf("failed to get partition count: %w", err)
	}

	return partitions, nil
}
