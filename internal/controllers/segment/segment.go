package segmentcontroller

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	entity "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	attributecontroller "github.com/Zomato/cdp-platform/internal/controllers/attributes"
	entitycontroller "github.com/Zomato/cdp-platform/internal/controllers/entity"
	log "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	AttributesController *attributecontroller.Controller
	EntityController     *entitycontroller.Controller
}

func (c Controller) AddSegment(ctx context.Context, segmentDTO *dtos.Segment) error {
	// Start transaction
	tx, err := c.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Insert into segments table
	query := `
        INSERT INTO segments (
            name,
            description,
            conditions,
            owner_id,
            created_at,
            updated_at
        ) VALUES (
            :name,
            :description,
            :conditions,
            :owner_id,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        ) RETURNING id, created_at, updated_at`

	// First marshal the DTO condition to JSON
	conditionsJSON, err := json.Marshal(segmentDTO.Conditions)
	if err != nil {
		return fmt.Errorf("error marshaling conditions: %w", err)
	}

	// Then unmarshal into the entity condition
	var entityCondition entity.Condition
	if err := json.Unmarshal(conditionsJSON, &entityCondition); err != nil {
		return fmt.Errorf("error unmarshaling conditions: %w", err)
	}

	segmentEntity := &entity.Segment{
		Name:        segmentDTO.Name,
		Description: segmentDTO.Description,
		Conditions:  entityCondition,
		OwnerID:     segmentDTO.OwnerID,
	}

	rows, err := tx.NamedQuery(query, segmentEntity)
	if err != nil {
		return fmt.Errorf("error inserting segment: %w", err)
	}

	if !rows.Next() {
		return errors.New("no id returned from segment insert")
	}

	err = rows.Scan(&segmentDTO.Id, &segmentDTO.CreatedAt, &segmentDTO.UpdatedAt)
	if err != nil {
		return fmt.Errorf("error scanning returned values: %w", err)
	}

	rows.Close()

	// Extract attribute IDs from conditions and create mappings
	attributeIDs := extractAttributeIDs(segmentDTO.Conditions)

	// Insert attribute mappings
	if len(attributeIDs) > 0 {
		mappingQuery := `
            INSERT INTO segment_attribute_mapping (
                segment_id,
                attribute_id
            ) VALUES (
                :segment_id,
                :attribute_id
            )`

		for _, attrID := range attributeIDs {
			mapping := map[string]interface{}{
				"segment_id":   segmentDTO.Id,
				"attribute_id": attrID,
			}

			_, err = tx.NamedExec(mappingQuery, mapping)
			if err != nil {
				return fmt.Errorf("error inserting segment-attribute mapping: %w", err)
			}
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (c Controller) GetLastRefreshDate(ctx context.Context, segmentId int64) (*time.Time, error) {
	query := `
        WITH AttributeLastRefresh AS (
            SELECT 
                sam.segment_id,
                sam.attribute_id,
                (
                    SELECT execution_end
                    FROM attribute_sync_runs asr
                    JOIN attribute_queries aq ON aq.id = asr.attribute_query_id
                    JOIN attributes a ON a.id = sam.attribute_id
                    WHERE asr.status = 'SUCCESSFUL'
                    ORDER BY asr.execution_end DESC
                    LIMIT 1
                ) as last_attribute_refresh
            FROM segment_attribute_mapping sam
            WHERE sam.segment_id = $1
        )
        SELECT MIN(last_attribute_refresh) as last_refresh
        FROM AttributeLastRefresh`

	var lastRefresh *time.Time
	err := c.PostgresClient.GetContext(ctx, &lastRefresh, query, segmentId)
	if err != nil {
		return nil, fmt.Errorf("error fetching last refresh date: %w", err)
	}

	return lastRefresh, nil
}

func (c Controller) GetAllSegments(ctx context.Context) ([]dtos.Segment, error) {
	query := `
        SELECT 
            s.id,
            s.name,
            s.description,
            s.created_at,
            s.updated_at,
            COALESCE(u.name, '') as owner_name
        FROM segments s
        LEFT JOIN users u ON s.owner_id = u.id
        ORDER BY s.updated_at DESC`

	segments := []dtos.Segment{}
	err := c.PostgresClient.SelectContext(ctx, &segments, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching segments: %w", err)
	}

	return segments, nil
}

func (c Controller) GetSegmentOverview(ctx context.Context, segmentId int64) (*dtos.Segment, error) {
	query := `
        SELECT 
            s.name,
            s.description,
            s.conditions,
			s.owner_id,
            s.created_at,
            s.updated_at,
            u.name as owner_name
        FROM segments s
        LEFT JOIN users u ON s.owner_id = u.id
        WHERE s.id = $1`

	segment := &dtos.Segment{}
	err := c.PostgresClient.GetContext(ctx, segment, query, segmentId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("segment with ID %d not found", segmentId)
		}
		return nil, fmt.Errorf("error fetching segment: %w", err)
	}

	lastRefresh, err := c.GetLastRefreshDate(ctx, segmentId)
	if err != nil {
		log.Info("Error fetching last refresh date for segment", "segmentId", segmentId, "error", err)
	} else {
		segment.LastRefresh = lastRefresh
	}

	return segment, nil
}

func (c Controller) GetDownstreamDestinationsOfSegment(ctx context.Context, segmentId int64) ([]dtos.SegmentDownstreamDestinationsResponse, error) {
	query := `
        SELECT 
            d.destination_type,
            d.name as destination_name,
            COALESCE(afa.name, f.feature_name) as audience_name
        FROM segments s
        JOIN destination_syncs ds ON ds.segment_id = s.id
        JOIN destinations d ON d.id = ds.destination_id
        LEFT JOIN appsflyer_sync_mappings asm ON asm.destination_sync_id = ds.id
        LEFT JOIN appsflyer_audiences afa ON afa.id = asm.audience_id
        LEFT JOIN feature_store_sync_mapping fsm ON fsm.destination_sync_id = ds.id
        LEFT JOIN feature_store_segments f ON f.id = fsm.feature_id AND f.is_active = true
        WHERE s.id = $1
        ORDER BY d.name, COALESCE(afa.name, f.feature_name)`

	destinations := []dtos.SegmentDownstreamDestinationsResponse{}
	err := c.PostgresClient.SelectContext(ctx, &destinations, query, segmentId)
	if err != nil {
		if err == sql.ErrNoRows {
			// Return empty slice instead of error when no destinations found
			return []dtos.SegmentDownstreamDestinationsResponse{}, nil
		}
		return nil, fmt.Errorf("error fetching downstream destinations: %w", err)
	}

	return destinations, nil
}

func (c Controller) UpdateSegment(ctx context.Context, segment *dtos.Segment) error {
	// Basic validation
	if segment == nil {
		return errors.New("segment cannot be nil")
	}
	if segment.Id == 0 {
		return errors.New("segment ID is required")
	}

	// Start transaction
	tx, err := c.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Check if the new name already exists (only if name is being updated)
	if segment.Name != "" {
		var existingCount int
		checkQuery := `SELECT COUNT(*) FROM segments WHERE name = $1 AND id != $2`
		err := tx.QueryRowContext(ctx, checkQuery, segment.Name, segment.Id).Scan(&existingCount)
		if err != nil {
			return fmt.Errorf("error checking segment name: %w", err)
		}
		if existingCount > 0 {
			return errors.New("segment name already exists")
		}
	}

	// Build update query
	query := "UPDATE segments SET updated_at = NOW()"
	args := map[string]interface{}{"id": segment.Id}

	var updates []string
	if segment.Name != "" {
		updates = append(updates, "name = :name")
		args["name"] = segment.Name
	}
	if segment.Description != "" {
		updates = append(updates, "description = :description")
		args["description"] = segment.Description
	}
	if segment.Conditions != nil {
		updates = append(updates, "conditions = :conditions")

		// Convert dtos.Condition to entity.Condition with proper JSON serialization
		conditionsJSON, err := json.Marshal(segment.Conditions)
		if err != nil {
			return fmt.Errorf("error marshaling conditions: %w", err)
		}

		var entityCondition entity.Condition
		if err := json.Unmarshal(conditionsJSON, &entityCondition); err != nil {
			return fmt.Errorf("error unmarshaling conditions to entity: %w", err)
		}

		// Pass the entity condition which implements proper SQL driver interfaces
		args["conditions"] = entityCondition

		// Update segment-attribute mappings if conditions are being updated
		// First, delete existing mappings
		_, err = tx.Exec(`DELETE FROM segment_attribute_mapping WHERE segment_id = $1`, segment.Id)
		if err != nil {
			return fmt.Errorf("error deleting existing attribute mappings: %w", err)
		}

		// Extract attribute IDs from new conditions and create new mappings
		attributeIDs := extractAttributeIDs(segment.Conditions)
		if len(attributeIDs) > 0 {
			for _, attrID := range attributeIDs {
				_, err = tx.Exec(`
                    INSERT INTO segment_attribute_mapping (segment_id, attribute_id)
                    VALUES ($1, $2)
                `, segment.Id, attrID)
				if err != nil {
					return fmt.Errorf("error inserting segment-attribute mapping: %w", err)
				}
			}
		}
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += ", " + strings.Join(updates, ", ") + " WHERE id = :id"

	// Execute update within transaction
	result, err := tx.NamedExec(query, args)
	if err != nil {
		return fmt.Errorf("error updating segment: %w", err)
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("error getting rows affected: %w", err)
	}
	if rowsAffected == 0 {
		return errors.New("segment not found")
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (c Controller) GetSegmentSize(ctx context.Context, condition *dtos.Condition) (*dtos.SegmentSize, error) {
	// Check if condition contains functional attributes
	hasFunctional, err := c.hasFunctionalAttributes(ctx, condition)
	if err != nil {
		return nil, fmt.Errorf("error checking for functional attributes: %w", err)
	}

	if hasFunctional {
		// Handle complex query with functional attributes
		return c.getSegmentSizeWithFunctionalAttributes(ctx, condition)
	}
	return c.getSegmentSizeWithoutFunctionalAttributes(ctx, condition)
}

func (c Controller) GetQueryWithConditions(ctx context.Context, condition *dtos.Condition, tgCgCondition *dtos.Condition, outputColumns []string) (string, error) {
	// Check if segment conditions contain functional attributes
	hasFunctional, err := c.hasFunctionalAttributes(ctx, condition)
	if err != nil {
		return "", fmt.Errorf("error checking for functional attributes: %w", err)
	}

	if hasFunctional {
		// Handle complex query with functional attributes
		return c.getQueryWithFunctionalAttributes(ctx, condition, tgCgCondition, outputColumns)
	}
	return c.getQueryWithoutFunctionalAttributes(ctx, condition, tgCgCondition, outputColumns)
}

func (c Controller) GetSegmentSizeTgCg(ctx context.Context, request *dtos.SegmentSizeTgCgRequest) (*dtos.SegmentSizeTgCgResponse, error) {
	query := `SELECT conditions FROM segments WHERE id = $1`
	var originalCondition dtos.Condition
	err := c.PostgresClient.GetContext(ctx, &originalCondition, query, request.Id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("segment with ID %d not found", request.Id)
		}
		return nil, fmt.Errorf("error fetching segment conditions: %w", err)
	}

	// Get size with original conditions
	originalSize, err := c.GetSegmentSize(ctx, &originalCondition)
	if err != nil {
		return nil, fmt.Errorf("error getting original segment size: %w", err)
	}

	AndLogicalOperator := "AND"
	// Create a new complex condition combining original and additional conditions
	combinedCondition := dtos.Condition{
		Type:            "complex",
		LogicalOperator: &AndLogicalOperator,
		Conditions:      []dtos.Condition{originalCondition, *request.TgCgConditions}, // ! Make sure TgCgConditions is sent properly with Entity Fields from frontend
	}

	// Get size with combined conditions
	filteredSize, err := c.GetSegmentSize(ctx, &combinedCondition)
	if err != nil {
		return nil, fmt.Errorf("error getting filtered segment size: %w", err)
	}

	// Calculate coverage percentage
	var coveragePercent float64
	if originalSize.Count > 0 {
		coveragePercent = float64(filteredSize.Count) / float64(originalSize.Count) * 100
	}

	return &dtos.SegmentSizeTgCgResponse{
		OriginalSize:    originalSize.Count,
		FilteredSize:    filteredSize.Count,
		CoveragePercent: coveragePercent,
	}, nil
}

func (c Controller) GetSegmentSizeById(ctx context.Context, segmentId int64) (*dtos.SegmentSize, error) {
	// First get the segment conditions from postgres
	query := `SELECT conditions FROM segments WHERE id = $1`

	var condition dtos.Condition
	err := c.PostgresClient.GetContext(ctx, &condition, query, segmentId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("segment with ID %d not found", segmentId)
		}
		return nil, fmt.Errorf("error fetching segment conditions: %w", err)
	}

	// Use existing GetSegmentSize method to calculate size
	return c.GetSegmentSize(ctx, &condition)
}

func (c Controller) GetAllSegmentNames(ctx context.Context) ([]dtos.Segment, error) {
	query := `SELECT s.id, s.name FROM segments s`

	var segments []dtos.Segment
	err := c.PostgresClient.SelectContext(ctx, &segments, query)
	if err != nil {
		log.Info("Error fetching segments from postgres", err)
		return nil, err
	}
	return segments, nil
}

func (c Controller) GetUpstreamAttributeInfo(ctx context.Context, segmentId int64) ([]dtos.AttributeInfo, error) {
	query := `
        SELECT
            a.id,
            a.name,
			a.attribute_type,
			a.description,
            CASE 
                WHEN a.attribute_type = 'BASE' THEN aq.cron_expression
                ELSE NULL
            END as cron_expression,
            CASE
                WHEN a.attribute_type = 'DERIVED' THEN dai.readable_expression
                ELSE NULL
            END as readable_expression
        FROM segments s
        JOIN segment_attribute_mapping sam ON s.id = sam.segment_id
        JOIN attributes a ON sam.attribute_id = a.id
        LEFT JOIN base_attribute_info bai ON a.id = bai.attribute_id
        LEFT JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
        LEFT JOIN derived_attribute_info dai ON a.id = dai.attribute_id
        WHERE s.id = $1
        ORDER BY a.name`

	var attributeInfos []dtos.AttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &attributeInfos, query, segmentId)
	if err != nil {
		if err == sql.ErrNoRows {
			return []dtos.AttributeInfo{}, nil
		}
		return nil, fmt.Errorf("error fetching upstream attribute info: %w", err)
	}

	return attributeInfos, nil
}

func (c Controller) GetSegmentOwnerID(ctx context.Context, id int64) (int64, error) {
	var ownerID int64
	query := `SELECT owner_id FROM segments WHERE id = $1`
	err := c.PostgresClient.GetContext(ctx, &ownerID, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("segment not found")
		}
		return 0, fmt.Errorf("failed to get segment owner ID: %w", err)
	}
	return ownerID, nil
}
