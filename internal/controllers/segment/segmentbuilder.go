package segmentcontroller

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	log "github.com/Zomato/go/logger"
)

const USER_ENTITY_ID = 1

// Helper function to recursively extract attribute IDs from conditions
func extractAttributeIDs(condition *dtos.Condition) []int64 {
	if condition == nil {
		return nil
	}

	var attributeIDs []int64

	// Check for filter attribute
	if condition.IsFilter != nil && *condition.IsFilter && condition.AttributeId != nil && *condition.AttributeId != 0 {
		attributeIDs = append(attributeIDs, *condition.AttributeId)
	}

	switch condition.Type {
	case "simple":
		if condition.AttributeId != nil && *condition.AttributeId != 0 {
			attributeIDs = append(attributeIDs, *condition.AttributeId)
		}
	case "complex":
		// Extract from nested conditions
		for _, subCondition := range condition.Conditions {
			subIDs := extractAttributeIDs(&subCondition)
			attributeIDs = append(attributeIDs, subIDs...)
		}
	}

	// Remove duplicates
	return uniqueInt64s(attributeIDs)
}

// Helper function to remove duplicates from int64 slice
func uniqueInt64s(slice []int64) []int64 {
	seen := make(map[int64]struct{})
	unique := make([]int64, 0, len(slice))

	for _, val := range slice {
		if _, exists := seen[val]; !exists {
			seen[val] = struct{}{}
			unique = append(unique, val)
		}
	}

	return unique
}

// Helper function to format values for SQL queries
func formatValueForSQL(value interface{}) (string, error) {
	var valueStr string

	switch v := value.(type) {
	case string:
		// Properly escape single quotes by doubling them
		escapedValue := strings.ReplaceAll(v, "'", "''")
		valueStr = fmt.Sprintf("'%s'", escapedValue)
	case float64, int, int64, bool:
		valueStr = fmt.Sprintf("%v", v)
	case []interface{}:
		values := make([]string, len(v))
		for i, item := range v {
			switch itemVal := item.(type) {
			case string:
				// Properly escape single quotes by doubling them
				escapedValue := strings.ReplaceAll(itemVal, "'", "''")
				values[i] = fmt.Sprintf("'%s'", escapedValue)
			case float64, int, int64, bool:
				values[i] = fmt.Sprintf("%v", itemVal)
			default:
				return "", fmt.Errorf("unsupported value type in array")
			}
		}
		valueStr = fmt.Sprintf("(%s)", strings.Join(values, ", "))
	default:
		return "", fmt.Errorf("unsupported value type")
	}

	return valueStr, nil
}

// getInvolvedEntities extracts all entity IDs involved in the condition
func (c Controller) getInvolvedEntities(ctx context.Context, condition *dtos.Condition) ([]int64, error) {
	if condition == nil {
		return nil, nil
	}

	var entityIDs []int64

	// If entity ID is directly specified
	if condition.EntityId != nil && *condition.EntityId != 0 {
		entityIDs = append(entityIDs, *condition.EntityId)
	}

	switch condition.Type {
	case "simple":
		if condition.AttributeId == nil || *condition.AttributeId == 0 {
			return nil, nil
		}

		// Get entity ID for this attribute [Assuming 1 entity_id for derived attributes]
		query := `
            SELECT COALESCE(
                (
                    SELECT entity_id FROM attribute_queries aq 
                    JOIN base_attribute_info bai ON aq.id = bai.attribute_query_id 
                    WHERE bai.attribute_id = $1
                ),
                (
                    SELECT DISTINCT aq.entity_id
                    FROM derived_attribute_mapping dam
                    JOIN attributes sa ON dam.source_attribute_id = sa.id
                    LEFT JOIN base_attribute_info bai ON sa.id = bai.attribute_id
                    LEFT JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
                    WHERE dam.derived_attribute_id = $1
                    LIMIT 1
                )
            ) as entity_id
        `
		var entityID int64
		err := c.PostgresClient.GetContext(ctx, &entityID, query, *condition.AttributeId)
		if err != nil && err != sql.ErrNoRows {
			return nil, fmt.Errorf("error getting entity ID for attribute %d: %w", *condition.AttributeId, err)
		}
		if entityID != 0 {
			entityIDs = append(entityIDs, entityID)
		}

	case "complex":
		for _, subCondition := range condition.Conditions {
			subEntityIDs, err := c.getInvolvedEntities(ctx, &subCondition)
			if err != nil {
				return nil, err
			}
			entityIDs = append(entityIDs, subEntityIDs...)
		}
	}

	result := uniqueInt64s(entityIDs)
	return result, nil
}

// hasFunctionalAttributes recursively checks if any attribute in the condition is functional
func (c Controller) hasFunctionalAttributes(ctx context.Context, condition *dtos.Condition) (bool, error) {
	if condition == nil {
		return false, nil
	}

	// Check filter attribute if present
	if condition.IsFilter != nil && *condition.IsFilter && condition.AttributeId != nil && *condition.AttributeId != 0 {
		// Check if the attribute is functional
		isFunctional, err := c.AttributesController.IsAttributeFunctional(ctx, *condition.AttributeId)
		if err != nil {
			return false, err
		}
		if isFunctional {
			return true, nil
		}
	}

	switch condition.Type {
	case "simple":
		if condition.AttributeId != nil && *condition.AttributeId != 0 {
			isFunctional, err := c.AttributesController.IsAttributeFunctional(ctx, *condition.AttributeId)
			if err != nil {
				return false, err
			}
			if isFunctional {
				return true, nil
			}
		}
	case "complex":
		for _, subCondition := range condition.Conditions {
			hasFunctional, err := c.hasFunctionalAttributes(ctx, &subCondition)
			if err != nil {
				return false, err
			}
			if hasFunctional {
				return true, nil
			}
		}
	}

	return false, nil
}

func (c Controller) ValidateFilterConditions(condition *dtos.Condition) error {
	if condition == nil {
		return nil
	}

	// If this level has a filter, check that conditions array has more than 1 element
	if condition.IsFilter != nil && *condition.IsFilter {
		if len(condition.Conditions) <= 0 {
			return fmt.Errorf("filter condition must have at least 1 sub-condition, found %d", len(condition.Conditions))
		}
	}

	// Recursively check sub-conditions
	if condition.Type == "complex" {
		for _, subCondition := range condition.Conditions {
			if err := c.ValidateFilterConditions(&subCondition); err != nil {
				return err
			}
		}
	}

	return nil
}

func (c Controller) BuildWhereClause(ctx context.Context, condition *dtos.Condition, applyPrefix bool) (string, error) {
	if condition == nil {
		return "", errors.New("invalid condition: condition is nil")
	}

	// Handle entity filter conditions
	if condition.IsFilter != nil && *condition.IsFilter && condition.AttributeId != nil && *condition.AttributeId != 0 {
		columnExpression, err := c.AttributesController.GetAttributeColumnExpression(ctx, *condition.AttributeId, applyPrefix)
		if err != nil {
			return "", fmt.Errorf("error getting filter attribute %d: %w", *condition.AttributeId, err)
		}

		// Format filter value
		valueStr, err := formatValueForSQL(condition.ConditionValue)
		if err != nil {
			return "", fmt.Errorf("error formatting filter value for attribute %d: %w", *condition.AttributeId, err)
		}

		if condition.Operator != nil {
			return fmt.Sprintf("%s %s %s", columnExpression, *condition.Operator, valueStr), nil
		}
		return "", errors.New("missing operator for filter condition")
	}

	switch condition.Type {
	case "simple":

		if condition.AttributeId == nil || *condition.AttributeId == 0 || condition.Operator == nil {
			return "", errors.New("invalid simple condition: missing attribute or operator")
		}

		columnExpression, err := c.AttributesController.GetAttributeColumnExpression(ctx, *condition.AttributeId, applyPrefix)
		if err != nil {
			return "", fmt.Errorf("error getting attribute %d: %w", *condition.AttributeId, err)
		}

		// Format filter value
		valueStr, err := formatValueForSQL(condition.ConditionValue)
		if err != nil {
			return "", fmt.Errorf("error formatting filter value for attribute %d: %w", *condition.AttributeId, err)
		}

		return fmt.Sprintf("%s %s %s", columnExpression, *condition.Operator, valueStr), nil

	case "complex":
		if len(condition.Conditions) == 0 || condition.LogicalOperator == nil {
			return "", errors.New("invalid complex condition: missing sub-conditions or logical operator")
		}

		var subClauses []string
		for _, subCondition := range condition.Conditions {

			subClause, err := c.BuildWhereClause(ctx, &subCondition, applyPrefix)
			if err != nil {
				return "", err
			}
			subClauses = append(subClauses, subClause)
		}
		return fmt.Sprintf("(%s)", strings.Join(subClauses, " "+*condition.LogicalOperator+" ")), nil

	default:
		return "", fmt.Errorf("invalid condition type: %s", condition.Type)
	}
}

func (c Controller) getTargetEntityTableNameType(ctx context.Context, condition *dtos.Condition) (string, string, string, string, error) {
	// Get involved entities to determine the correct base table
	entityIDs, err := c.getInvolvedEntities(ctx, condition)
	if err != nil {
		return "", "", "", "", fmt.Errorf("error getting involved entities: %w", err)
	}

	targetEntityID, err := c.EntityController.DetermineTargetEntity(ctx, entityIDs)
	if err != nil {
		return "", "", "", "", fmt.Errorf("error determining target entity: %w", err)
	}

	entityInfo, err := c.EntityController.GetEntityDetails(ctx, targetEntityID)
	if err != nil {
		return "", "", "", "", fmt.Errorf("error getting entity details: %w", err)
	}

	catalogName := ""
	if entityInfo.CatalogName != nil {
		catalogName = *entityInfo.CatalogName
	} else {
		catalogName = c.StarrocksDefaultCatalog
	}
	schemaName := ""
	if entityInfo.SchemaName != nil {
		schemaName = *entityInfo.SchemaName
	} else {
		schemaName = c.StarrocksDefaultDatabase
	}

	return *entityInfo.TableName, *entityInfo.Type, catalogName, schemaName, nil
}

// getSegmentSizeWithoutFunctionalAttributes retrieves the size of the segment without functional attributes
func (c Controller) getSegmentSizeWithoutFunctionalAttributes(ctx context.Context, condition *dtos.Condition) (*dtos.SegmentSize, error) {
	// Validate filter conditions
	if err := c.ValidateFilterConditions(condition); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get the entity table name
	targetTableName, targetEntityType, targetCatalogName, targetSchemaName, err := c.getTargetEntityTableNameType(ctx, condition)
	if err != nil {
		return nil, fmt.Errorf("error getting attribute prefix: %w", err)
	}

	applyPrefix := false
	if targetEntityType == string(dtos.EntityTypeDerived) {
		applyPrefix = true
	}

	whereClause, err := c.BuildWhereClause(ctx, condition, applyPrefix)
	if err != nil {
		log.Error("Error building where clause", err)
		return nil, fmt.Errorf("error building query: %w", err)
	}

	userIDField := ""
	if applyPrefix {
		userEntityInfo, err := c.EntityController.GetEntityDetails(ctx, USER_ENTITY_ID)
		if err != nil {
			log.Error("Error getting entity details", err)
			return nil, fmt.Errorf("error getting entity details: %w", err)
		}
		userIDField = fmt.Sprintf("%s_user_id", *userEntityInfo.TableName)
	} else {
		userIDField = "user_id"
	}

	query := fmt.Sprintf("SELECT COUNT(DISTINCT %s) FROM %s.%s.%s WHERE %s",
		userIDField, targetCatalogName, targetSchemaName, targetTableName, whereClause)

	log.Info("Executing query:", query)
	var count int64

	err = c.StarrocksClient.GetContext(ctx, &count, query)
	if err != nil {
		log.Error("Error executing query", err)
		return nil, fmt.Errorf("error executing query: %w", err)
	}

	return &dtos.SegmentSize{Count: count}, nil
}

// Define a structure to track filter values
type FilterInfo struct {
	AttributeID int64 // ONLY FOR INFO
	Value       string
	FilterPath  string // Unique path identifier for this filter in the condition tree
}

// Define structure to track functional attributes with their context
type FunctionalAttrInfo struct {
	Expression  string // The SQL expression
	FilterPath  string // Which filters this attribute depends on
	Path        string // The path in the condition tree where this attribute appears  // ONLY FOR INFO
	AttributeID int64  // The attribute ID  // ONLY FOR INFO
}

func (c Controller) processFunctionalCondition(
	ctx context.Context,
	condition *dtos.Condition,
	currentPath string, // Track position in condition tree
	applyPrefix bool, // Prefix for attribute columns
	parentFilterInfo []FilterInfo, // Add parameter to pass down parent filter info
) (
	functionalAttrs []FunctionalAttrInfo,
	filterClauses []string,
	standardClauses []string,
	functionalClauses []string,
	filterInfo []FilterInfo, // Track filters for {params} replacement
	err error,
) {
	functionalAttrs = make([]FunctionalAttrInfo, 0)
	filterClauses = make([]string, 0)
	standardClauses = make([]string, 0)
	functionalClauses = make([]string, 0)
	filterInfo = make([]FilterInfo, 0)

	// Start by including any parent filter info
	if parentFilterInfo != nil {
		filterInfo = append(filterInfo, parentFilterInfo...)
	}

	if condition == nil {
		return functionalAttrs, filterClauses, standardClauses, functionalClauses, filterInfo, nil
	}

	// Process entity filter if present
	if condition.IsFilter != nil && *condition.IsFilter && condition.AttributeId != nil && *condition.AttributeId != 0 {

		// Generate a unique path for this filter
		filterPath := fmt.Sprintf("%s_filter_%d", currentPath, *condition.AttributeId)

		// Build filter clause with attribute prefix
		filterCondition := &dtos.Condition{
			AttributeId:    condition.AttributeId,
			ConditionValue: condition.ConditionValue,
			Type:           "simple",
			Operator:       condition.Operator,
		}

		// GENERATE FILTER CLAUSE ------------------
		filterClause, err := c.BuildWhereClause(ctx, filterCondition, applyPrefix)
		if err != nil {
			return nil, nil, nil, nil, nil, err
		}
		filterClauses = append(filterClauses, filterClause)

		// Record filter attribute and its formatted value for {params} replacement
		valueStr, err := formatValueForSQL(condition.ConditionValue)
		if err != nil {
			return nil, nil, nil, nil, nil, err
		}

		// Remove parentheses if present (to get just the values)
		if strings.HasPrefix(valueStr, "(") && strings.HasSuffix(valueStr, ")") {
			valueStr = valueStr[1 : len(valueStr)-1]
		}

		// GENERATE FILTER INFO ------------------
		filterInfo = append(filterInfo, FilterInfo{
			AttributeID: *condition.AttributeId,
			Value:       valueStr,
			FilterPath:  filterPath,
		})

	}

	switch condition.Type {
	case "simple":
		if condition.AttributeId == nil || *condition.AttributeId == 0 {
			return functionalAttrs, filterClauses, standardClauses, functionalClauses, filterInfo, nil
		}

		isFunctional, err := c.AttributesController.IsAttributeFunctional(ctx, *condition.AttributeId)
		if err != nil {
			return nil, nil, nil, nil, nil, err
		}

		if isFunctional {
			// Generate a unique path for this attribute instance
			attrPath := fmt.Sprintf("%s_attr_%d", currentPath, *condition.AttributeId)

			// Get the functional expression with attribute prefix
			expr, err := c.AttributesController.GetAttributeColumnExpression(ctx, *condition.AttributeId, applyPrefix)

			if err != nil {
				return nil, nil, nil, nil, nil, err
			}
			expr += fmt.Sprintf("AS %s", attrPath)

			if len(filterInfo) == 0 {
				return nil, nil, nil, nil, nil, fmt.Errorf("needed a parent filter for Functional Attribute")
			}
			if len(filterInfo) > 1 {
				return nil, nil, nil, nil, nil, fmt.Errorf("can have only 1 parent filter for Functional Attribute")
			}

			// Create a context-specific functional attribute
			attrInfo := FunctionalAttrInfo{
				Expression:  expr,
				FilterPath:  filterInfo[0].FilterPath,
				Path:        attrPath,
				AttributeID: *condition.AttributeId,
			}

			// Store with context-specific path
			functionalAttrs = append(functionalAttrs, attrInfo)

			// Build functional clause with context-specific alias
			valueStr, err := formatValueForSQL(condition.ConditionValue)
			if err != nil {
				return nil, nil, nil, nil, nil, err
			}

			clause := fmt.Sprintf("%s %s %s", attrPath, *condition.Operator, valueStr)
			functionalClauses = append(functionalClauses, clause)

		} else {
			// For standard attributes, add to where clause with attribute prefix
			clause, err := c.BuildWhereClause(ctx, condition, applyPrefix)
			if err != nil {
				return nil, nil, nil, nil, nil, err
			}
			standardClauses = append(standardClauses, clause)
		}

	case "complex":
		// Initialize slices to collect sub-condition results
		var allSubStandardClauses []string
		var allSubFunctionalClauses []string
		var allSubFilterClauses []string
		var allFilterInfo []FilterInfo
		var allFunctionalAttrs []FunctionalAttrInfo

		for i, subCondition := range condition.Conditions {
			// Create a unique path for this subcondition
			subPath := fmt.Sprintf("%s_%d", currentPath, i) // root
			subFuncAttrs, subFilterClauses, subStdClauses, subFuncClauses, subFilterInfo, err :=
				c.processFunctionalCondition(ctx, &subCondition, subPath, applyPrefix, filterInfo)
			if err != nil {
				return nil, nil, nil, nil, nil, err
			}
			// Simply append all functional attributes - no more overriding
			allFunctionalAttrs = append(allFunctionalAttrs, subFuncAttrs...)

			// Collect filter clauses
			allSubFilterClauses = append(allSubFilterClauses, subFilterClauses...)

			// Collect standard clauses
			allSubStandardClauses = append(allSubStandardClauses, subStdClauses...)

			// Collect functional clauses
			allSubFunctionalClauses = append(allSubFunctionalClauses, subFuncClauses...)

			allFilterInfo = append(allFilterInfo, subFilterInfo...)
		}

		// Add filter clauses
		filterClauses = append(filterClauses, allSubFilterClauses...)

		filterInfo = append(filterInfo, allFilterInfo...)

		// Append the collected functional attributes
		functionalAttrs = append(functionalAttrs, allFunctionalAttrs...)

		// Combine standard clauses with the logical operator if there are multiple
		if len(allSubStandardClauses) > 0 {
			if len(allSubStandardClauses) == 1 {
				standardClauses = append(standardClauses, allSubStandardClauses[0])
			} else if condition.LogicalOperator != nil {
				standardClauses = append(standardClauses,
					fmt.Sprintf("(%s)", strings.Join(allSubStandardClauses, " "+*condition.LogicalOperator+" ")))
			}
		}

		// Combine functional clauses with the logical operator if there are multiple
		if len(allSubFunctionalClauses) > 0 {
			if len(allSubFunctionalClauses) == 1 {
				functionalClauses = append(functionalClauses, allSubFunctionalClauses[0])
			} else if condition.LogicalOperator != nil {
				functionalClauses = append(functionalClauses,
					fmt.Sprintf("(%s)", strings.Join(allSubFunctionalClauses, " "+*condition.LogicalOperator+" ")))
			}
		}
	}

	return functionalAttrs, filterClauses, standardClauses, functionalClauses, filterInfo, nil
}

func (c Controller) getSegmentSizeWithFunctionalAttributes(ctx context.Context, condition *dtos.Condition) (*dtos.SegmentSize, error) {
	// Validate filter conditions
	if err := c.ValidateFilterConditions(condition); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	targetTableName, targetEntityType, targetCatalogName, targetSchemaName, err := c.getTargetEntityTableNameType(ctx, condition)
	if err != nil {
		return nil, fmt.Errorf("error getting target entity table name: %w", err)
	}

	// Get the entity attribute prefix
	applyPrefix := false
	if targetEntityType == string(dtos.EntityTypeDerived) {
		applyPrefix = true
	}

	// Process the condition to separate functional and standard attributes
	functionalAttrs, _, standardClauses, functionalClauses, filterInfo, err :=
		c.processFunctionalCondition(ctx, condition, "root", applyPrefix, nil)
	if err != nil {
		return nil, fmt.Errorf("error processing functional condition: %w", err)
	}

	// Create a map to organize filters by path
	filterMap := make(map[string]FilterInfo)
	for _, filter := range filterInfo {
		filterMap[filter.FilterPath] = filter
	}

	// Build derived table query with functional expressions
	userIDField := ""
	userEntityInfo, err := c.EntityController.GetEntityDetails(ctx, USER_ENTITY_ID)
	if err != nil {
		return nil, fmt.Errorf("error getting entity details: %w", err)
	}
	if applyPrefix {
		userIDField = fmt.Sprintf("%s_user_id", *userEntityInfo.TableName)
	} else {
		userIDField = "user_id"
	}
	derivedTableQuery := fmt.Sprintf("SELECT %s", userIDField)

	// Use path-based aliases to ensure uniqueness across the condition tree
	for _, attrInfo := range functionalAttrs {
		expr := attrInfo.Expression

		// Replace {params} in functional expressions with actual filter values based on context
		if filter, exists := filterMap[attrInfo.FilterPath]; exists && strings.Contains(expr, "{params}") {
			expr = strings.Replace(expr, "{params}", filter.Value, 1)
		}

		// Create a path-based alias for each attribute context
		derivedTableQuery += fmt.Sprintf(", %s", expr)
	}

	derivedTableQuery += fmt.Sprintf(" FROM %s.%s.%s", targetCatalogName, targetSchemaName, targetTableName)

	// Add WHERE conditions
	// whereConditions := append(filterClauses, standardClauses...)
	whereConditions := append(standardClauses) // Removing filterClauses like l0_category, l1_category, etc. as they are already handled in functional attributes
	if len(whereConditions) > 0 {
		derivedTableQuery += " WHERE " + strings.Join(whereConditions, " AND ")
	}

	// Group by user_id for aggregation functions
	derivedTableQuery += fmt.Sprintf(" GROUP BY %s", userIDField)

	// Main query using the derived table
	query := fmt.Sprintf("SELECT COUNT(DISTINCT %s) FROM (%s) AS derived_table", userIDField, derivedTableQuery)

	// Add any conditions on the calculated fields
	if len(functionalClauses) > 0 {
		query += " WHERE " + strings.Join(functionalClauses, " AND ")
	}

	log.Info("Executing functional query:", query)
	var count int64

	err = c.StarrocksClient.GetContext(ctx, &count, query)
	if err != nil {
		return nil, fmt.Errorf("error executing functional query: %w", err)
	}

	return &dtos.SegmentSize{Count: count}, nil
}

func (c Controller) getQueryWithFunctionalAttributes(ctx context.Context, condition *dtos.Condition, tgCgCondition *dtos.Condition, outputColumns []string) (string, error) {
	// Validate filter conditions
	if err := c.ValidateFilterConditions(condition); err != nil {
		return "", fmt.Errorf("validation failed: %w", err)
	}

	// Get the entity table name
	targetTableName, targetEntityType, targetCatalogName, targetSchemaName, err := c.getTargetEntityTableNameType(ctx, condition)
	if err != nil {
		return "", fmt.Errorf("error getting entity table name: %w", err)
	}

	applyPrefix := false
	if targetEntityType == string(dtos.EntityTypeDerived) {
		applyPrefix = true
	}
	// Process segment conditions to get functional attributes, filters, etc.
	functionalAttrs, _, standardClauses, functionalClauses, filterInfo, err :=
		c.processFunctionalCondition(ctx, condition, "root", applyPrefix, nil)
	if err != nil {
		return "", fmt.Errorf("error processing functional condition: %w", err)
	}

	// Create a map to organize filters by path
	filterMap := make(map[string]FilterInfo)
	for _, filter := range filterInfo {
		filterMap[filter.FilterPath] = filter
	}

	// Handle TG conditions
	var tgWhereClause string
	if tgCgCondition != nil {
		// Build TG where clause with proper entity table name
		tgWhereClause, err = c.BuildWhereClause(ctx, tgCgCondition, applyPrefix)
		if err != nil {
			return "", fmt.Errorf("error building TG where clause: %w", err)
		}
	}

	// Build derived table query with functional expressions
	userEntityInfo, err := c.EntityController.GetEntityDetails(ctx, USER_ENTITY_ID)
	if err != nil {
		return "", fmt.Errorf("error getting entity details: %w", err)
	}

	userIDField := ""
	if applyPrefix {
		userIDField = fmt.Sprintf("%s_user_id", *userEntityInfo.TableName)
	} else {
		userIDField = "user_id"
	}

	// Start building the derived table SELECT statement
	derivedTableQuery := fmt.Sprintf("SELECT %s", userIDField)

	// Track column names to be used in GROUP BY
	groupByColumns := []string{userIDField}

	// Add output columns with prefixes in derived table but remember original names
	outputColumnMap := make(map[string]string) // map from prefixed to unprefixed
	if len(outputColumns) > 0 {
		for _, col := range outputColumns {
			col = strings.TrimSpace(col)
			if col == "" {
				continue
			}

			prefixedCol := ""
			if *userEntityInfo.TableName != "" && !strings.Contains(col, ".") {
				if applyPrefix {
					prefixedCol = fmt.Sprintf("%s_%s", *userEntityInfo.TableName, col)
				} else {
					prefixedCol = col
				}
				derivedTableQuery += fmt.Sprintf(", %s", prefixedCol)
				outputColumnMap[prefixedCol] = col
				groupByColumns = append(groupByColumns, prefixedCol)
			} else {
				// Handle columns that already have a qualifier
				derivedTableQuery += fmt.Sprintf(", %s", col)
				outputColumnMap[col] = col
				groupByColumns = append(groupByColumns, col)
			}
		}
	}

	// Add functional attribute expressions
	for _, attrInfo := range functionalAttrs {
		expr := attrInfo.Expression

		// Replace {params} in functional expressions with actual filter values
		if filter, exists := filterMap[attrInfo.FilterPath]; exists && strings.Contains(expr, "{params}") {
			expr = strings.Replace(expr, "{params}", filter.Value, 1)
		}

		derivedTableQuery += fmt.Sprintf(", %s", expr)
	}

	// Add TG condition field to the select clause
	if tgCgCondition != nil {
		derivedTableQuery += fmt.Sprintf(", CASE WHEN %s THEN true ELSE false END AS is_tg", tgWhereClause)
		groupByColumns = append(groupByColumns, "is_tg")
	} else {
		derivedTableQuery += ", true AS is_tg"
		groupByColumns = append(groupByColumns, "is_tg")
	}

	derivedTableQuery += fmt.Sprintf(" FROM %s.%s.%s",
		targetCatalogName,
		targetSchemaName,
		targetTableName)

	// Add WHERE conditions
	// whereConditions := append([]string{}, filterClauses...)
	whereConditions := append(standardClauses) // Removing filterClauses like l0_category, l1_category, etc. as they are already handled in functional attributes

	if len(whereConditions) > 0 {
		derivedTableQuery += " WHERE " + strings.Join(whereConditions, " AND ")
	}

	// Group by all necessary columns
	derivedTableQuery += " GROUP BY " + strings.Join(groupByColumns, ", ")

	// Build final SELECT with proper column aliases
	finalSelectColumns := []string{
		fmt.Sprintf("%s AS user_id", userIDField),
	}

	// Add output columns with proper aliases
	for prefixedCol, originalCol := range outputColumnMap {
		finalSelectColumns = append(finalSelectColumns, fmt.Sprintf("%s AS %s", prefixedCol, originalCol))
	}

	// Add is_tg column
	finalSelectColumns = append(finalSelectColumns, "is_tg")

	// Main query using the derived table with proper column aliases
	mainQuery := fmt.Sprintf("SELECT %s FROM (%s) AS derived_table",
		strings.Join(finalSelectColumns, ", "),
		derivedTableQuery)

	// Add conditions on calculated fields
	if len(functionalClauses) > 0 {
		mainQuery += " WHERE " + strings.Join(functionalClauses, " AND ")
	}

	return mainQuery, nil
}

func (c Controller) getQueryWithoutFunctionalAttributes(ctx context.Context, condition *dtos.Condition, tgCgCondition *dtos.Condition, outputColumns []string) (string, error) {
	// Validate filter conditions
	if err := c.ValidateFilterConditions(condition); err != nil {
		return "", fmt.Errorf("validation failed: %w", err)
	}

	// Get the entity table name
	targetTableName, targetEntityType, targetCatalogName, targetSchemaName, err := c.getTargetEntityTableNameType(ctx, condition)
	if err != nil {
		return "", fmt.Errorf("error getting attribute prefix: %w", err)
	}

	applyPrefix := false
	if targetEntityType == string(dtos.EntityTypeDerived) {
		applyPrefix = true
	}

	whereClause, err := c.BuildWhereClause(ctx, condition, applyPrefix)
	if err != nil {
		log.Error("Error building where clause", err)
		return "", fmt.Errorf("error building query: %w", err)
	}

	// Build the select clause with provided output columns
	var selectColumns []string

	// Handle user_id column with prefix but alias as simple user_id
	userID := ""
	userEntityInfo, err := c.EntityController.GetEntityDetails(ctx, USER_ENTITY_ID)
	if err != nil {
		return "", fmt.Errorf("error getting entity details: %w", err)
	}
	if applyPrefix {
		userID = fmt.Sprintf("%s_user_id AS user_id", *userEntityInfo.TableName)
	} else {
		userID = "user_id"
	}
	selectColumns = append(selectColumns, userID)

	// Add other output columns with prefix but alias to simple names
	if len(outputColumns) > 0 {
		for _, col := range outputColumns {
			if applyPrefix {
				selectColumns = append(selectColumns, fmt.Sprintf("%s_%s AS %s", *userEntityInfo.TableName, col, col))
			} else {
				selectColumns = append(selectColumns, col)
			}
		}
	}

	// Create the base SELECT clause
	selectClause := strings.Join(selectColumns, ", ")

	// Build the TG WHERE clause if TG conditions exist
	if tgCgCondition != nil {
		tgWhereClause, err := c.BuildWhereClause(ctx, tgCgCondition, applyPrefix)
		if err != nil {
			return "", fmt.Errorf("error building TG where clause: %w", err)
		}

		// Add is_tg field that's true when TG conditions are met, false otherwise
		selectClause = selectClause + fmt.Sprintf(", CASE WHEN %s THEN true ELSE false END AS is_tg", tgWhereClause)
	} else {
		// If no TG conditions exist, all records are not TG
		selectClause = selectClause + ", true AS is_tg"
	}

	// Return the complete query
	return fmt.Sprintf("SELECT %s FROM %s.%s.%s WHERE %s",
		selectClause,
		targetCatalogName,
		targetSchemaName,
		targetTableName,
		whereClause), nil
}
