package featurestoresparkcontroller

import (
	"bytes"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	entities "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	"github.com/Zomato/cdp-platform/internal/controllers/configcontroller"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/cdp-platform/pkg/s3"
	"github.com/Zomato/go/logger"
	"github.com/xtgo/uuid"
	"gopkg.in/yaml.v2"
)

type Controller struct {
	*basecontroller.Controller
	DestinationSyncRunController *destinationsyncrunscontroller.Controller
	TemporalController           *temporalcontroller.Controller
	ConfigController             *configcontroller.Controller
}

const (
	RUN_ID_PREFIX         = "feature_store_spark_cdp"
	FEATURE_STORE_CONTEXT = "audiences_cdp_1"
	FEATURE_STORE_ENTITY  = "user"
	USER_ID_COLUMN        = "user_id"
	SEGMENT_PROP_TYPE     = "segment"
	YAML_BUCKET           = "blinkit-analytics"
)

// generateUUID creates a random UUID string
func generateUUID() (string, error) {
	uuid := uuid.NewRandom()
	// Return just the first part of the UUID to keep it shorter
	return strings.Split(uuid.String(), "-")[0], nil
}

// getEnvironmentBasedKey generates the S3 key based on the current environment, entity and context
func (controller Controller) getEnvironmentBasedKey(entity, context string) string {
	env, _ := controller.ConfigController.GetEnvironment()
	if env == "" {
		env = "local"
	}
	return fmt.Sprintf("config/cdp/%s/spark/%s/%s.yml", env, entity, context)
}

func (controller Controller) AddFeatureStoreSparkRun(ctx context.Context, featureStoreSparkRunDTO *dtos.FeatureStoreSparkRun) (*dtos.FeatureStoreSparkRun, error) {
	query := `
        INSERT INTO feature_store_spark_runs (
            execution_start,
            status
        ) VALUES (
            :execution_start,
            :status
        ) RETURNING id`

	featureStoreSparkRunEntity := &entities.FeatureStoreSparkRun{
		ExecutionStart: featureStoreSparkRunDTO.ExecutionStart,
		Status:         featureStoreSparkRunDTO.Status,
	}

	rows, err := controller.PostgresClient.NamedQueryContext(ctx, query, featureStoreSparkRunEntity)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&featureStoreSparkRunDTO.Id)
		if err != nil {
			return nil, err
		}

		// If run_id is not provided, generate one with the personas_cdp_{random_string}_id format
		if featureStoreSparkRunDTO.RunID == nil {
			uuid, err := generateUUID()
			if err != nil {
				return nil, fmt.Errorf("failed to generate UUID: %w", err)
			}
			runID := fmt.Sprintf("%s_%d_%s", RUN_ID_PREFIX, featureStoreSparkRunDTO.Id, uuid)
			featureStoreSparkRunDTO.RunID = &runID
		}

		// Update the run_id in the database
		updateQuery := "UPDATE feature_store_spark_runs SET run_id = :run_id WHERE id = :id"
		updateArgs := map[string]interface{}{
			"id":     featureStoreSparkRunDTO.Id,
			"run_id": *featureStoreSparkRunDTO.RunID,
		}
		_, err = controller.PostgresClient.NamedExecContext(ctx, updateQuery, updateArgs)
		if err != nil {
			logger.Warnf("Failed to update run_id after insertion: %v", err)
		}
	}

	return featureStoreSparkRunDTO, nil
}

func (controller Controller) UpdateFeatureStoreSparkRun(ctx context.Context, featureStoreSparkRunDTO *dtos.FeatureStoreSparkRun) error {
	logger.Infoln("FeatureStoreSpark update method started")
	query := "UPDATE feature_store_spark_runs SET "
	var updates []string
	args := map[string]interface{}{"id": featureStoreSparkRunDTO.Id}

	if featureStoreSparkRunDTO.ExecutionStart != nil && !featureStoreSparkRunDTO.ExecutionStart.IsZero() {
		updates = append(updates, "execution_start = :execution_start")
		args["execution_start"] = *featureStoreSparkRunDTO.ExecutionStart
	}
	if featureStoreSparkRunDTO.ExecutionEnd != nil && !featureStoreSparkRunDTO.ExecutionEnd.IsZero() {
		updates = append(updates, "execution_end = :execution_end")
		args["execution_end"] = *featureStoreSparkRunDTO.ExecutionEnd
	}
	if featureStoreSparkRunDTO.Status != "" {
		updates = append(updates, "status = :status")
		args["status"] = featureStoreSparkRunDTO.Status
	}
	if featureStoreSparkRunDTO.ErrorMessage != nil && *featureStoreSparkRunDTO.ErrorMessage != "" {
		updates = append(updates, "error_message = :error_message")
		args["error_message"] = *featureStoreSparkRunDTO.ErrorMessage
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += strings.Join(updates, ", ") + " WHERE id = :id"

	_, err := controller.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (controller Controller) AddFeatureStoreSparkDestinationSyncRunMapping(ctx context.Context, mappingDTO *dtos.FeatureStoreSparkDestinationSyncRunMapping) error {
	query := `
        INSERT INTO feature_store_spark_destination_sync_run_mapping (
            feature_store_spark_run_id,
            destination_sync_run_id
        ) VALUES (
            :feature_store_spark_run_id,
            :destination_sync_run_id
        ) RETURNING id`

	mappingEntity := &entities.FeatureStoreSparkDestinationSyncRunMapping{
		FeatureStoreSparkRunID: mappingDTO.FeatureStoreSparkRunID,
		DestinationSyncRunID:   mappingDTO.DestinationSyncRunID,
	}

	rows, err := controller.PostgresClient.NamedQueryContext(ctx, query, mappingEntity)
	if err != nil {
		return err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&mappingDTO.Id)
		if err != nil {
			return err
		}
	}

	return nil
}

func (controller Controller) AddDestinationSyncRunForFeatureStoreSparkRun(ctx context.Context, segmentInfoList []dtos.FeatureStoreSegmentInfo, featureStoreSparkRunId int64) error {
	query := `
        INSERT INTO feature_store_spark_destination_sync_run_mapping (
            feature_store_spark_run_id,
            destination_sync_run_id
        ) VALUES (
            :feature_store_spark_run_id,
            :destination_sync_run_id
        ) RETURNING id`

	for _, segmentInfo := range segmentInfoList {
		if segmentInfo.LastDestinationSyncRunID == nil || *segmentInfo.LastDestinationSyncRunID == 0 {
			continue
		}

		mappingEntity := &entities.FeatureStoreSparkDestinationSyncRunMapping{
			FeatureStoreSparkRunID: featureStoreSparkRunId,
			DestinationSyncRunID:   *segmentInfo.LastDestinationSyncRunID,
		}

		rows, err := controller.PostgresClient.NamedQueryContext(ctx, query, mappingEntity)
		if err != nil {
			return fmt.Errorf("failed to insert feature store spark destination sync mapping: %w", err)
		}

		var mappingId int64
		if rows.Next() {
			err = rows.Scan(&mappingId)
			if err != nil {
				rows.Close()
				return fmt.Errorf("failed to scan mapping ID: %w", err)
			}
		}
		rows.Close()
	}

	return nil
}

func (controller Controller) MarkLastDestinationSyncRunStatus(ctx context.Context, segmentInfoList []dtos.FeatureStoreSegmentInfo, status string) error {
	for _, segmentInfo := range segmentInfoList {
		if segmentInfo.LastDestinationSyncRunID == nil || *segmentInfo.LastDestinationSyncRunID == 0 {
			continue
		}

		destinationSyncRunDTO := &dtos.DestinationSyncRun{
			Base:   dtos.Base{Id: *segmentInfo.LastDestinationSyncRunID},
			Status: status,
		}

		err := controller.DestinationSyncRunController.UpdateDestinationSyncRun(ctx, destinationSyncRunDTO)
		if err != nil {
			return fmt.Errorf("failed to update destination sync run: %w", err)
		}
	}
	return nil
}

// GetMaxSuccessfulExecutionStart retrieves the maximum (most recent) execution start time
// from all successful personas runs
func (controller Controller) GetMaxSuccessfulExecutionStart(ctx context.Context) (*time.Time, error) {
	query := `
        SELECT MAX(execution_start)
        FROM feature_store_spark_runs
        WHERE status = 'SUCCESSFUL'
    `

	var maxExecutionStart *time.Time
	err := controller.PostgresClient.GetContext(ctx, &maxExecutionStart, query)
	if err != nil {
		if err == sql.ErrNoRows {
			// No successful runs found, return nil without error
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get max successful execution start: %w", err)
	}

	return maxExecutionStart, nil
}

// FormatSegmentInfoToYAML formats the feature store segment info into a YAML compatible structure
func (controller Controller) FormatSegmentInfoToYAML(ctx context.Context, segmentInfoList []dtos.FeatureStoreSegmentInfo) (*dtos.SegmentYAML, error) {
	// Group by context and entity
	segmentsByContext := make(map[string]map[string]map[string][]string)

	for _, info := range segmentInfoList {
		context := info.ContextName
		entity := info.Entity
		if info.CommitedSegmentTableName == nil {
			continue
		}
		tableName := *info.CommitedSegmentTableName

		// Initialize maps if they don't exist
		if _, exists := segmentsByContext[context]; !exists {
			segmentsByContext[context] = make(map[string]map[string][]string)
		}
		if _, exists := segmentsByContext[context][entity]; !exists {
			segmentsByContext[context][entity] = make(map[string][]string)
		}

		// Add the feature to the corresponding table's columns
		segmentsByContext[context][entity][tableName] = append(
			segmentsByContext[context][entity][tableName],
			info.FeatureName,
		)
	}

	// Start building the YAML structure
	result := &dtos.SegmentYAML{
		Context:        FEATURE_STORE_CONTEXT,
		Entity:         FEATURE_STORE_ENTITY,
		EntityIdColumn: USER_ID_COLUMN,
		PropType:       SEGMENT_PROP_TYPE,
		TTLInDays:      -1,
		Properties:     []dtos.SegmentProperty{},
	}

	if len(segmentsByContext) > 0 {
		for contextName, entities := range segmentsByContext {
			if contextName != FEATURE_STORE_CONTEXT {
				continue
			}
			for entityName, tables := range entities {
				if entityName != FEATURE_STORE_ENTITY {
					continue
				}
				for tableName, columns := range tables {
					property := dtos.SegmentProperty{
						Schema:  controller.StarrocksIcebergSchema,
						Table:   tableName,
						Columns: columns,
					}
					result.Properties = append(result.Properties, property)
				}

				// Sort properties for consistent output
				sort.Slice(result.Properties, func(i, j int) bool {
					return result.Properties[i].Table < result.Properties[j].Table
				})
			}
		}
	}

	return result, nil
}

// UploadSegmentYAMLToS3 generates a segment YAML file from feature store data and uploads it to S3
func (controller Controller) UploadSegmentYAMLToS3(ctx context.Context, yamlData *dtos.SegmentYAML) (string, error) {
	// Skip upload if no properties found
	if len(yamlData.Properties) == 0 {
		logger.Info("No segment properties found, skipping S3 upload")
		return "", nil
	}

	// Convert to YAML bytes
	yamlBytes, err := yaml.Marshal(yamlData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal to YAML: %w", err)
	}

	// Get environment-based key
	key := controller.getEnvironmentBasedKey(FEATURE_STORE_ENTITY, FEATURE_STORE_CONTEXT)

	// Upload to S3
	uploadInput := &s3.UploadInput{
		Bucket:      YAML_BUCKET,
		Key:         key,
		Body:        bytes.NewReader(yamlBytes),
		ContentType: "application/x-yaml",
	}

	err = controller.S3Client.UploadFile(ctx, uploadInput)
	if err != nil {
		return "", fmt.Errorf("failed to upload YAML to S3: %w", err)
	}

	s3Path := fmt.Sprintf("s3://%s/%s", YAML_BUCKET, key)
	logger.Infof("Successfully uploaded segment YAML to %s", s3Path)
	return s3Path, nil
}

func (c Controller) ManualTriggerSparkJob(ctx context.Context) error {
	id := 1
	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.FeatureStoreSparkWorkflow,
		Args:         []interface{}{id},
	}

	err := c.TemporalController.TriggerSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}
	return nil
}

func (c Controller) ScheduleSparkJob(ctx context.Context) error {
	id := 1
	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.FeatureStoreSparkWorkflow,
		ScheduleSpec: dtos.ScheduleSpecRequest{
			CronExpressions: []string{"30 9 30 5 *"},
		},
		Args: []interface{}{id},
	}

	err := c.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}
	return nil
}

// ScheduleFeatureStoreStatusChecker creates a schedule for the feature store status scheduler workflow
// that runs every 20 minutes to check and update pending destination sync runs
func (c Controller) ScheduleFeatureStoreStatusChecker(ctx context.Context) error {
	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.FeatureStoreStatusSchedulerWorkflow,
		ScheduleSpec: dtos.ScheduleSpecRequest{
			CronExpressions: []string{"*/20 * * * *"}, // Every 20 minutes
		},
		Args: []interface{}{}, // No arguments needed for the status checker
	}

	err := c.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create feature store status scheduler: %w", err)
	}

	logger.Info("Successfully created feature store status scheduler to run every 20 minutes")
	return nil
}

// ManualTriggerFeatureStoreStatusChecker manually triggers the feature store status checker workflow
func (c Controller) ManualTriggerFeatureStoreStatusChecker(ctx context.Context) error {
	triggerInput := dtos.TriggerWorkflowInput{
		WorkflowType: enum.FeatureStoreStatusSchedulerWorkflow,
		Args:         []interface{}{}, // No arguments needed
	}

	err := c.TemporalController.TriggerWorkflow(ctx, triggerInput)
	if err != nil {
		return fmt.Errorf("failed to trigger feature store status checker: %w", err)
	}

	logger.Info("Successfully triggered feature store status checker workflow")
	return nil
}
