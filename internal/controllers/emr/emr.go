package emrcontroller

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	"github.com/Zomato/cdp-platform/pkg/emr"
	"github.com/Zomato/go/logger"
)

// Controller handles EMR-related operations
type Controller struct {
	*basecontroller.Controller
	EMRClient *emr.Client
	// Store workflow-specific configurations
	WorkflowConfigs map[string]*WorkflowEMRConfig
}

// WorkflowEMRConfig contains workflow-specific EMR configurations
type WorkflowEMRConfig struct {
	ClusterConfigTemplate  emr.ClusterConfig
	SparkJobConfigTemplate emr.SparkJobConfig
}

// NewController creates a new EMR controller with default workflow configurations
func NewController(baseCtrl *basecontroller.Controller, client *emr.Client) *Controller {
	controller := &Controller{
		Controller:      baseCtrl,
		EMRClient:       client,
		WorkflowConfigs: make(map[string]*WorkflowEMRConfig),
	}

	// Initialize with default configurations
	controller.initDefaultConfigs()

	return controller
}

// initDefaultConfigs initializes default configurations for known workflows
func (c *Controller) initDefaultConfigs() {
	// Update HelloWorld config to use instance fleets
	helloWorldConfig := &WorkflowEMRConfig{
		ClusterConfigTemplate: emr.ClusterConfig{
			Name:                        "hello_world",
			LogURI:                      "s3://aws-logs-183295456051-ap-southeast-1/elasticmapreduce/",
			ReleaseLabel:                "emr-6.11.1",
			Applications:                []string{"Hadoop", "Spark", "Ganglia"},
			InstanceGroups:              false, // Use instance fleets
			ServiceRole:                 "EMR_DefaultRole",
			JobFlowRole:                 "prod-blinkit-cdp-emr-role",
			KeepJobFlowAliveWhenNoSteps: true,
			VisibleToAllUsers:           true,
			StepConcurrencyLevel:        7,
			EC2SubnetIDs:                []string{"subnet-09a51196787a38025", "subnet-091d72e204a90b03e"},
			ServiceAccessSG:             "sg-0e3174223a1699084",
			EmrManagedMasterSG:          "sg-0bcf64e5c80a5f1f7",
			EmrManagedSlaveSG:           "sg-0bcf64e5c80a5f1f7",
			InstanceFleets: []emr.InstanceFleetConfig{
				{
					Name:                   "Master",
					InstanceFleetType:      "MASTER",
					TargetOnDemandCapacity: 1,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
				{
					Name:                   "Core",
					InstanceFleetType:      "CORE",
					TargetOnDemandCapacity: 3,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
				{
					Name:                   "Task",
					InstanceFleetType:      "TASK",
					TargetOnDemandCapacity: 2,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
			},
			Tags: map[string]string{
				"Dag":                         "hello_world",
				"Name":                        "hello_world",
				"grofers.io/service":          "test",
				"grofers.io/component":        "test",
				"grofers.io/component-role":   "data-processing",
				"grofers.io/business-service": "data-platform",
				"grofers.io/team":             "data-engineering",
				"grofers.io/tribe":            "data",
				"cost:namespace":              "data",
				"cost:application":            "emr",
			},
		},
		SparkJobConfigTemplate: emr.SparkJobConfig{
			DeployMode:          "cluster",
			Master:              "yarn",
			DriverMemory:        "5g",
			DriverMaxResultSize: "2g",
			ExecutorCores:       2,
			ExecutorMemory:      "5g",
			ExecutorInstances:   3,
			AdditionalConfigs: map[string]string{
				"spark.sql.caseSensitive":                   "true",
				"spark.serializer":                          "org.apache.spark.serializer.KryoSerializer",
				"spark.kryoserializer.buffer.max":           "512m",
				"spark.dynamicAllocation.enabled":           "false",
				"spark.yarn.am.waitTime":                    "1800s",
				"spark.yarn.heterogeneousExecutors.enabled": "false",
			},
		},
	}
	c.WorkflowConfigs["HelloWorld"] = helloWorldConfig

	featureStoreSparkConfig := &WorkflowEMRConfig{
		ClusterConfigTemplate: emr.ClusterConfig{
			Name:                        "de_feature_store_spark",
			LogURI:                      "s3://aws-logs-183295456051-ap-southeast-1/elasticmapreduce/",
			ReleaseLabel:                "emr-6.11.1",
			Applications:                []string{"Hadoop", "Spark", "Ganglia"},
			InstanceGroups:              false, // Use instance fleets
			ServiceRole:                 "EMR_DefaultRole",
			JobFlowRole:                 "prod-blinkit-cdp-emr-role",
			KeepJobFlowAliveWhenNoSteps: true,
			VisibleToAllUsers:           true,
			StepConcurrencyLevel:        7,
			EC2SubnetIDs:                []string{"subnet-09a51196787a38025", "subnet-091d72e204a90b03e"},
			ServiceAccessSG:             "sg-0e3174223a1699084",
			EmrManagedMasterSG:          "sg-0bcf64e5c80a5f1f7",
			EmrManagedSlaveSG:           "sg-0bcf64e5c80a5f1f7",
			BootstrapActions: []emr.BootstrapAction{
				{
					Name:       "python-libs",
					ScriptPath: "s3://blinkit-analytics/config/emr/python_bootstrap_packages.sh",
				},
				{
					Name:       "kafka-libs",
					ScriptPath: "s3://blinkit-analytics/config/emr/bootstrap_spark_packages.sh",
				},
			},
			InstanceFleets: []emr.InstanceFleetConfig{
				{
					Name:                   "MasterFleet",
					InstanceFleetType:      "MASTER",
					TargetOnDemandCapacity: 1,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "m5.xlarge",
						},
					},
				},
				{
					Name:                   "CoreFleet",
					InstanceFleetType:      "CORE",
					TargetOnDemandCapacity: 2,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r6g.4xlarge",
						},
						{
							InstanceType: "m6g.4xlarge",
						},
					},
				},
				{
					Name:                   "Task",
					InstanceFleetType:      "TASK",
					TargetOnDemandCapacity: 3,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.2xlarge",
						},
					},
				},
			},
			Tags: map[string]string{
				"Dag":                         "de_feature_store_spark",
				"Name":                        "de_feature_store_spark",
				"grofers.io/service":          "feature-store",
				"grofers.io/component":        "feature-store-spark-cluster",
				"grofers.io/component-role":   "data-processing",
				"grofers.io/business-service": "data-platform",
				"grofers.io/team":             "data-engineering",
				"grofers.io/tribe":            "data",
				"cost:namespace":              "data",
				"cost:application":            "emr",
			},
		},
		SparkJobConfigTemplate: emr.SparkJobConfig{
			DeployMode:          "cluster",
			Master:              "yarn",
			DriverMemory:        "5g",
			DriverMaxResultSize: "2g",
			ExecutorCores:       6,
			ExecutorMemory:      "40g",
			ExecutorInstances:   4,
			AdditionalJars:      []string{"s3://blinkit-analytics/artifacts/iceberg/iceberg-spark-runtime-3.3_2.12-1.3.0.jar"},
			AdditionalConfigs: map[string]string{
				"spark.sql.caseSensitive":                   "true",
				"spark.serializer":                          "org.apache.spark.serializer.KryoSerializer",
				"spark.kryoserializer.buffer.max":           "512m",
				"spark.dynamicAllocation.enabled":           "false",
				"spark.yarn.am.waitTime":                    "1800s",
				"spark.yarn.heterogeneousExecutors.enabled": "false",
			},
		},
	}
	c.WorkflowConfigs["FeatureStoreSpark"] = featureStoreSparkConfig

	// Default EMR config for SyncAttributeSpark workflow
	// This can be a different configuration
	syncAttributeConfig := &WorkflowEMRConfig{
		ClusterConfigTemplate: emr.ClusterConfig{
			Name:                        "attribute_sync_spark",
			LogURI:                      "s3://aws-logs-183295456051-ap-southeast-1/elasticmapreduce/",
			ReleaseLabel:                "emr-6.11.1",
			Applications:                []string{"Hadoop", "Spark", "Ganglia"},
			InstanceGroups:              false, // Use instance fleets
			ServiceRole:                 "EMR_DefaultRole",
			JobFlowRole:                 "prod-blinkit-cdp-emr-role",
			KeepJobFlowAliveWhenNoSteps: true,
			VisibleToAllUsers:           true,
			StepConcurrencyLevel:        7,
			EC2SubnetIDs:                []string{"subnet-09a51196787a38025", "subnet-091d72e204a90b03e"},
			ServiceAccessSG:             "sg-0e3174223a1699084",
			EmrManagedMasterSG:          "sg-0bcf64e5c80a5f1f7",
			EmrManagedSlaveSG:           "sg-0bcf64e5c80a5f1f7",
			Tags: map[string]string{
				"Name": "cdp_attribute_sync_spark",
			},
			InstanceFleets: []emr.InstanceFleetConfig{
				{
					Name:                   "Master",
					InstanceFleetType:      "MASTER",
					TargetOnDemandCapacity: 1,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
				{
					Name:                   "Core",
					InstanceFleetType:      "CORE",
					TargetOnDemandCapacity: 3,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
				{
					Name:               "Task",
					InstanceFleetType:  "TASK",
					TargetSpotCapacity: 10,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
			},
		},
		SparkJobConfigTemplate: emr.SparkJobConfig{
			DeployMode:          "cluster",
			Master:              "yarn",
			DriverMemory:        "5g",
			DriverMaxResultSize: "2g",
			ExecutorCores:       3,
			ExecutorMemory:      "19g",
			ExecutorInstances:   10,
			AdditionalConfigs: map[string]string{
				"spark.sql.caseSensitive":                             "true",
				"spark.serializer":                                    "org.apache.spark.serializer.KryoSerializer",
				"spark.kryoserializer.buffer.max":                     "512m",
				"spark.dynamicAllocation.enabled":                     "false",
				"spark.yarn.am.waitTime":                              "1800s",
				"spark.yarn.heterogeneousExecutors.enabled":           "false",
				"spark.sql.extensions":                                "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
				"spark.sql.iceberg.handle-timestamp-without-timezone": "true",
				"spark.sql.catalog.iceberg":                           "org.apache.iceberg.spark.SparkCatalog",
				"spark.sql.catalog.iceberg.type":                      "hive",
				"spark.sql.catalog.iceberg.uri":                       "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9084",
				"spark.sql.catalog.iceberg_staging":                   "org.apache.iceberg.spark.SparkCatalog",
				"spark.sql.catalog.iceberg_staging.type":              "hive",
				"spark.sql.catalog.iceberg_staging.uri":               "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9083",
			},
		},
	}
	c.WorkflowConfigs["SyncAttributeSpark"] = syncAttributeConfig

	syncAttributeZomatoConfig := &WorkflowEMRConfig{
		ClusterConfigTemplate: emr.ClusterConfig{
			Name:                        "cdp_category_attribute_sync",
			LogURI:                      "s3://zanalytics-logs/aws-emr/spark-jobs/prod/",
			ReleaseLabel:                "emr-6.11.1",
			Applications:                []string{"Hadoop", "Spark", "Ganglia"},
			InstanceGroups:              false, // Use instance fleets
			ServiceRole:                 "EMR_DefaultRole",
			JobFlowRole:                 "prod-cdp-emr-role",
			KeepJobFlowAliveWhenNoSteps: true,
			VisibleToAllUsers:           true,
			StepConcurrencyLevel:        7,
			EC2SubnetIDs:                []string{"subnet-0586fdc43af5867ed", "subnet-0def816668f235097", "subnet-0d48361fc82d8400b"},
			ServiceAccessSG:             "sg-0bd75983393b4d131",
			EmrManagedMasterSG:          "sg-0c6ee7a5339986c58",
			EmrManagedSlaveSG:           "sg-0c0a7d2365d560f4f",
			Tags: map[string]string{
				"Name": "cdp_category_attribute_sync_spark",
			},
			InstanceFleets: []emr.InstanceFleetConfig{
				{
					Name:                   "Master",
					InstanceFleetType:      "MASTER",
					TargetOnDemandCapacity: 1,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.xlarge",
						},
					},
				},
				{
					Name:                   "Core",
					InstanceFleetType:      "CORE",
					TargetOnDemandCapacity: 3,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.2xlarge",
						},
					},
				},
				{
					Name:                   "Task",
					InstanceFleetType:      "TASK",
					TargetOnDemandCapacity: 30,
					InstanceTypeConfigs: []emr.InstanceTypeConfig{
						{
							InstanceType: "r5.2xlarge",
						},
						{
							InstanceType: "r6i.2xlarge",
						},
					},
				},
			},
		},
		SparkJobConfigTemplate: emr.SparkJobConfig{
			DeployMode:          "cluster",
			Master:              "yarn",
			DriverMemory:        "5g",
			DriverMaxResultSize: "2g",
			ExecutorCores:       5,
			ExecutorMemory:      "40g",
			AdditionalConfigs: map[string]string{
				"spark.sql.caseSensitive":                   "true",
				"spark.serializer":                          "org.apache.spark.serializer.KryoSerializer",
				"spark.kryoserializer.buffer.max":           "512m",
				"spark.dynamicAllocation.enabled":           "false",
				"spark.executor.instances":                  "30",
				"spark.yarn.am.waitTime":                    "1800s",
				"spark.yarn.heterogeneousExecutors.enabled": "false",
				"spark.sql.orc.impl":                        "native",
				"spark.sql.orc.filterPushdown":              "true",
				"spark.sql.orc.enableVectorizedReader":      "true",
				"spark.sql.crossJoin.enabled":               "true",
				"spark.sql.sources.partitionOverwriteMode":  "dynamic",
				"spark.sql.extensions":                      "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
				// Use spark.hadoop.hive.metastore.uris for Zomato instead of spark_catalog
				"spark.hadoop.hive.metastore.uris":       "thrift://prod-thrift-hive-metastore-nlb-f9c8815c23eeadfa.elb.ap-southeast-1.amazonaws.com:9083",
				"spark.sql.spark_catalog.type":           "hive",
				"spark.sql.catalogImplementation":        "hive",
				"spark.sql.catalog.iceberg":              "org.apache.iceberg.spark.SparkCatalog",
				"spark.sql.catalog.iceberg.type":         "hive",
				"spark.sql.catalog.iceberg.uri":          "thrift://prod-thrift-hive-metastore-nlb-f9c8815c23eeadfa.elb.ap-southeast-1.amazonaws.com:9083",
				"spark.sql.catalog.iceberg_staging":      "org.apache.iceberg.spark.SparkCatalog",
				"spark.sql.catalog.iceberg_staging.type": "hive",
				"spark.sql.catalog.iceberg_staging.uri":  "thrift://stag-hive-metastore.internal.zomans.com:9083",
			},
		},
	}
	c.WorkflowConfigs["SyncAttributeSparkZomato"] = syncAttributeZomatoConfig
}

// GetWorkflowConfig retrieves the configuration for a specific workflow
func (c *Controller) GetWorkflowConfig(workflowName string) (*WorkflowEMRConfig, error) {
	config, exists := c.WorkflowConfigs[workflowName]
	if !exists {
		return nil, fmt.Errorf("no EMR configuration found for workflow: %s", workflowName)
	}
	return config, nil
}

func (c *Controller) GetSparkConfig(sparkConfigMap json.RawMessage) (*emr.SparkConfig, error) {
	var config emr.SparkConfig
	if err := json.Unmarshal([]byte(sparkConfigMap), &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Spark config: %w", err)
	}
	return &config, nil
}

// CreateEMRCluster creates an EMR cluster using the configuration for the specified workflow
func (c *Controller) CreateEMRCluster(ctx context.Context, req *dtos.CreateEMRClusterRequest) (*dtos.CreateEMRClusterResponse, error) {
	// Get workflow configuration
	workflowConfig, err := c.GetWorkflowConfig(req.WorkflowName)
	if err != nil {
		return nil, err
	}

	// Clone the template config
	clusterConfig := workflowConfig.ClusterConfigTemplate

	// Apply any overrides from the request
	if req.OverrideConfig != nil {
		// Apply overrides here
		if req.OverrideConfig.InstanceType != "" {
			clusterConfig.InstanceType = req.OverrideConfig.InstanceType
		}
		if req.OverrideConfig.InstanceCount > 0 {
			clusterConfig.InstanceCount = req.OverrideConfig.InstanceCount
		}
		// Add more overrides as needed
	}
	logger.Info("Cluster config", "clusterConfig", clusterConfig)

	// Create the cluster
	clusterID, err := c.EMRClient.CreateCluster(ctx, clusterConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create EMR cluster: %w", err)
	}

	return &dtos.CreateEMRClusterResponse{
		ClusterID:   clusterID,
		ClusterName: clusterConfig.Name,
	}, nil
}

func (c *Controller) SubmitSparkJob(ctx context.Context, req *dtos.SubmitSparkJobRequest) (*dtos.SubmitSparkJobResponse, error) {
	workflowConfig, err := c.GetWorkflowConfig(req.WorkflowName)
	if err != nil {
		return nil, err
	}

	jobConfig := workflowConfig.SparkJobConfigTemplate

	jobConfig.Name = req.JobName
	jobConfig.JarURI = req.JarURI
	jobConfig.MainClass = req.MainClass
	jobConfig.Args = req.Args

	if jobConfig.Properties == nil {
		jobConfig.Properties = make(map[string]string)
	}
	for k, v := range req.Properties {
		jobConfig.Properties[k] = v
	}

	jobConfig.AdditionalJars = req.AdditionalJars

	for k, v := range req.ConfigOverrides {
		if jobConfig.AdditionalConfigs == nil {
			jobConfig.AdditionalConfigs = make(map[string]string)
		}
		jobConfig.AdditionalConfigs[k] = v
	}

	stepID, err := c.EMRClient.AddSparkJobStep(ctx, req.ClusterID, jobConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to submit Spark job: %w", err)
	}

	return &dtos.SubmitSparkJobResponse{
		StepID: stepID,
	}, nil
}

// WaitForClusterReady waits for an EMR cluster to be ready (in WAITING state)
func (c *Controller) WaitForClusterReady(ctx context.Context, clusterID string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		status, err := c.EMRClient.GetClusterStatus(ctx, clusterID)
		if err != nil {
			return err
		}

		logger.Infof("EMR cluster %s state: %s", clusterID, status.State)

		if status.State == string(emr.StateWaiting) {
			return nil
		}

		if status.State == string(emr.StateTerminated) ||
			status.State == string(emr.StateTerminatedWithErrors) {
			return fmt.Errorf("EMR cluster %s terminated unexpectedly: %s", clusterID, status.StateReason)
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(30 * time.Second):
			// Continue polling
		}
	}

	return fmt.Errorf("timeout waiting for EMR cluster %s to be ready", clusterID)
}

// WaitForStepCompletion waits for a step to complete
func (c *Controller) WaitForStepCompletion(ctx context.Context, clusterID, stepID string, timeout time.Duration) error {
	return c.EMRClient.WaitForStepCompletion(ctx, clusterID, stepID, timeout)
}

// TerminateCluster terminates an EMR cluster
func (c *Controller) TerminateCluster(ctx context.Context, clusterID string) error {
	return c.EMRClient.TerminateCluster(ctx, clusterID)
}

func (c *Controller) GetClusterStatus(ctx context.Context, clusterID string) (*dtos.EMRClusterStatusResponse, error) {
	status, err := c.EMRClient.GetClusterStatus(ctx, clusterID)
	if err != nil {
		return nil, err
	}

	return &dtos.EMRClusterStatusResponse{
		ClusterID: clusterID,
		Status:    emr.EMRStatus(*status),
	}, nil
}

func (c *Controller) WaitForClusterStatus(ctx context.Context, clusterID string, status dtos.EMRStatus, timeout time.Duration) error {
	return c.EMRClient.WaitForClusterStatus(ctx, clusterID, string(status), timeout)
}

func (c *Controller) ListClusters(ctx context.Context, states []string) ([]string, error) {
	return c.EMRClient.ListClusters(ctx, states)
}
