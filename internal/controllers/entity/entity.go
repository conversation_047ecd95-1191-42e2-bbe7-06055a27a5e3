package entitycontroller

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	"github.com/Zomato/go/logger"
	"github.com/lib/pq"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) GetAllEntities(ctx context.Context) ([]dtos.EntityInfo, error) {
	query := `
		SELECT e.id, e.name, e.description, e.requires_filter
		FROM entities e
		WHERE e.type = 'BASE'
		ORDER BY e.name
	`

	var entities []dtos.EntityInfo
	err := c.PostgresClient.SelectContext(ctx, &entities, query)
	if err != nil {
		logger.Info("Error fetching entities from postgres", err)
		return nil, err
	}

	return entities, nil
}

func (c Controller) GetEntityByID(ctx context.Context, id int64) (*dtos.EntityInfo, error) {
	query := `
		SELECT e.id, e.name, e.description, e.requires_filter
		FROM entities e
		WHERE e.id = $1
	`

	var entity dtos.EntityInfo
	err := c.PostgresClient.GetContext(ctx, &entity, query, id)
	if err != nil {
		logger.Info("Error fetching entity by ID from postgres", err)
		return nil, err
	}

	return &entity, nil
}

// GetEntityDetails fetches details for an entity
func (c Controller) GetEntityDetails(ctx context.Context, entityId int64) (*dtos.EntityInfo, error) {
	entityQuery := `
        SELECT id, name, catalog_name, schema_name, table_name, type
        FROM entities
        WHERE id = $1
    `

	var entity dtos.EntityInfo
	err := c.PostgresClient.GetContext(ctx, &entity, entityQuery, entityId)
	if err != nil {
		return nil, fmt.Errorf("error fetching entity details: %w", err)
	}

	return &entity, nil
}

func (c Controller) GetTotalUsers(ctx context.Context, entityId int64) (int64, error) {
	entity, err := c.GetEntityDetails(ctx, entityId)
	if err != nil {
		return 0, fmt.Errorf("error getting entity details: %w", err)
	}

	query := fmt.Sprintf("SELECT COUNT(1) FROM %s.%s.%s",
		*entity.CatalogName,
		*entity.SchemaName,
		*entity.TableName)

	var count int64
	err = c.StarrocksClient.GetContext(ctx, &count, query)
	if err != nil {
		return 0, fmt.Errorf("error executing query: %w", err)
	}

	return count, nil
}

// DetermineTargetEntity finds the appropriate entity to use based on the involved entities
func (c Controller) DetermineTargetEntity(ctx context.Context, entityIDs []int64) (int64, error) {
	if len(entityIDs) == 0 {
		return 1, nil // Default to User entity if no entities are specified
	}

	if len(entityIDs) == 1 {
		return entityIDs[0], nil // If only one entity, use that
	}

	// Build the query with proper parameterization
	// We're looking for a derived entity that includes all the source entity IDs
	query := `
        SELECT id 
        FROM entities 
        WHERE type = 'DERIVED' 
        AND id IN (
            SELECT entity_id 
            FROM derived_entity_mapping
            WHERE parent_entity_id = ANY($1)
            GROUP BY entity_id
            HAVING COUNT(DISTINCT parent_entity_id) = $2
        )
        LIMIT 1
    `

	// Use proper SQL array parameter instead of string replacement
	var targetEntityID int64
	err := c.PostgresClient.GetContext(
		ctx,
		&targetEntityID,
		query,
		pq.Array(entityIDs),
		len(entityIDs),
	)

	if err != nil {
		if err == sql.ErrNoRows {
			logger.Info("No derived entity found that combines all requested entities, falling back to first entity")
			return entityIDs[0], nil
		}
		return 0, fmt.Errorf("error finding target entity: %w", err)
	}

	return targetEntityID, nil
}

// findDerivedEntitiesByParent returns IDs of all derived entities that depend on the given entity
func (c Controller) FindDerivedEntitiesByParent(ctx context.Context, entityId int64) ([]int64, error) {
	derivedEntitiesQuery := `
        SELECT DISTINCT dem.entity_id 
        FROM derived_entity_mapping dem
        JOIN entities e ON e.id = dem.entity_id
        WHERE dem.parent_entity_id = $1 AND e.type = 'DERIVED'
    `

	var derivedEntityIds []int64
	err := c.PostgresClient.SelectContext(ctx, &derivedEntityIds, derivedEntitiesQuery, entityId)
	if err != nil {
		return nil, fmt.Errorf("error finding derived entities: %w", err)
	}

	return derivedEntityIds, nil
}

// getParentEntities fetches all parent entities for a derived entity
func (c Controller) GetParentEntities(ctx context.Context, derivedEntityId int64) ([]dtos.EntityInfo, error) {
	parentEntitiesQuery := `
        SELECT e.id, e.name, e.table_name, e.catalog_name, e.schema_name
        FROM derived_entity_mapping dem
        JOIN entities e ON e.id = dem.parent_entity_id
        WHERE dem.entity_id = $1
        ORDER BY e.id
    `

	var parentEntities []dtos.EntityInfo
	err := c.PostgresClient.SelectContext(ctx, &parentEntities, parentEntitiesQuery, derivedEntityId)
	if err != nil {
		return nil, fmt.Errorf("error fetching parent entities: %w", err)
	}

	return parentEntities, nil
}

// getEntityJoins fetches join information for a derived entity's parent entities
func (c Controller) GetEntityJoins(ctx context.Context, derivedEntityId int64) ([]dtos.JoinInfo, error) {
	joinsQuery := `
        SELECT 
            source_entity_id, 
            target_entity_id, 
            source_column, 
            target_column, 
            join_type
        FROM entity_join_info
        WHERE 
            (source_entity_id IN (SELECT parent_entity_id FROM derived_entity_mapping WHERE entity_id = $1)
            AND target_entity_id IN (SELECT parent_entity_id FROM derived_entity_mapping WHERE entity_id = $1))
    `

	var joins []dtos.JoinInfo
	err := c.PostgresClient.SelectContext(ctx, &joins, joinsQuery, derivedEntityId)
	if err != nil {
		return nil, fmt.Errorf("error fetching join information: %w", err)
	}

	return joins, nil
}

// getAttributesForEntities fetches attributes for all specified entities
func (c Controller) GetAttributesForEntities(ctx context.Context, entities []dtos.EntityInfo) ([]dtos.AttributeInfo, error) {
	var allAttributes []dtos.AttributeInfo
	// TODO: Can we take this to Attribute Controller?
	for _, entity := range entities {
		attributesQuery := `
            SELECT 
                aq.entity_id,
                e.table_name,
                bai.query_column_name
            FROM attributes a
            JOIN base_attribute_info bai ON a.id = bai.attribute_id
            JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
            JOIN entities e ON aq.entity_id = e.id
            WHERE aq.entity_id = $1
            ORDER BY a.name
        `

		var attributes []dtos.AttributeInfo
		err := c.PostgresClient.SelectContext(ctx, &attributes, attributesQuery, entity.Id)
		if err != nil {
			logger.Error("Error fetching attributes for entity", "entityId", entity.Id, "error", err)
			continue
		}

		allAttributes = append(allAttributes, attributes...)
	}

	return allAttributes, nil
}

// buildDerivedEntityViewQuery constructs the SQL query to create a view for a derived entity
func (c Controller) BuildDerivedEntityViewQuery(
	ctx context.Context,
	derivedEntity *dtos.EntityInfo,
	parentEntities []dtos.EntityInfo,
	joins []dtos.JoinInfo,
) (string, error) {
	// Get attributes from the attribute controller
	allAttributes, err := c.GetAttributesForEntities(ctx, parentEntities)
	if err != nil {
		return "", err
	}

	// Build the CREATE OR REPLACE VIEW query
	viewQuery := fmt.Sprintf("CREATE OR REPLACE VIEW %s.%s.%s AS\nSELECT\n",
		c.StarrocksDefaultCatalog, c.StarrocksDefaultDatabase, *derivedEntity.TableName)

	// Add attribute selections with table name prefixes to avoid column name conflicts
	var selections []string
	for _, attr := range allAttributes {
		selections = append(selections,
			fmt.Sprintf("    %s.%s AS %s_%s",
				*attr.TableName,
				attr.QueryColumnName,
				*attr.TableName,
				attr.QueryColumnName))
	}
	viewQuery += strings.Join(selections, ",\n")

	// Find source entity for FROM clause (first parent entity)
	firstEntity := parentEntities[0]

	// Add FROM clause starting with the first parent entity using its catalog and schema from postgres
	viewQuery += fmt.Sprintf("\nFROM %s.%s.%s\n",
		*firstEntity.CatalogName,
		*firstEntity.SchemaName,
		*firstEntity.TableName)

	// Add JOIN clauses for other parent entities
	for i := 0; i < len(joins); i++ {
		join := joins[i]
		// Find the entity table names, catalogs, and schemas
		var sourceTable, targetTable, targetCatalog, targetSchema string

		for _, parent := range parentEntities {
			if parent.Id == join.SourceEntityId {
				sourceTable = *parent.TableName
			}
			if parent.Id == join.TargetEntityId {
				targetTable = *parent.TableName
				targetCatalog = *parent.CatalogName
				targetSchema = *parent.SchemaName
			}
		}

		viewQuery += fmt.Sprintf("%s JOIN %s.%s.%s ON %s.%s = %s.%s\n",
			join.JoinType,
			targetCatalog,
			targetSchema,
			targetTable,
			sourceTable,
			join.SourceColumn,
			targetTable,
			join.TargetColumn)
	}

	return viewQuery, nil
}

// RefreshDerivedEntityView creates or refreshes the view for a derived entity
func (c Controller) refreshDerivedEntityView(ctx context.Context, derivedEntityId int64) error {
	// Get derived entity details
	derivedEntity, err := c.GetEntityDetails(ctx, derivedEntityId)
	if err != nil {
		return err
	}

	// Get parent entities
	parentEntities, err := c.GetParentEntities(ctx, derivedEntityId)
	if err != nil {
		return err
	}

	if len(parentEntities) < 2 {
		return fmt.Errorf("derived entity must have at least 2 parent entities: %d", derivedEntityId)
	}

	// Get entity join information
	joins, err := c.GetEntityJoins(ctx, derivedEntityId)
	if err != nil {
		return err
	}

	// Build and execute the view creation query
	viewQuery, err := c.BuildDerivedEntityViewQuery(ctx, derivedEntity, parentEntities, joins)
	if err != nil {
		return err
	}
	// fmt.Print("viewQuery : ", viewQuery)

	// Execute the query to create or replace the view
	_, err = c.StarrocksClient.ExecContext(ctx, viewQuery)
	if err != nil {
		return fmt.Errorf("error creating view for derived entity %d: %w", derivedEntityId, err)
	}

	logger.Info("Successfully refreshed view for derived entity",
		"derivedEntityId", derivedEntityId,
		"viewName", derivedEntity.TableName)

	return nil
}

func (c Controller) RefreshDependentViews(ctx context.Context, entityId int64) error {
	// Find all derived entities that depend on the given entity
	derivedEntityIds, err := c.FindDerivedEntitiesByParent(ctx, entityId)
	if err != nil {
		return err
	}

	// If no derived entities depend on this entity, nothing to do
	if len(derivedEntityIds) == 0 {
		logger.Info("No derived entities depend on this entity", "entityId", entityId)
		return nil
	}

	// Process each derived entity
	for _, derivedEntityId := range derivedEntityIds {
		err = c.refreshDerivedEntityView(ctx, derivedEntityId)
		if err != nil {
			return fmt.Errorf("error refreshing view for derived entity %d: %w", derivedEntityId, err)
		}
	}

	return nil
}
