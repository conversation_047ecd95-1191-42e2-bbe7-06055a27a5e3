package trinocontroller

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	trino "github.com/Zomato/cdp-platform/pkg/trino"
	"github.com/Zomato/go/logger"
	log "github.com/Zomato/go/logger"
)

const (
	ResponseChannelCapacity int = 10000
)

type Controller struct {
	*basecontroller.Controller
}

func (c *Controller) getTrinoConfig(trinoConfigMap json.RawMessage) (*trino.Config, error) {
	var config trino.Config
	if err := json.Unmarshal([]byte(trinoConfigMap), &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Trino config: %w", err)
	}

	return &config, nil
}

func (c *Controller) PreviewSQL(ctx context.Context, trinoConfigMap json.RawMessage, query string, impersonateUserEmail *string) (*dtos.ResultTable, error) {
	log.Infof("Previewing SQL query: %s", query)
	return c.ExecuteSQLSimplyfied(ctx, trinoConfigMap, query, impersonateUserEmail)
	// return c.ExecuteSQL(ctx, previewQuery.EngineName, previewQuery.Query)
}

// kept non-deterministic as we want separate table name on each retry
func (c *Controller) CreateHiveTable(ctx context.Context, trinoConfigMap json.RawMessage, query string, attributeQueryID int64) (string, error) {

	queryHash := sha256.Sum256([]byte(query))
	randomSuffix := time.Now().UnixNano() % 1000 // Generate a small random value based on timestamp
	syncTableName := fmt.Sprintf("attribute_sync_%x_%d_%d", queryHash[:], randomSuffix, attributeQueryID)
	createTableQuery := fmt.Sprintf(`
		CREATE TABLE %s.%s.%s 
		WITH ( format = 'PARQUET' )
		AS
		SELECT * FROM (%s) AS source_query
	`, c.TrinoHiveCatalog, c.TrinoHiveDatabase, syncTableName, query)

	logger.Info(createTableQuery)

	_, err := c.ExecuteSQLSimplyfied(ctx, trinoConfigMap, createTableQuery, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create table %s: %w", syncTableName, err)
	}

	return syncTableName, nil
}

func (c *Controller) DropTable(ctx context.Context, trinoConfigMap json.RawMessage, tableName string) error {
	fullTableName := fmt.Sprintf("%s.%s.%s", c.TrinoHiveCatalog, c.TrinoHiveDatabase, tableName)
	dropQuery := fmt.Sprintf("DROP TABLE IF EXISTS %s", fullTableName)

	logger.Infof("Executing DROP TABLE query: %s", dropQuery)

	_, err := c.ExecuteSQLSimplyfied(ctx, trinoConfigMap, dropQuery, nil)
	if err != nil {
		return fmt.Errorf("failed to drop table %s: %w", fullTableName, err)
	}

	logger.Infof("Successfully dropped table: %s", fullTableName)
	return nil
}

func (c *Controller) ExecuteSQLSimplyfied(ctx context.Context, trinoConfigMap json.RawMessage, query string, impersonateUserEmail *string) (*dtos.ResultTable, error) {
	log.Infof("Executing SQL query: %s", query)

	// Get Trino configuration
	trinoConfig, err := c.getTrinoConfig(trinoConfigMap)
	if err != nil {
		return nil, err
	}

	// Get Trino client
	trinoClient, err := trino.GetTrinoClient(trinoConfig)
	if err != nil {
		log.WithError(err).Error("Failed to create Trino client")
		return nil, err
	}
	defer trinoClient.Close()

	// Execute the query
	queryUser := trinoConfig.Username
	if impersonateUserEmail != nil {
		queryUser = *impersonateUserEmail
	}
	rows, err := trinoClient.QueryContext(ctx, query, sql.Named("X-Trino-User", queryUser))
	if err != nil {
		log.WithError(err).Error("Query execution failed")
		return nil, err
	}
	defer rows.Close()

	// Process results
	return c.processQueryResults(rows)
}

func (c *Controller) processQueryResults(rows *sql.Rows) (*dtos.ResultTable, error) {
	// Fetch column metadata
	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		log.WithError(err).Error("Failed to fetch column types")
		return nil, err
	}

	// Extract column names and types
	var columnNames, columnSchemas []string
	for _, colType := range columnTypes {
		columnNames = append(columnNames, colType.Name())
		columnSchemas = append(columnSchemas, trino.GetColType(colType.DatabaseTypeName()))
	}

	// Initialize result table
	resultTable := &dtos.ResultTable{
		Rows: []*dtos.Row{},
		Schema: &dtos.MessageSchema{
			ColumnNames:   columnNames,
			ColumnSchemas: columnSchemas,
		},
	}

	// Read rows
	for rows.Next() {
		cols := make([]interface{}, len(columnNames))
		columnPointers := make([]interface{}, len(columnNames))
		for i := range cols {
			columnPointers[i] = &cols[i]
		}

		// Scan the row into the column pointers
		if err := rows.Scan(columnPointers...); err != nil {
			log.WithError(err).Error("Failed to scan row")
			return nil, err
		}

		// Convert the scanned row into the result format
		row := &dtos.Row{}
		for i, col := range cols {
			row.Columns = append(row.Columns, &dtos.AnyValue{
				Type:  columnSchemas[i],
				Value: col,
			})
		}
		resultTable.Rows = append(resultTable.Rows, row)
	}

	if err := rows.Err(); err != nil {
		log.WithError(err).Error("Error occurred during row iteration")
		return nil, err
	}

	// Populate statistics
	resultTable.RowCount = int32(len(resultTable.Rows))
	resultTable.ColumnCount = int32(len(columnNames))
	return resultTable, nil
}

// TODO: Fix this for large row querying

// type processorConfig struct {
// 	startTime   time.Time
// 	noOfRows    int32
// 	noOfColumns int32
// }

// func (c *Controller) SyncAttributeQueryToIceberg(ctx context.Context, attributeQueryId int64, attributeSyncRunId int64) (string, error) {
// 	attributeSyncRunUpdated := dtos.AttributeSyncRuns{
// 		Base:   dtos.Base{Id: attributeSyncRunId},
// 		Status: "STARTED",
// 	}

// 	if err := c.AttributesSyncRunsController.UpdateAttributeSyncRun(ctx, &attributeSyncRunUpdated); err != nil {
// 		return "", fmt.Errorf("failed to update attribute run status %d: %w", attributeQueryId, err)
// 	}

// 	query, err := c.AttributeQueriesController.GetAttributeQuery(ctx, attributeQueryId)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to get attribute info for attributeId %d: %w", attributeQueryId, err)
// 	}

// 	queryHash := sha256.Sum256([]byte(query))
// 	syncTableName := fmt.Sprintf("cdp_sync_%x_%d", queryHash[:], attributeSyncRunId)

// 	createTableQuery := fmt.Sprintf(`
// 		CREATE OR REPLACE TABLE %s.%s AS
// 		SELECT * FROM (%s) AS source_query
// 	`, DEFAULT_SYNC_SCHEMA, syncTableName, query)

// 	if _, err = c.ExecuteSQL(ctx, createTableQuery); err != nil {
// 		return "", fmt.Errorf("failed to create table %s: %w", syncTableName, err)
// 	}

// 	attributeSyncRunUpdated = dtos.AttributeSyncRuns{
// 		Base:          dtos.Base{Id: attributeSyncRunId},
// 		Status:        "INTERMEDIATE",
// 		SyncTableName: syncTableName,
// 	}

// 	if err = c.AttributesSyncRunsController.UpdateAttributeSyncRun(ctx, &attributeSyncRunUpdated); err != nil {
// 		return "", fmt.Errorf("error updating run status %s: %w", syncTableName, err)
// 	}

// 	return syncTableName, nil
// }

// func (c *Controller) TableExists(ctx context.Context, schema string, tableName string) (bool, error) {
// 	if schema == "" || tableName == "" {
// 		return false, nil
// 	}

// 	query := fmt.Sprintf(`
// 		SELECT column_name, data_type
// 		FROM blinkit_iceberg.information_schema.columns
// 		WHERE table_schema = '%s' AND table_name = '%s'
// 		ORDER BY ordinal_position
// 	`, schema, tableName)

// 	result, err := c.ExecuteSQL(ctx, query)
// 	if err != nil {
// 		return false, fmt.Errorf("failed to get membership status of table %s in schema %s: %w", tableName, schema, err)
// 	}

// 	return result.RowCount > 0, nil
// }

// func (c *Controller) ExecuteSQL(ctx context.Context, engineName, query string) (*dtos.ResultTable, error) {
// 	log.Infof("Executing SQL query: %s", query)

// 	trinoConfig, err := c.getTrinoConfig(ctx, engineName)
// 	if err != nil {
// 		return nil, err
// 	}

// 	replyChan := make(chan *trino.Result, ResponseChannelCapacity)
// 	completed := make(chan struct{})
// 	errorChan := make(chan error)
// 	resultChan := make(chan *dtos.ResultTable)

// 	var wg sync.WaitGroup
// 	processor := &processorConfig{
// 		startTime:   time.Now(),
// 		noOfRows:    0,
// 		noOfColumns: 0,
// 	}

// 	runRequest := &trino.RunRequest{
// 		Query:     query,
// 		ReplyChan: replyChan,
// 		ShutdownChannel: &trino.ShutdownChannel{
// 			Completed: completed,
// 			ErrorChan: errorChan,
// 		},
// 		TrinoConfig: trinoConfig,
// 	}

// 	wg.Add(1)
// 	go func() {
// 		defer wg.Done()
// 		trino.ExecuteSQL(ctx, *runRequest)
// 	}()

// 	go c.processResponses(ctx, processor, replyChan, resultChan)

// 	return c.waitForCompletion(errorChan, completed, resultChan, &wg)
// }

// func (c *Controller) processResponses(ctx context.Context, processorConfig *processorConfig, replyChan chan *trino.Result, resultChan chan *dtos.ResultTable) {
// 	// Initialize result with an empty ResultTable object
// 	resultTable := &dtos.ResultTable{
// 		Rows: []*dtos.Row{},
// 	}

// 	for {
// 		select {
// 		case response := <-replyChan:
// 			processorConfig.noOfRows++
// 			if resultTable.Schema == nil && response.Schema != nil {
// 				resultTable.Schema = response.Schema
// 				processorConfig.noOfColumns = int32(len(resultTable.Schema.ColumnNames))
// 				log.Infof("Schema received with %d columns", processorConfig.noOfColumns)
// 			}
// 			if response.Row != nil {
// 				resultTable.Rows = append(resultTable.Rows, response.Row)
// 			}

// 		case <-ctx.Done():
// 			log.Info("Context done, exiting processResponses")
// 			populateResultStatistics(processorConfig, resultTable)
// 			resultChan <- resultTable

// 		case <-c.completed:
// 			log.Info("Completed processing, exiting processResponses")
// 			populateResultStatistics(processorConfig, resultTable)
// 			resultChan <- resultTable
// 		}
// 	}
// }

// func (c *Controller) waitForCompletion(errorChan chan error, completed chan struct{}, resultChan chan *dtos.ResultTable, wg *sync.WaitGroup) (*dtos.ResultTable, error) {
// 	for {
// 		select {
// 		case err := <-errorChan:
// 			log.WithError(err).Error("Query execution failed")
// 			c.completed <- struct{}{}
// 			return nil, err
// 		case <-completed:
// 			wg.Wait()
// 			c.completed <- struct{}{}
// 			resultTable := <-resultChan
// 			return resultTable, nil
// 		}
// 	}
// }

// func populateResultStatistics(processorConfig *processorConfig, resultTable *dtos.ResultTable) {
// 	resultTable.RowCount = processorConfig.noOfRows
// 	resultTable.ColumnCount = processorConfig.noOfColumns
// 	resultTable.ProcessingTimeMS = time.Since(processorConfig.startTime).Milliseconds()
// }
