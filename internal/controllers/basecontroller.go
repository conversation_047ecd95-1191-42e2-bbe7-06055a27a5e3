package basecontroller

import (
	"context"

	"github.com/Zomato/cdp-platform/internal/database/postgres"
	"github.com/Zomato/cdp-platform/internal/database/starrocks"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/cdp-platform/pkg/emr"
	featurestore "github.com/Zomato/cdp-platform/pkg/feature-store"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/cdp-platform/pkg/s3"
	"github.com/Zomato/cdp-platform/pkg/temporal"
	"github.com/Zomato/go/config"
)

type Controller struct {
	// Core clients (always required)
	PostgresClient  *postgres.DB
	StarrocksClient *starrocks.DB
	TemporalClient  *temporal.Client
	KafkaProducer   *kafka.Producer
	S3Client        *s3.Client
	EMRClient       *emr.Client

	// Optional clients (based on configuration)
	AppsflyerClient    *appsflyer.Client
	FeaturestoreClient *featurestore.Client

	// StarRocks configs
	StarrocksHiveCatalog     string
	StarrocksHiveDatabase    string
	StarrocksIcebergCatalog  string
	StarrocksIcebergSchema   string
	StarrocksDefaultCatalog  string
	StarrocksDefaultDatabase string
	UserAttributeTable       string

	// Storage configs
	BrokerLoadS3Bucket   string
	BrokerLoadIAMRoleARN string
	BrokerLoadAWSRegion  string

	// Query Engine configs
	TrinoHiveCatalog  string
	TrinoHiveDatabase string

	// Appsflyer configs (only initialized if AppsFlyer is enabled)
	AppsflyerKafkaTopic   string
	AppsflyerAndroidAppID string
	AppsflyerIOSAppID     string
	AppsflyerDLQTopic     string

	// Temporal configs
	TemporalTaskQueue string

	// Service configs
	ServiceEnvironment string

	// EMR configs
	OrgName string
}

func NewController(ctx context.Context, postgresClient *postgres.DB, starrocksClient *starrocks.DB, temporalClient *temporal.Client, kafkaProducer *kafka.Producer, appsflyerClient *appsflyer.Client, s3Client *s3.Client, emrClient *emr.Client, featurestoreClient *featurestore.Client) *Controller {
	controller := &Controller{
		// Core clients
		PostgresClient:  postgresClient,
		StarrocksClient: starrocksClient,
		TemporalClient:  temporalClient,
		KafkaProducer:   kafkaProducer,
		S3Client:        s3Client,
		EMRClient:       emrClient,

		// Optional clients (can be nil if disabled)
		AppsflyerClient:    appsflyerClient,
		FeaturestoreClient: featurestoreClient,

		// StarRocks configs
		StarrocksHiveCatalog:     config.GetString(ctx, "starrocks.catalog.hive.name"),
		StarrocksHiveDatabase:    config.GetString(ctx, "starrocks.catalog.hive.schema"),
		StarrocksIcebergCatalog:  config.GetString(ctx, "starrocks.catalog.iceberg.name"),
		StarrocksIcebergSchema:   config.GetString(ctx, "starrocks.catalog.iceberg.schema"),
		StarrocksDefaultCatalog:  config.GetString(ctx, "starrocks.catalog.default.name"),
		StarrocksDefaultDatabase: config.GetString(ctx, "starrocks.catalog.default.schema"),
		UserAttributeTable:       config.GetString(ctx, "starrocks.catalog.default.tables.user_attribute"),

		// Storage configs
		BrokerLoadS3Bucket:   config.GetString(ctx, "starrocks.storage.s3.broker_load_bucket"),
		BrokerLoadIAMRoleARN: config.GetString(ctx, "starrocks.storage.s3.aws_iam_role_arn"),
		BrokerLoadAWSRegion:  config.GetString(ctx, "starrocks.storage.s3.aws_region"),

		// Query Engine configs
		TrinoHiveCatalog:  config.GetString(ctx, "query_engine.trino.hive.catalog"),
		TrinoHiveDatabase: config.GetString(ctx, "query_engine.trino.hive.schema"),

		// Temporal configs
		TemporalTaskQueue: config.GetString(ctx, "temporal.task_queue"),

		// Service configs
		ServiceEnvironment: config.GetString(ctx, "service.environment"),

		// EMR configs
		OrgName: config.GetString(ctx, "org"),
	}

	// Only initialize AppsFlyer configs if AppsFlyer client is enabled
	enableAppsflyer := config.GetBool(ctx, "appsflyer.enable")
	if enableAppsflyer && appsflyerClient != nil {
		controller.AppsflyerKafkaTopic = config.GetString(ctx, "appsflyer_consumer.kafka_topic")
		controller.AppsflyerAndroidAppID = config.GetString(ctx, "appsflyer.android_app_id")
		controller.AppsflyerIOSAppID = config.GetString(ctx, "appsflyer.ios_app_id")
		controller.AppsflyerDLQTopic = config.GetString(ctx, "appsflyer_consumer.dlq_topic")
	}

	return controller
}
