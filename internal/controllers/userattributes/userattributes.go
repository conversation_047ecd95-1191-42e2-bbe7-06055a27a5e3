package userattributes

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	attributesyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/attributesyncruns"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	"github.com/Zomato/go/logger"
	log "github.com/Zomato/go/logger"
	"github.com/pkg/errors"
)

type Controller struct {
	*basecontroller.Controller
	AttributeSyncRunsController *attributesyncrunscontroller.Controller
	StarrocksController         *starrockscontroller.Controller
}

type ColumnDiff struct {
	ColumnName      string
	SourceType      string
	DestinationType string
}

// SyncTableSchema handles the schema comparison and alteration between iceberg and native tables
func (c Controller) SyncAttributeSchema(ctx context.Context, syncTableSchemaAndData *dtos.SyncAttibute) ([]dtos.TableColumn, error) {
	// Compare schemas between iceberg and native table
	comparison, err := c.StarrocksController.CompareTableColumns(ctx,
		c.StarrocksHiveCatalog,
		c.StarrocksHiveDatabase,
		syncTableSchemaAndData.SyncTableName,
		c.StarrocksDefaultCatalog,
		c.StarrocksDefaultDatabase,
		c.UserAttributeTable,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to compare table schemas")
	}

	// Check for incompatible columns first
	var incompatibleCols []ColumnDiff
	for _, col := range comparison.ConflictColumns {
		incompatibleCols = append(incompatibleCols, ColumnDiff{
			ColumnName:      col.ColumnName,
			SourceType:      col.DataType,
			DestinationType: col.DataType,
		})
	}

	// If we found any incompatible columns, log them and exit
	if len(incompatibleCols) > 0 {
		for _, col := range incompatibleCols {
			log.Info("Schema mismatch found", map[string]interface{}{
				"column":     col.ColumnName,
				"sourceType": col.SourceType,
				"destType":   col.DestinationType,
			})
		}
		return nil, fmt.Errorf("found %d columns with incompatible schemas, sync aborted", len(incompatibleCols))
	}

	// Alter the native table for new columns
	for _, col := range comparison.NewColumns {
		err := c.StarrocksController.AddColumn(ctx, c.StarrocksDefaultCatalog, c.StarrocksDefaultDatabase, c.UserAttributeTable, col)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to add column: %s", col.ColumnName)
		}
	}

	// since all the columns have been added we will return both new and matching columns
	return append(comparison.NewColumns, comparison.MatchingColumns...), nil
}

// SyncTableData handles the data synchronization between the source and target tables
func (c Controller) SyncAttributeData(ctx context.Context, syncAttibute *dtos.SyncAttibute) error {
	// Compare schemas to get matching columns

	var syncColumnsNames []string

	for _, column := range syncAttibute.SyncColumns {
		syncColumnsNames = append(syncColumnsNames, column.ColumnName)
	}
	logger.Infof("Following columns will be synced %s", strings.Join(syncColumnsNames, ","))

	if len(syncColumnsNames) > 0 {
		brokerLoadQuery := fmt.Sprintf(`
			LOAD LABEL %s.%s_%s
			(
				DATA INFILE("s3://%s/%s/%s/*")
				INTO TABLE %s
				FORMAT AS "parquet"
				(%s)
			)
			WITH BROKER 
			(
				"aws.s3.use_instance_profile" = "true",
				"aws.s3.iam_role_arn" = "%s",
				"aws.s3.region" = "%s",
				"aws.s3.use_aws_sdk_default_behavior" = "true",
				"aws.glue.use_aws_sdk_default_behavior" = "true"
			)
			PROPERTIES
			(
				"partial_update_mode" = "column",
				"partial_update" = "true"
			)`,
			c.StarrocksDefaultDatabase,
			c.UserAttributeTable,
			syncAttibute.SyncTableName,
			c.BrokerLoadS3Bucket,
			c.StarrocksHiveDatabase,
			syncAttibute.SyncTableName,
			c.UserAttributeTable,
			strings.Join(syncColumnsNames, ","),
			c.BrokerLoadIAMRoleARN,
			c.BrokerLoadAWSRegion,
		)
		logger.Infof("Broker load query: %s", brokerLoadQuery)

		_, err := c.StarrocksClient.ExecContext(ctx, brokerLoadQuery)
		if err != nil {
			return errors.Wrap(err, "failed to execute broker load")
		}
		logger.Info("Broker load initiated successfully")
	}

	return nil
}

// returns an error of sync is not completed and @LoadJobStatus status if sync is completed
func (c Controller) CheckSyncDataStatus(ctx context.Context, syncTableName string) (*dtos.LoadJobStatus, error) {
	loadJobName := c.UserAttributeTable + "_" + syncTableName
	logger.Infof("Checking sync status for load job %s", loadJobName)

	const maxRetries = 60
	const retryInterval = 60 * time.Second

	for range maxRetries {
		loadJobStatus, err := c.StarrocksController.GetLoadJobStatus(ctx, loadJobName)
		if err != nil {
			// it is possible that there maybe a temporary connection issue causing this failures in getting status from starrocks
			logger.Warnf("Failed to get load job status. Retrying in %v seconds. Error: %v", retryInterval, err)
			time.Sleep(retryInterval)
			continue
		}

		if !loadJobStatus.IsComplete {
			logger.Infof("Load job is not complete. Rechecking status in %v seconds.", retryInterval)
			time.Sleep(retryInterval)
			continue
		}

		if loadJobStatus.Status == "FINISHED" {
			return loadJobStatus, nil
		}

		if loadJobStatus.Status == "CANCELLED" {
			return nil, fmt.Errorf("Load job cancelled. Error: %s", loadJobStatus.ErrorMessage)
		}
	}

	return nil, fmt.Errorf("Failed to get sync status or sync is not completed for load job after 60 minutes. Load job name: %s", loadJobName)
}

// SyncTableSchemaAndData orchestrates both schema and data synchronization
func (c Controller) SyncAttibute(ctx context.Context, syncAttibute *dtos.SyncAttibute) error {
	// Step 1: Sync the schema
	addedColumns, err := c.SyncAttributeSchema(ctx, syncAttibute)
	if err != nil {
		return errors.Wrap(err, "failed to sync table schema")
	}
	syncAttibute.SyncColumns = addedColumns
	// Step 2: Sync the data
	err = c.SyncAttributeData(ctx, syncAttibute)
	if err != nil {
		return errors.Wrap(err, "failed to sync table data")
	}

	return nil
}
