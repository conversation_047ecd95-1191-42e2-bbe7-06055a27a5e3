package attributequeriescontroller

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	attributesyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/attributesyncruns"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	queryenginecontroller "github.com/Zomato/cdp-platform/internal/controllers/queryengine.go"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/enum"
	utils "github.com/Zomato/cdp-platform/utils"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	AttributeSyncRunsController *attributesyncrunscontroller.Controller
	QueryEngineController       *queryenginecontroller.Controller
	TemporalController          *temporalcontroller.Controller
	DataSourceController        *datasourcecontroller.Controller
}

func (c Controller) GetAttributeQuery(ctx context.Context, attributeQueryID int64) (string, error) {
	query := `
		SELECT query_text
		FROM attribute_queries
		WHERE id = $1`

	var queryText *string
	err := c.PostgresClient.QueryRowContext(ctx, query, attributeQueryID).Scan(&queryText)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.New("attribute query not found")
		}
		return "", err
	}

	if queryText == nil {
		return "", fmt.Errorf("IllegalState : query string is null")
	}
	return utils.StringOrEmpty(queryText), nil
}

func (c Controller) GetAllAttributeQueries(ctx context.Context) ([]dtos.AttributeQueryInfo, error) {
	query := `
        SELECT
            aq.id,
            aq.cron_expression,
            aq.query_text,
			aq.data_source_id,
			aq.entity_id,
			aq.end_date,
            aq.created_at,
            aq.updated_at,
            latest_run.sync_table_name,
            COALESCE(latest_run.status, 'REGISTERED') as latest_status
        FROM attribute_queries aq
        LEFT JOIN LATERAL (
            SELECT
                asr.sync_table_name,
                asr.status
            FROM attribute_sync_runs asr
            WHERE asr.attribute_query_id = aq.id
            ORDER BY asr.execution_start DESC
            LIMIT 1
        ) latest_run ON true
        WHERE aq.end_date > NOW()
        ORDER BY aq.id
    `

	var queries []dtos.AttributeQueryInfo
	err := c.PostgresClient.SelectContext(ctx, &queries, query)
	if err != nil {
		logger.Info("Error fetching attribute queries from postgres", err)
		return nil, err
	}

	return queries, nil
}

func (c Controller) GetAttributeQueryByID(ctx context.Context, id int64) (*dtos.AttributeQueryInfo, error) {
	attributeQuery, err := c.fetchAttributeQuery(ctx, id)
	if err != nil {
		return nil, err
	}

	attributes, err := c.fetchAttributes(ctx, id)
	if err != nil {
		return nil, err
	}
	attributeQuery.Attributes = attributes

	runLogs, err := c.fetchRunLogs(ctx, id)
	if err != nil {
		return nil, err
	}
	attributeQuery.RunLogs = runLogs

	// Get the data source type to determine the correct workflow type
	dataSourceInfo, err := c.DataSourceController.GetDataSourceConfigMapAndType(ctx, attributeQuery.DataSourceID)
	if err != nil {
		logger.Info("Error fetching data source info", err)
		return nil, err
	}

	var workflowType enum.WorkflowType
	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		workflowType = enum.SyncAttributeQueryWorkflow
	case enum.Spark:
		workflowType = enum.SyncAttributeQueryWorkflowSpark
	default:
		return nil, fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: workflowType,
		Args:         []interface{}{id},
	}

	nextRuns, err := c.TemporalController.GetSchedulePreview(ctx, scheduleInput, 10)
	if err != nil {
		logger.Info("Error fetching schedule preview", err)
		attributeQuery.NextRuns = []time.Time{}
	} else {
		attributeQuery.NextRuns = nextRuns
	}

	return attributeQuery, nil
}

func (c Controller) ManualTrigger(ctx context.Context, id int64) error {

	// Get the data source type
	dataSourceInfo, err := c.GetDataSourceInfoByAttributeQueryID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get data source info: %w", err)
	}

	// Set the appropriate workflow type based on the data source type
	var workflowType enum.WorkflowType
	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		workflowType = enum.SyncAttributeQueryWorkflow
	case enum.Spark:
		workflowType = enum.SyncAttributeQueryWorkflowSpark
	default:
		// Use Trino workflow as default for backward compatibility
		workflowType = enum.SyncAttributeQueryWorkflow
	}

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: workflowType,
		Args:         []interface{}{id},
	}

	err = c.TemporalController.TriggerSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}
	return nil
}
func (c Controller) GetDataSourceInfoByAttributeQueryID(ctx context.Context, attributeQueryID int64) (*dtos.DataSourceInfo, error) {
	var dataSourceInfo dtos.DataSourceInfo

	query := `
		SELECT dsc.data_source_type, ds.config_map
		FROM data_sources ds
		JOIN data_source_configs dsc ON ds.data_source_config_id = dsc.id
		JOIN attribute_queries aq ON ds.id = aq.data_source_id
		WHERE aq.id = $1
	`

	err := c.PostgresClient.GetContext(ctx, &dataSourceInfo, query, attributeQueryID)
	if err != nil {
		logger.Info("Error fetching data source info from postgres", err)
		return nil, err
	}

	return &dataSourceInfo, nil
}

func (c Controller) fetchAttributeQuery(ctx context.Context, id int64) (*dtos.AttributeQueryInfo, error) {
	query := `
        SELECT
            aq.id,
            aq.cron_expression,
            aq.query_text,
			aq.data_source_id,
			aq.entity_id,
			aq.owner_id,
			aq.end_date,
            aq.created_at,
            aq.updated_at,
            latest_run.sync_table_name,
            latest_run.status as latest_status
        FROM attribute_queries aq
        LEFT JOIN LATERAL (
            SELECT
                asr.sync_table_name,
                asr.status
            FROM attribute_sync_runs asr
            WHERE asr.attribute_query_id = aq.id
            ORDER BY asr.execution_start DESC
            LIMIT 1
        ) latest_run ON true
        WHERE aq.id = $1
    `

	var attributeQuery dtos.AttributeQueryInfo
	err := c.PostgresClient.GetContext(ctx, &attributeQuery, query, id)
	if err != nil {
		logger.Info("Error fetching attribute query by ID from postgres", err)
		return nil, err
	}

	return &attributeQuery, nil
}

func (c Controller) fetchAttributes(ctx context.Context, id int64) ([]dtos.AttributeInfo, error) {
	query := `
		SELECT
			a.id,
			a.name
		FROM attributes a
		JOIN base_attribute_info bai ON a.id = bai.attribute_id
		JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		WHERE bai.attribute_query_id = $1
	`

	var attributes []dtos.AttributeInfo
	err := c.PostgresClient.SelectContext(ctx, &attributes, query, id)
	if err != nil {
		logger.Info("Error fetching attributes for attribute query", err)
		return nil, err
	}

	return attributes, nil
}

func (c Controller) fetchRunLogs(ctx context.Context, id int64) ([]dtos.AttributeSyncRuns, error) {
	query := `
        SELECT
            id,
            execution_start,
            execution_end,
            status,
            sync_table_name,
            rows_affected,
            error_message,
            created_at,
            updated_at
        FROM attribute_sync_runs
        WHERE attribute_query_id = $1
        ORDER BY created_at DESC
		LIMIT 7
    `

	var runLogs []dtos.AttributeSyncRuns
	err := c.PostgresClient.SelectContext(ctx, &runLogs, query, id)
	if err != nil {
		logger.Info("Error fetching run logs for attribute query", err)
		return nil, err
	}

	return runLogs, nil
}

func (c Controller) GetConfigMapByAttributeQueryID(ctx context.Context, attributeQueryID int) (json.RawMessage, error) {
	var configMap json.RawMessage
	err := c.PostgresClient.GetContext(ctx, &configMap, `
        SELECT ds.config_map
        FROM attribute_queries aq
        JOIN data_sources ds ON aq.data_source_id = ds.id
        WHERE aq.id = $1`, attributeQueryID)

	if err == sql.ErrNoRows {
		logger.Info("No attribute query found with the given ID")
		return nil, errors.New("no attribute query found with the given ID")
	}

	if err != nil {
		logger.WithError(err).Info("Error fetching from Postgres")
		return nil, err
	}

	return configMap, nil
}

func (c Controller) ValidateAttributeQuery(ctx context.Context, validateAttributeQueryRequestDTO *dtos.ValidateAttributeQueryRequest) (*dtos.ValidateAttributeQueryResponse, error) {
	// Step 1: Fetch all existing query_column_name from base_attribute_info table for a specific entity
	var existingUserAttributeColumns []string
	query := `
		SELECT bai.query_column_name 
		FROM base_attribute_info bai
		JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		WHERE aq.entity_id = $1
	`
	err := c.PostgresClient.SelectContext(ctx, &existingUserAttributeColumns, query, validateAttributeQueryRequestDTO.EntityID)
	if err != nil {
		logger.Info("Error fetching existing query_column_names", err)
		return nil, err
	}

	// Step 2: Modify the query and call PreviewSQL
	queryEngineInput := &dtos.QueryEngineInput{
		DataSourceID: validateAttributeQueryRequestDTO.DataSourceID,
		Query:        fmt.Sprintf("SELECT * FROM (%s) AS t LIMIT 0", validateAttributeQueryRequestDTO.Query),
	}

	previewResponse, err := c.QueryEngineController.PreviewSQL(ctx, queryEngineInput, nil)
	if err != nil {
		logger.Info("Error previewing SQL", err)
		return nil, err
	}

	// Step 3: Extract Columns from previewResponse
	currentQueryColumns := previewResponse.Columns
	existingColumnsInCurrentQuery := []string{}
	for _, column := range currentQueryColumns {
		if column.Name != "user_id" && utils.StringInSlice(column.Name, existingUserAttributeColumns) {
			existingColumnsInCurrentQuery = append(existingColumnsInCurrentQuery, column.Name)
		}
	}

	// Step 4: Ensure "user_id" is present
	containsUserID := false
	for _, column := range currentQueryColumns {
		if column.Name == "user_id" {
			containsUserID = true
			break
		}
	}

	return &dtos.ValidateAttributeQueryResponse{
		ExistingColumns: existingColumnsInCurrentQuery,
		ContainsUserID:  containsUserID,
		IsValid:         (len(existingColumnsInCurrentQuery) == 0 && containsUserID),
	}, nil
}

func (c Controller) validateUpdatedQuery(ctx context.Context, attributeQueryID int64, updatedQuery string) (*dtos.AttributeQueryUpdateQueryResponse, error) {
	query := `
		SELECT query_text, data_source_id
		FROM attribute_queries
		WHERE id = $1
	`

	var currentQuery *string
	var datasourceID *int64

	err := c.PostgresClient.QueryRowContext(ctx, query, attributeQueryID).Scan(&currentQuery, &datasourceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("attribute query not found")
		}
	}

	if currentQuery == nil {
		return nil, fmt.Errorf("IllegalState: query string is null")
	}
	if datasourceID == nil {
		return nil, fmt.Errorf("IllegalState: datasource_id is null")
	}

	existingQuerySchema, err := c.QueryEngineController.GetQueryColumnSchema(ctx, &dtos.QueryEngineInput{
		DataSourceID: *datasourceID,
		Query:        *currentQuery,
	})
	if err != nil {
		return nil, fmt.Errorf("error getting schema for original query: %w", err)
	}

	updatedQuerySchema, err := c.QueryEngineController.GetQueryColumnSchema(ctx, &dtos.QueryEngineInput{
		DataSourceID: *datasourceID,
		Query:        updatedQuery,
	})
	if err != nil {
		return nil, fmt.Errorf("error getting schema for new updated query: %w", err)
	}

	changedColumns := make([]*dtos.ColumnChange, 0)
	schemasMatch := true

	for colName, existingDataType := range existingQuerySchema {
		if updatedDataType, exists := updatedQuerySchema[colName]; exists {
			if existingDataType != updatedDataType {
				changedColumns = append(changedColumns, &dtos.ColumnChange{
					ColumnName:   colName,
					ExistingType: existingDataType,
					UpdatedType:  updatedDataType,
				})
				schemasMatch = false
			}
		} else {
			changedColumns = append(changedColumns, &dtos.ColumnChange{
				ColumnName:   colName,
				ExistingType: existingDataType,
				UpdatedType:  "NOT_PRESENT",
			})
			schemasMatch = false
		}
	}

	for colName, updatedDataType := range updatedQuerySchema {
		if _, exists := existingQuerySchema[colName]; !exists {
			changedColumns = append(changedColumns, &dtos.ColumnChange{
				ColumnName:   colName,
				ExistingType: "NOT_PRESENT",
				UpdatedType:  updatedDataType,
			})
			schemasMatch = false
		}
	}

	return &dtos.AttributeQueryUpdateQueryResponse{
		SchemasMatch:   schemasMatch,
		ChangedColumns: changedColumns,
	}, nil
}

func (c Controller) UpdateQuery(ctx context.Context, attributeQueryUpdateQueryRequestDTO *dtos.AttributeQueryUpdateQueryRequest) (*dtos.AttributeQueryUpdateQueryResponse, error) {
	if attributeQueryUpdateQueryRequestDTO.QueryText == "" {
		return nil, errors.New("no fields to update")
	}

	attributeQueryUpdateQueryResponse, err := c.validateUpdatedQuery(ctx, attributeQueryUpdateQueryRequestDTO.Id, attributeQueryUpdateQueryRequestDTO.QueryText)
	if err != nil {
		return nil, errors.New("error while validating new query")
	}

	if attributeQueryUpdateQueryResponse.SchemasMatch {
		query := "UPDATE attribute_queries SET "
		var updates []string
		args := map[string]interface{}{"id": attributeQueryUpdateQueryRequestDTO.Id}

		updates = append(updates, "query_text = :query_text")
		args["query_text"] = attributeQueryUpdateQueryRequestDTO.QueryText

		query += strings.Join(updates, ", ") + ", updated_at = NOW() WHERE id = :id"
		_, err = c.PostgresClient.NamedExecContext(ctx, query, args)
		if err != nil {
			return nil, errors.New("error while updating new query")
		}
	}

	return attributeQueryUpdateQueryResponse, nil
}

func (c Controller) UpdateCron(ctx context.Context, attributeQueryUpdateCronRequestDTO *dtos.AttributeQueryUpdateCronRequest) error {

	dataSourceInfo, err := c.GetDataSourceInfoByAttributeQueryID(ctx, attributeQueryUpdateCronRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to get data source info: %w", err)
	}

	var workflowType enum.WorkflowType
	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		workflowType = enum.SyncAttributeQueryWorkflow
	case enum.Spark:
		workflowType = enum.SyncAttributeQueryWorkflowSpark
	default:
		return fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	query := "UPDATE attribute_queries SET "
	var updates []string
	args := map[string]interface{}{"id": attributeQueryUpdateCronRequestDTO.Id}

	if attributeQueryUpdateCronRequestDTO.CronExpression != "" {
		scheduleInput := dtos.ScheduleInput{
			WorkflowType: workflowType,
			Args:         []interface{}{attributeQueryUpdateCronRequestDTO.Id},
		}
		cronExpressions := []string{attributeQueryUpdateCronRequestDTO.CronExpression}
		err = c.TemporalController.UpdateCron(ctx, scheduleInput, cronExpressions)
		if err != nil {
			return fmt.Errorf("failed to update cron: %w", err)
		}

		updates = append(updates, "cron_expression = :cron_expression")
		args["cron_expression"] = attributeQueryUpdateCronRequestDTO.CronExpression
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += strings.Join(updates, ", ") + ", updated_at = NOW() WHERE id = :id"
	_, err = c.PostgresClient.NamedExecContext(ctx, query, args)

	return err
}

func (c Controller) UpdateEndDate(ctx context.Context, attributeQueryUpdateEndDateRequestDTO *dtos.AttributeQueryUpdateEndDateRequest) error {
	// Get current end date for validation
	var currentEndDate *time.Time
	query := `SELECT end_date FROM attribute_queries WHERE id = $1`
	err := c.PostgresClient.GetContext(ctx, &currentEndDate, query, attributeQueryUpdateEndDateRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to get current end date: %w", err)
	}

	// Parse and validate the new end date
	newEndDate, err := c.validateAttributeQueryEndDate(attributeQueryUpdateEndDateRequestDTO.EndDate, currentEndDate)
	if err != nil {
		return fmt.Errorf("invalid end date: %w", err)
	}

	// Get data source info for temporal schedule update
	dataSourceInfo, err := c.GetDataSourceInfoByAttributeQueryID(ctx, attributeQueryUpdateEndDateRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to get data source info: %w", err)
	}

	var workflowType enum.WorkflowType
	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		workflowType = enum.SyncAttributeQueryWorkflow
	case enum.Spark:
		workflowType = enum.SyncAttributeQueryWorkflowSpark
	default:
		return fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	// Update temporal schedule end date
	scheduleInput := dtos.ScheduleInput{
		WorkflowType: workflowType,
		Args:         []interface{}{attributeQueryUpdateEndDateRequestDTO.Id},
		ScheduleSpec: dtos.ScheduleSpecRequest{
			EndAt: newEndDate,
		},
	}

	// Get current cron expression for schedule update
	var cronExpression string
	cronQuery := `SELECT cron_expression FROM attribute_queries WHERE id = $1`
	err = c.PostgresClient.GetContext(ctx, &cronExpression, cronQuery, attributeQueryUpdateEndDateRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to get cron expression: %w", err)
	}

	cronExpressions := []string{cronExpression}
	err = c.TemporalController.UpdateScheduleEndDate(ctx, scheduleInput, cronExpressions)
	if err != nil {
		return fmt.Errorf("failed to update temporal schedule end date: %w", err)
	}

	// Update database
	updateQuery := "UPDATE attribute_queries SET end_date = $1, updated_at = NOW() WHERE id = $2"
	_, err = c.PostgresClient.ExecContext(ctx, updateQuery, newEndDate, attributeQueryUpdateEndDateRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to update end date in database: %w", err)
	}

	return nil
}

func (c Controller) GetAttributeQueryOwnerID(ctx context.Context, id int64) (int64, error) {
	var ownerID int64
	query := `SELECT owner_id FROM attribute_queries WHERE id = $1`
	err := c.PostgresClient.GetContext(ctx, &ownerID, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("attribute query not found")
		}
		return 0, fmt.Errorf("failed to get attribute query owner ID: %w", err)
	}
	return ownerID, nil
}

// validateAttributeQueryEndDate validates and parses an end date string for attribute queries
// It ensures the end date is in the future and can only be increased if currentEndDate is provided
func (c Controller) validateAttributeQueryEndDate(endDateStr string, currentEndDate *time.Time) (*time.Time, error) {
	if endDateStr == "" {
		return nil, nil
	}

	// Parse the end date using the existing time utility
	endDate, err := utils.ParseISTDateToStartOfDay(endDateStr)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format: %w", err)
	}

	// Validate that end date is in the future
	now := time.Now()
	if endDate.Before(now) {
		return nil, fmt.Errorf("end date must be in the future. Provided: %s, Current time: %s",
			endDate.Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05"))
	}

	// If currentEndDate is provided, validate that new end date is not decreased
	if currentEndDate != nil && endDate.Before(*currentEndDate) {
		return nil, fmt.Errorf("end date can only be increased, not decreased. Current: %s, New: %s",
			currentEndDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}

	return &endDate, nil
}
