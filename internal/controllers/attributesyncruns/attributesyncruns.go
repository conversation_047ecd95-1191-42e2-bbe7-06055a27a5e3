package attributesyncrunscontroller

import (
	"context"
	"errors"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	entity "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) AddAttributeSyncRun(ctx context.Context, attributeSyncRunDTO *dtos.AttributeSyncRuns) (*dtos.AttributeSyncRuns, error) {
	query := `
		INSERT INTO attribute_sync_runs (
			attribute_query_id,
			execution_start,
			status
		) VALUES (
			:attribute_query_id,
			:execution_start,
			:status
		) RETURNING id, created_at, updated_at`

	attributeSyncRunEntity := &entity.AttributeSyncRuns{
		AttributeQueryID: attributeSyncRunDTO.AttributeQueryID,
		ExecutionStart:   attributeSyncRunDTO.ExecutionStart,
		Status:           attributeSyncRunDTO.Status,
	}

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, attributeSyncRunEntity)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&attributeSyncRunDTO.Id, &attributeSyncRunDTO.CreatedAt, &attributeSyncRunDTO.UpdatedAt)
		if err != nil {
			return nil, err
		}
	}

	return attributeSyncRunDTO, nil
}

func (c Controller) UpdateAttributeSyncRun(ctx context.Context, attributeSyncRunDTO *dtos.AttributeSyncRuns) error {
	query := "UPDATE attribute_sync_runs SET "
	var updates []string
	args := map[string]interface{}{"id": attributeSyncRunDTO.Id}
	if attributeSyncRunDTO.ExecutionStart != nil && !attributeSyncRunDTO.ExecutionStart.IsZero() {
		updates = append(updates, "execution_start = :execution_start")
		args["execution_start"] = *attributeSyncRunDTO.ExecutionStart
	}
	if attributeSyncRunDTO.ExecutionEnd != nil && !attributeSyncRunDTO.ExecutionEnd.IsZero() {
		updates = append(updates, "execution_end = :execution_end")
		args["execution_end"] = *attributeSyncRunDTO.ExecutionEnd
	}
	if attributeSyncRunDTO.Status != "" {
		updates = append(updates, "status = :status")
		args["status"] = attributeSyncRunDTO.Status
	}
	if attributeSyncRunDTO.SyncTableName != nil && *attributeSyncRunDTO.SyncTableName != "" {
		updates = append(updates, "sync_table_name = :sync_table_name")
		args["sync_table_name"] = attributeSyncRunDTO.SyncTableName
	}
	if attributeSyncRunDTO.RowsAffected != nil && *(attributeSyncRunDTO.RowsAffected) != 0 {
		updates = append(updates, "rows_affected = :rows_affected")
		args["rows_affected"] = attributeSyncRunDTO.RowsAffected
	}
	if attributeSyncRunDTO.ErrorMessage != nil && *(attributeSyncRunDTO.ErrorMessage) != "" {
		updates = append(updates, "error_message = :error_message")
		args["error_message"] = attributeSyncRunDTO.ErrorMessage
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += strings.Join(updates, ", ") + " WHERE id = :id"

	_, err := c.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (c Controller) GetLatestIntermediateSyncTableName(ctx context.Context, attributeQueryID int64) (string, error) {
	query := `
		SELECT sync_table_name 
		FROM attribute_sync_runs 
		WHERE attribute_query_id = $1 
		AND status = 'INTERMEDIATE'
		ORDER BY updated_at DESC 
		LIMIT 1`

	var syncTableName string
	err := c.PostgresClient.GetContext(ctx, &syncTableName, query, attributeQueryID)
	if err != nil {
		return "", err
	}

	return syncTableName, nil
}
