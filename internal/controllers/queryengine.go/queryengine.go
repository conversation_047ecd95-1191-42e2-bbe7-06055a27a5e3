package queryenginecontroller

import (
	"context"
	"fmt"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	"github.com/Zomato/cdp-platform/internal/controllers/trinocontroller"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	TrinoController      *trinocontroller.Controller
	DataSourceController *datasourcecontroller.Controller
}

func (c Controller) PreviewSQL(ctx context.Context, queryEngineInput *dtos.QueryEngineInput, impersonateUserEmail *string) (*dtos.PreviewSQLResponse, error) {
	dataSourceInfo, err := c.DataSourceController.GetDataSourceConfigMapAndType(ctx, queryEngineInput.DataSourceID)
	if err != nil {
		return nil, err
	}

	var resultTable *dtos.ResultTable

	limitPreviewQuery := fmt.Sprintf("SELECT * FROM (%s) AS t LIMIT 10", queryEngineInput.Query)

	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		resultTable, err = c.TrinoController.PreviewSQL(ctx, dataSourceInfo.ConfigMap, limitPreviewQuery, impersonateUserEmail)
		if err != nil {
			logger.Info("Error fetching data from query engine", err)
			return nil, err
		}
	case enum.Spark:
		// TODO: Implement Spark preview
		return nil, fmt.Errorf("spark preview not implemented")
	default:
		logger.Info("Unsupported data source type:", dataSourceInfo.DataSourceType)
		return nil, fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	formattedResult := c.transformResultTable(resultTable)

	return formattedResult, nil
}

func (c Controller) GetQueryColumns(ctx context.Context, queryEngineInput *dtos.QueryEngineInput) (*dtos.PreviewColumn, error) {
	dataSourceInfo, err := c.DataSourceController.GetDataSourceConfigMapAndType(ctx, queryEngineInput.DataSourceID)
	if err != nil {
		return nil, err
	}

	var resultTable *dtos.ResultTable
	limitZeroQuery := fmt.Sprintf("SELECT * FROM (%s) AS t LIMIT 0", queryEngineInput.Query)

	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		resultTable, err = c.TrinoController.PreviewSQL(ctx, dataSourceInfo.ConfigMap, limitZeroQuery, nil)
		if err != nil {
			logger.Info("Error fetching data from query engine", err)
			return nil, err
		}
	case enum.Spark:
		// TODO: Implement Spark preview
		return nil, fmt.Errorf("spark preview not implemented")
	default:
		logger.Info("Unsupported data source type:", dataSourceInfo.DataSourceType)
		return nil, fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	formattedResult := c.transformResultTable(resultTable)
	previewColumnDto := &dtos.PreviewColumn{
		Columns: formattedResult.Columns,
	}

	return previewColumnDto, nil
}

func (c Controller) GetQueryColumnSchema(ctx context.Context, queryEngineInput *dtos.QueryEngineInput) (map[string]string, error) {
	dataSourceInfo, err := c.DataSourceController.GetDataSourceConfigMapAndType(ctx, queryEngineInput.DataSourceID)
	if err != nil {
		return nil, err
	}

	var resultTable *dtos.ResultTable
	schemaCheckQuery := fmt.Sprintf("SELECT * FROM (%s) AS t LIMIT 0", queryEngineInput.Query)

	switch dataSourceInfo.DataSourceType {
	case enum.Trino:
		resultTable, err = c.TrinoController.PreviewSQL(ctx, dataSourceInfo.ConfigMap, schemaCheckQuery, nil)
		if err != nil {
			logger.Info("Error fetching data from query engine", err)
			return nil, err
		}
	case enum.Spark:
		// TODO: Implement Spark preview
		return nil, fmt.Errorf("spark preview not implemented")
	default:
		logger.Info("Unsupported data source type:", dataSourceInfo.DataSourceType)
		return nil, fmt.Errorf("unsupported data source type: %s", dataSourceInfo.DataSourceType)
	}

	schemaMap := make(map[string]string)
	for i, _ := range resultTable.Schema.ColumnNames {
		schemaMap[resultTable.Schema.ColumnNames[i]] = resultTable.Schema.ColumnSchemas[i]
	}
	return schemaMap, nil
}

func (c Controller) transformResultTable(resultTable *dtos.ResultTable) *dtos.PreviewSQLResponse {
	if resultTable == nil {
		return nil
	}

	columns := make([]dtos.ColumnInfo, len(resultTable.Schema.ColumnNames))
	for i, name := range resultTable.Schema.ColumnNames {
		columns[i] = dtos.ColumnInfo{
			Name: name,
			Type: resultTable.Schema.ColumnSchemas[i],
		}
	}

	rows := make([]dtos.PreviewResultRow, 0, len(resultTable.Rows))
	for _, row := range resultTable.Rows {
		rowMap := make(dtos.PreviewResultRow)
		for i, col := range row.Columns {
			rowMap[resultTable.Schema.ColumnNames[i]] = col.Value
		}
		rows = append(rows, rowMap)
	}

	return &dtos.PreviewSQLResponse{
		Columns: columns,
		Rows:    rows,
		Meta: dtos.PreviewMeta{
			RowCount:         resultTable.RowCount,
			ColumnCount:      resultTable.ColumnCount,
			ProcessingTimeMS: resultTable.ProcessingTimeMS,
		},
	}
}
