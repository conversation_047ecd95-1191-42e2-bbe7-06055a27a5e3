package starrockscontroller

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/go/logger"
	"github.com/pkg/errors"
)

type Controller struct {
	*basecontroller.Controller
	TemporalController *temporalcontroller.Controller
}

// Move struct definitions to a separate entities package

type ColumnComparison struct {
	NewColumns      []dtos.TableColumn // Columns present only in source table
	MatchingColumns []dtos.TableColumn // Columns present in both with same datatype
	ConflictColumns []dtos.TableColumn // Columns present in both with different datatype
}

const (
	MAX_VARCHAR_LENGTH = 1048576 // StarRocks maximum VARCHAR length
)

// CreateMaterializedView creates a new materialized view in StarRocks database.
// It uses the provided DTO to construct the view definition and executes the creation query.
// The view is set to deferred manual refresh mode and will only be created if it doesn't already exist.
func (controller Controller) CreateMaterializedView(ctx context.Context, createMaterializedViewDTO *dtos.CreateMaterializedView) error {
	if createMaterializedViewDTO.TableName == "" || createMaterializedViewDTO.Query == "" {
		return fmt.Errorf("invalid input: table name and query are required")
	}

	createMvQuery := fmt.Sprintf(`
		CREATE MATERIALIZED VIEW IF NOT EXISTS %s.%s.%s
		REFRESH DEFERRED MANUAL
		AS 
		%s
	`, controller.StarrocksDefaultCatalog, controller.StarrocksDefaultDatabase, createMaterializedViewDTO.TableName, createMaterializedViewDTO.Query)

	_, err := controller.StarrocksClient.ExecContext(ctx, createMvQuery)
	if err != nil {
		return fmt.Errorf("failed to create materialized view: %w", err)
	}
	return nil
}

// RefreshMaterializedViewSync refreshes a materialized view synchronously using SYNC MODE.
// This method blocks until the materialized view refresh is completed.
func (controller Controller) RefreshMaterializedViewSync(ctx context.Context, mvName string) error {
	if mvName == "" {
		return fmt.Errorf("invalid input: materialized view name is required")
	}

	logger.Infof("Starting synchronous refresh of materialized view: %s", mvName)

	refreshQuery := fmt.Sprintf("REFRESH MATERIALIZED VIEW %s.%s.%s with SYNC MODE",
		controller.StarrocksDefaultCatalog,
		controller.StarrocksDefaultDatabase,
		mvName)

	_, err := controller.StarrocksClient.ExecContext(ctx, refreshQuery)
	if err != nil {
		return fmt.Errorf("failed to refresh materialized view %s: %w", mvName, err)
	}

	logger.Infof("Successfully completed synchronous refresh of materialized view: %s", mvName)
	return nil
}

// QueryDataFromStarrocks executes a query against StarRocks database and streams the results
// through the provided channel. It handles the conversion of database types to internal types
// and provides schema information with the first result.
// The caller is responsible for consuming all results from the channel to prevent goroutine leaks.
func (controller Controller) QueryDataFromStarrocks(ctx context.Context, queryDataChan chan *dtos.Result, query string) error {
	if query == "" {
		return fmt.Errorf("invalid input: query cannot be empty")
	}

	// Execute query and get result set
	rows, err := controller.StarrocksClient.QueryContext(ctx, query)
	if err != nil {
		return fmt.Errorf("error running query: %w", err)
	}
	defer rows.Close() // Ensure resources are cleaned up

	// Get column metadata
	columns, err := rows.Columns()
	if err != nil {
		return fmt.Errorf("failed to get column information: %w", err)
	}

	colTypes, err := rows.ColumnTypes()
	if err != nil {
		return fmt.Errorf("failed to get column types: %w", err)
	}

	// Prepare column metadata arrays
	var columnNames, columnSchemas []string
	for _, colType := range colTypes {
		columnNames = append(columnNames, colType.Name())
		columnSchemas = append(columnSchemas, GetColType(colType.DatabaseTypeName()))
	}

	addSchema := true // Flag to send schema only with first result
	noOfRows := 0
	logger.Infof("Starting row processing for query: %s", query)

	// Process rows
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled during query execution: %w", ctx.Err())
		default:
			if rows.Next() {
				fmt.Println("scanning rows")
				// Prepare containers for row data
				cols := make([]interface{}, len(columns))
				columnPointers := make([]interface{}, len(columns))
				for i := range columns {
					columnPointers[i] = &cols[i]
				}

				// Scan row data
				if err := rows.Scan(columnPointers...); err != nil {
					return fmt.Errorf("failed to scan row: %w", err)
				}

				// Process columns and create result
				processedcols := make([]*dtos.AnyValue, 0, len(columns))
				for i := range columns {
					ptr := columnPointers[i].(*interface{})
					value := *ptr
					dataType := columnSchemas[i]
					processedcols = append(processedcols, &dtos.AnyValue{
						Type:  dataType,
						Value: value,
					})
				}

				// Create result row
				resultRow := &dtos.Result{
					Row: &dtos.Row{
						Columns: processedcols,
					},
				}

				// Add schema information to first result only
				if addSchema {
					resultRow.Schema = &dtos.MessageSchema{
						ColumnSchemas: columnSchemas,
						ColumnNames:   columnNames,
					}
					addSchema = false
				}

				// Send result through channel
				select {
				case queryDataChan <- resultRow:
					noOfRows++
					fmt.Println("noOfRows", noOfRows)
				case <-ctx.Done():
					return fmt.Errorf("context cancelled while sending results: %w", ctx.Err())
				}
			} else {
				// Handle end of result set or errors
				if err := rows.Err(); err != nil {
					return fmt.Errorf("error processing query results: %w", err)
				}

				logger.Infof("Query completed successfully. Processed %d rows", noOfRows)
				close(queryDataChan)
				return nil
			}
		}
	}
}

// GetColType maps database-specific column types to standardized internal types.
// This ensures consistent type handling across different database implementations.
func GetColType(dbType string) string {
	// Normalize type to lowercase for consistent matching
	dbType = strings.ToLower(dbType)

	switch dbType {
	case "integer", "bigint", "smallint", "tinyint":
		return "INT"
	case "varchar", "char", "text":
		return "STRING"
	case "double", "real", "float":
		return "DOUBLE"
	case "boolean", "bool":
		return "BOOLEAN"
	default:
		logger.Warnf("Unmapped database type encountered: %s", dbType)
		return dbType
	}
}

func (c Controller) DescribeTable(ctx context.Context, catalogName, database, tableName string) ([]dtos.TableColumn, error) {
	// First set the catalog
	setCatalogQuery := fmt.Sprintf("SET CATALOG %s", catalogName)
	_, err := c.StarrocksClient.ExecContext(ctx, setCatalogQuery)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to set catalog to %s", catalogName)
	}

	// Now run the describe command
	query := fmt.Sprintf("DESCRIBE %s.%s", database, tableName)
	rows, err := c.StarrocksClient.QueryContext(ctx, query)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to describe table %s", tableName)
	}
	defer rows.Close()

	// First, try to get column count
	colTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get column types")
	}

	var columns []dtos.TableColumn
	for rows.Next() {
		var col dtos.TableColumn
		var defaultVal, comment sql.NullString

		if len(colTypes) == 7 {
			// Hive catalog case (7 columns)
			err = rows.Scan(
				&col.ColumnName,
				&col.DataType,
				&col.IsNullable,
				&col.Key,
				&defaultVal,
				&col.Extra,
				&comment,
			)
		} else {
			// Default catalog case (6 columns)
			err = rows.Scan(
				&col.ColumnName,
				&col.DataType,
				&col.IsNullable,
				&col.Key,
				&defaultVal,
				&col.Extra,
			)
		}

		if err != nil {
			return nil, errors.Wrap(err, "failed to scan row")
		}

		// Handle nullable Default value
		if defaultVal.Valid {
			col.DefaultValue = defaultVal.String
		}

		// Handle nullable Comment (will remain empty for default catalog)
		if comment.Valid {
			col.Comment = comment.String
		}

		columns = append(columns, col)
	}

	if err = rows.Err(); err != nil {
		return nil, errors.Wrap(err, "error iterating over rows")
	}

	return columns, nil
}

func (c Controller) CompareTableColumns(ctx context.Context,
	sourceCatalog, sourceDb, sourceTable, targetCatalog, targetDb, targetTable string) (*ColumnComparison, error) {

	sourceColumns, err := c.DescribeTable(ctx, sourceCatalog, sourceDb, sourceTable)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to describe source table %s", sourceTable)
	}

	targetColumns, err := c.DescribeTable(ctx, targetCatalog, targetDb, targetTable)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to describe target table %s", targetTable)
	}

	targetColumnMap := make(map[string]dtos.TableColumn)
	for _, col := range targetColumns {
		targetColumnMap[col.ColumnName] = col
	}

	comparison := &ColumnComparison{
		NewColumns:      make([]dtos.TableColumn, 0),
		MatchingColumns: make([]dtos.TableColumn, 0),
		ConflictColumns: make([]dtos.TableColumn, 0),
	}

	for _, sourceCol := range sourceColumns {
		if targetCol, exists := targetColumnMap[sourceCol.ColumnName]; exists {
			if normalizeDataType(sourceCol.DataType) == normalizeDataType(targetCol.DataType) {
				comparison.MatchingColumns = append(comparison.MatchingColumns, sourceCol)
			} else {
				comparison.ConflictColumns = append(comparison.ConflictColumns, sourceCol)
			}
		} else {
			comparison.NewColumns = append(comparison.NewColumns, sourceCol)
		}
	}

	return comparison, nil
}

// normalizeDataType helps in comparing data types by normalizing them
func normalizeDataType(dataType string) string {
	// Convert to lowercase for case-insensitive comparison
	dataType = strings.ToLower(dataType)

	// Handle VARCHAR with length specification
	if strings.HasPrefix(dataType, "varchar(") {
		// For comparison purposes, we just return varchar
		return "varchar"
	}

	// Add mappings for equivalent data types
	switch dataType {
	case "int", "integer", "int32":
		return "int"
	case "bigint", "long", "int64":
		return "bigint"
	case "float", "real":
		return "float"
	case "double", "float64":
		return "double"
	case "boolean", "bool":
		return "boolean"
	case "timestamp", "datetime":
		return "datetime"
	default:
		return dataType
	}
}

// adjustDataTypeForStarRocks modifies data types to be compatible with StarRocks
func (controller Controller) AdjustDataTypeForStarRocks(dataType string) (string, error) {
	dataType = strings.ToLower(dataType)

	// Handle VARCHAR with length specification
	if strings.HasPrefix(dataType, "varchar(") {
		// Extract the length
		lengthStr := strings.TrimPrefix(strings.TrimSuffix(dataType, ")"), "varchar(")
		length, err := strconv.Atoi(lengthStr)
		if err != nil {
			// If we can't parse the length, use maximum allowed
			return fmt.Sprintf("VARCHAR(%d)", MAX_VARCHAR_LENGTH), nil
		}
		// Adjust length if it exceeds maximum
		if length > MAX_VARCHAR_LENGTH {
			return fmt.Sprintf("VARCHAR(%d)", MAX_VARCHAR_LENGTH), nil
		}
	}

	return dataType, nil
}

func (controller Controller) AddColumn(ctx context.Context, catalogName, schemaName, tableName string, col dtos.TableColumn) error {
	logger.Infof("Adding column %s with data type %s to table %s.%s.%s", col.ColumnName, col.DataType, catalogName, schemaName, tableName)

	adjustedDataType, _ := controller.AdjustDataTypeForStarRocks(col.DataType)

	alterTableStatement := fmt.Sprintf(
		"ALTER TABLE %s.%s.%s ADD COLUMN %s %s",
		catalogName,
		schemaName,
		tableName,
		col.ColumnName,
		adjustedDataType)

	// If source column is nullable (IsNullable = "YES"), make it nullable in StarRocks
	if col.IsNullable == "YES" {
		alterTableStatement += " NULL"
	}

	// Add default value only if one was specified in the source
	if col.DefaultValue != "" {
		alterTableStatement += fmt.Sprintf(" DEFAULT %s", col.DefaultValue)
	}

	_, err := controller.StarrocksClient.ExecContext(ctx, alterTableStatement)

	if err != nil {
		return errors.Wrapf(err, "failed to add column %s to table %s.%s.%s", col.ColumnName, catalogName, schemaName, tableName)
	}
	logger.Infof("Column %s with data type %s added successfully to table %s.%s.%s", col.ColumnName, col.DataType, catalogName, schemaName, tableName)
	return nil
}

func (controller Controller) CreateHiveTableFromMV(ctx context.Context, runID int64, mvName string, audienceTableName string) error {
	// Validate input params
	if audienceTableName == "" || mvName == "" {
		return fmt.Errorf("invalid input: audience table name, materialized view name are required")
	}

	// First, get the schema of the materialized view
	mvColumns, err := controller.DescribeTable(ctx, controller.StarrocksDefaultCatalog, controller.StarrocksDefaultDatabase, mvName)
	if err != nil {
		return errors.Wrapf(err, "failed to describe materialized view %s", mvName)
	}

	// Build the columns part of the query
	columnsDefinition := ""
	for i, col := range mvColumns {
		// Convert StarRocks data type to Hive compatible type
		hiveType, err := controller.convertToHiveDataType(col.DataType)
		if err != nil {
			return errors.Wrapf(err, "failed to convert data type for column %s", col.ColumnName)
		}

		// Start with column name and type
		columnDef := fmt.Sprintf("    %s %s", col.ColumnName, hiveType)

		// Add nullability
		if col.IsNullable == "YES" {
			columnDef += " NULL"
		} else {
			columnDef += " NOT NULL"
		}

		// Add default value if exists
		if col.DefaultValue != "" {
			columnDef += fmt.Sprintf(" DEFAULT %s", col.DefaultValue)
		}

		// Add comment if exists
		if col.Comment != "" {
			columnDef += fmt.Sprintf(" COMMENT '%s'", col.Comment)
		}

		// Add to the columns definition with comma if not the last column
		if i < len(mvColumns)-1 {
			columnsDefinition += columnDef + ", "
		} else {
			columnsDefinition += columnDef
		}
	}

	// Add dt and hr columns to the table definition for date and hour partitioning
	columnsDefinition += ", dt STRING, hr STRING"

	// Define partition by clause - using dt and hr instead of run_id
	partitionDefinition := "PARTITION BY (dt, hr)"

	// Create the full CREATE TABLE query
	createTableQuery := fmt.Sprintf(
		"CREATE TABLE IF NOT EXISTS `%s`.`%s`.`%s` (%s) %s PROPERTIES (\"file_format\" = \"parquet\")",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		audienceTableName,
		columnsDefinition,
		partitionDefinition,
	)

	logger.Infof("Create table query: %s", createTableQuery)

	// Execute the CREATE TABLE statement
	_, err = controller.StarrocksClient.ExecContext(ctx, createTableQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to create Hive table %s", audienceTableName)
	}

	// Calculate current date and hour in IST timezone
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return errors.Wrap(err, "failed to load IST timezone")
	}
	nowIST := time.Now().In(istLocation)
	currentDateIST := nowIST.Format("20060102") // YYYYMMDD format
	currentHourIST := nowIST.Format("15")       // HH format (00-23)

	// Modified insert query - don't include dt and hr in SELECT, add them as partition values
	// Get column names from mvColumns to generate specific column list
	var columnNames []string
	for _, col := range mvColumns {
		columnNames = append(columnNames, col.ColumnName)
	}

	columnList := strings.Join(columnNames, ", ")

	insertQuery := fmt.Sprintf(
		"INSERT INTO `%s`.`%s`.`%s` PARTITION (dt='%s', hr='%s') SELECT %s FROM `%s`.`%s`.`%s`",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		audienceTableName,
		currentDateIST,
		currentHourIST,
		columnList,
		controller.StarrocksDefaultCatalog,
		controller.StarrocksDefaultDatabase,
		mvName,
	)

	logger.Infof("Insert query: %s", insertQuery)

	_, err = controller.StarrocksClient.ExecContext(ctx, insertQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to insert data into Hive table %s", audienceTableName)
	}

	return nil
}

func (controller Controller) CreateHiveLogTableFromHiveRunTable(ctx context.Context, hiveRunTable string, hiveLogTableName string) error {
	if hiveRunTable == "" {
		return fmt.Errorf("invalid input: hive run table name is required")
	}

	hiveColumns, err := controller.DescribeTable(ctx, controller.StarrocksHiveCatalog, controller.StarrocksHiveDatabase, hiveRunTable)
	if err != nil {
		return errors.Wrapf(err, "failed to describe hive run table %s", hiveRunTable)
	}

	columnsDefinition := ""
	for i, col := range hiveColumns {
		hiveType, err := controller.convertToHiveDataType(col.DataType)
		if err != nil {
			return errors.Wrapf(err, "failed to convert data type for column %s", col.ColumnName)
		}
		columnDef := fmt.Sprintf("    %s %s", col.ColumnName, hiveType)

		if col.IsNullable == "YES" {
			columnDef += " NULL"
		} else {
			columnDef += " NOT NULL"
		}

		if col.DefaultValue != "" {
			columnDef += fmt.Sprintf(" DEFAULT %s", col.DefaultValue)
		}

		if col.Comment != "" {
			columnDef += fmt.Sprintf(" COMMENT '%s'", col.Comment)
		}

		if i < len(hiveColumns)-1 {
			columnsDefinition += columnDef + ", "
		} else {
			columnsDefinition += columnDef
		}
	}

	columnsDefinition += ", dt STRING, hr STRING"

	partitionDefinition := "PARTITION BY (dt, hr)"

	createTableQuery := fmt.Sprintf(
		"CREATE TABLE IF NOT EXISTS `%s`.`%s`.`%s` (%s) %s PROPERTIES (\"file_format\" = \"parquet\")",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		hiveLogTableName,
		columnsDefinition,
		partitionDefinition,
	)
	logger.Infof("Create table query: %s", createTableQuery)

	_, err = controller.StarrocksClient.ExecContext(ctx, createTableQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to create Hive table %s", hiveLogTableName)
	}

	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return errors.Wrap(err, "failed to load IST timezone")
	}
	nowIST := time.Now().In(istLocation)
	currentDateIST := nowIST.Format("20060102") // YYYYMMDD format
	currentHourIST := nowIST.Format("15")       // HH format (00-23)

	var columnNames []string
	for _, col := range hiveColumns {
		columnNames = append(columnNames, col.ColumnName)
	}

	columnList := strings.Join(columnNames, ", ")

	insertQuery := fmt.Sprintf(
		"INSERT INTO `%s`.`%s`.`%s` PARTITION (dt='%s', hr='%s') SELECT %s FROM `%s`.`%s`.`%s`",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		hiveLogTableName,
		currentDateIST,
		currentHourIST,
		columnList,
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		hiveRunTable,
	)

	logger.Infof("Insert query: %s", insertQuery)

	_, err = controller.StarrocksClient.ExecContext(ctx, insertQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to insert data into Hive table %s", hiveLogTableName)
	}

	return nil
}
func (controller Controller) GetLoadJobStatus(ctx context.Context, loadLabel string) (*dtos.LoadJobStatus, error) {
	const query = `
        SELECT state, error_msg, scan_rows
        FROM information_schema.loads
        WHERE label = ?`

	var state string
	var errorMsg *string
	var scanRows *int64
	err := controller.StarrocksClient.QueryRowContext(ctx, query, loadLabel).Scan(&state, &errorMsg, &scanRows)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get load job status for label %s", loadLabel)
	}
	// Map states to LoadJobStatus
	switch state {
	case "FINISHED":
		return &dtos.LoadJobStatus{
			IsComplete: true,
			Status:     state,
			ScanRows:   *scanRows,
		}, nil
	case "CANCELLED":
		return &dtos.LoadJobStatus{
			IsComplete:   true,
			Status:       state,
			ErrorMessage: *errorMsg,
			ScanRows:     *scanRows,
		}, nil
	default:
		return &dtos.LoadJobStatus{
			IsComplete: false,
			Status:     "LOADING",
			ScanRows:   *scanRows,
		}, nil
	}
}

// Helper function to convert StarRocks data types to Hive-compatible types
func (controller Controller) convertToHiveDataType(starrocksType string) (string, error) {
	// Normalize the type name to lowercase
	starrocksType = strings.ToLower(starrocksType)

	// Handle VARCHAR with length specification
	if strings.HasPrefix(starrocksType, "varchar(") {
		// For Hive, we'll use STRING type instead of VARCHAR
		return "STRING", nil
	}

	// Map StarRocks types to Hive types
	switch starrocksType {
	case "tinyint":
		return "TINYINT", nil
	case "smallint":
		return "SMALLINT", nil
	case "int", "integer":
		return "INT", nil
	case "bigint":
		return "BIGINT", nil
	case "float":
		return "FLOAT", nil
	case "double", "real":
		return "DOUBLE", nil
	case "decimal", "numeric":
		return "DECIMAL(38,10)", nil
	case "boolean", "bool":
		return "BOOLEAN", nil
	case "date":
		return "DATE", nil
	case "datetime", "timestamp", "timestamp without time zone":
		return "TIMESTAMP", nil
	case "char", "varchar", "text", "string":
		return "STRING", nil
	case "binary", "varbinary":
		return "BINARY", nil
	default:
		return "", fmt.Errorf("unsupported data type conversion: %s", starrocksType)
	}
}

type DistinctValuesResult struct {
	DistinctCount int64         `json:"distinct_count"`
	DataType      string        `json:"data_type"`
	Values        []interface{} `json:"values"`
}

func (controller Controller) GetDistinctValuesForColumn(ctx context.Context, columnExpr, dataType, catalogName, schemaName, tableName string) (*DistinctValuesResult, error) {
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT %s) FROM %s.%s.%s",
		columnExpr,
		catalogName,
		schemaName,
		tableName)

	var distinctCount int64
	err := controller.StarrocksClient.GetContext(ctx, &distinctCount, countQuery)
	if err != nil {
		return nil, fmt.Errorf("error getting distinct count: %w", err)
	}

	result := &DistinctValuesResult{
		DistinctCount: distinctCount,
		DataType:      dataType,
		Values:        make([]interface{}, 0),
	}

	if distinctCount < 10000 {
		valuesQuery := fmt.Sprintf(`
            SELECT DISTINCT %s 
            FROM %s.%s.%s 
            WHERE (%s) IS NOT NULL 
            ORDER BY %s 
            LIMIT 10000`,
			columnExpr,
			catalogName,
			schemaName,
			tableName,
			columnExpr,
			columnExpr)

		rows, err := controller.StarrocksClient.QueryContext(ctx, valuesQuery)
		if err != nil {
			return nil, fmt.Errorf("error fetching distinct values: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			var value interface{}
			switch normalizeDataType(dataType) {
			case "int", "integer", "bigint", "smallint", "tinyint":
				var v sql.NullInt64
				if err := rows.Scan(&v); err != nil {
					return nil, fmt.Errorf("error scanning row: %w", err)
				}
				if v.Valid {
					value = v.Int64
				}
			case "double", "float", "real":
				var v sql.NullFloat64
				if err := rows.Scan(&v); err != nil {
					return nil, fmt.Errorf("error scanning row: %w", err)
				}
				if v.Valid {
					value = v.Float64
				}
			case "boolean", "bool":
				var v sql.NullBool
				if err := rows.Scan(&v); err != nil {
					return nil, fmt.Errorf("error scanning row: %w", err)
				}
				if v.Valid {
					value = v.Bool
				}
			case "timestamp", "datetime":
				var v sql.NullTime
				if err := rows.Scan(&v); err != nil {
					return nil, fmt.Errorf("error scanning row: %w", err)
				}
				if v.Valid {
					value = v.Time
				}
			default: // string, varchar, text, etc.
				var v sql.NullString
				if err := rows.Scan(&v); err != nil {
					return nil, fmt.Errorf("error scanning row: %w", err)
				}
				if v.Valid && v.String != "" {
					value = v.String
				}
			}

			if value != nil {
				result.Values = append(result.Values, value)
			}
		}

		if err = rows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating rows: %w", err)
		}
	}

	return result, nil
}

func (controller Controller) TriggerDatabaseBackup(ctx context.Context, request *dtos.DatabaseBackupRequest) error {
	if request.DatabaseName == "" || request.BackupRepo == "" {
		return errors.New("database name and backup repo are required")
	}
	backupRequestIdentifier := fmt.Sprintf("backup_%s_%s", request.DatabaseName, request.BackupRepo)

	workflowInput := dtos.TriggerWorkflowInput{
		WorkflowType: enum.StarrocksDatabaseBackupWorkflow,
		Args:         []interface{}{backupRequestIdentifier, request.DatabaseName, request.BackupRepo},
	}

	return controller.TemporalController.TriggerWorkflow(ctx, workflowInput)
}

func (controller Controller) ScheduleDatabaseBackup(ctx context.Context, request *dtos.DatabaseBackupRequest) error {
	if request.DatabaseName == "" || request.BackupRepo == "" || request.CronExpression == "" {
		return errors.New("database name, backup repo, and cron expression are required")
	}
	backupRequestIdentifier := fmt.Sprintf("backup_%s_%s", request.DatabaseName, request.BackupRepo)

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.StarrocksDatabaseBackupWorkflow,
		ScheduleSpec: dtos.ScheduleSpecRequest{
			CronExpressions: []string{request.CronExpression},
		},
		Args: []interface{}{backupRequestIdentifier, request.DatabaseName, request.BackupRepo},
	}

	return controller.TemporalController.CreateSchedule(ctx, scheduleInput)
}

func (controller Controller) UpdateDatabaseBackupCron(ctx context.Context, request *dtos.DatabaseBackupUpdateCronRequest) error {
	if request.DatabaseName == "" || request.BackupRepo == "" || request.CronExpression == "" {
		return errors.New("database name, backup repo, and cron expression are required")
	}
	backupRequestIdentifier := fmt.Sprintf("backup_%s_%s", request.DatabaseName, request.BackupRepo)

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.StarrocksDatabaseBackupWorkflow,
		Args:         []interface{}{backupRequestIdentifier, request.DatabaseName, request.BackupRepo},
	}

	cronExpressions := []string{request.CronExpression}
	return controller.TemporalController.UpdateCron(ctx, scheduleInput, cronExpressions)
}

func (controller Controller) BackupDatabase(ctx context.Context, starrocksBackupRequest *dtos.DatabaseBackupRequest) (*string, error) {
	databaseName := starrocksBackupRequest.DatabaseName
	backupRepo := starrocksBackupRequest.BackupRepo

	if databaseName == "" || backupRepo == "" {
		return nil, fmt.Errorf("invalid input: database name and backup repo are required")
	}

	snapshotName := fmt.Sprintf("snapshot_%s_%s_%d", backupRepo, databaseName, time.Now().Unix())
	logger.Infof("Initiating backup for database %s to repo %s with snapshot name %s", databaseName, backupRepo, snapshotName)

	backupQuery := fmt.Sprintf(`BACKUP DATABASE %s SNAPSHOT %s TO %s`,
		databaseName,
		snapshotName,
		backupRepo)

	logger.Infof("Executing backup query: %s", backupQuery)

	_, err := controller.StarrocksClient.ExecContext(ctx, backupQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to backup database %s to repo %s: %w", databaseName, backupRepo, err)
	}

	// Query to check snapshot status
	snapshotQuery := fmt.Sprintf(`SHOW SNAPSHOT ON %s WHERE snapshot = '%s'`, backupRepo, snapshotName)

	type snapshotStatus struct {
		Snapshot  string `db:"Snapshot"`
		Timestamp string `db:"Timestamp"`
		Status    string `db:"Status"`
	}
	maxPollAttempts := 10
	for i := range maxPollAttempts {
		logger.Infof("Polling snapshot status for %s (Attempt %d/%d)", snapshotName, i+1, maxPollAttempts)
		var status snapshotStatus
		err = controller.StarrocksClient.QueryRowContext(ctx, snapshotQuery).Scan(&status.Snapshot, &status.Timestamp, &status.Status)
		if err != nil {
			return nil, fmt.Errorf("failed to get snapshot status for %s: %w", snapshotName, err)
		}
		if status.Status == "OK" {
			logger.Infof("Successfully backed up database %s to repo %s with snapshot %s (Timestamp: %s)",
				databaseName, backupRepo, snapshotName, status.Timestamp)
			return &snapshotName, nil
		}
		time.Sleep(time.Second * 300)
	}
	return nil, fmt.Errorf("backup operation timed out for database %s", databaseName)
}

// CreateIcebergTable creates a new unpartitioned Iceberg table in StarRocks using the provided configuration.
// It constructs the CREATE TABLE statement with appropriate Iceberg format properties and
// executes it against the StarRocks database.
func (controller Controller) CreateIcebergTable(ctx context.Context, createIcebergTableDTO *dtos.CreateIcebergTable) error {
	if createIcebergTableDTO.TableName == "" || createIcebergTableDTO.Query == "" {
		return fmt.Errorf("invalid input: table name and query are required")
	}

	// Construct the full qualified table name using configured Iceberg catalog and schema
	fullTableName := fmt.Sprintf("`%s`.`%s`.`%s`",
		controller.StarrocksIcebergCatalog,
		controller.StarrocksIcebergSchema,
		createIcebergTableDTO.TableName)

	createTableQuery := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s 
		PROPERTIES (
			"format" = "parquet"
		)
		AS 
		SELECT * FROM (%s) AS source_query
	`, fullTableName, createIcebergTableDTO.Query)

	logger.Infof("Creating Iceberg table with query: %s", createTableQuery)

	// Execute the CREATE TABLE statement
	_, err := controller.StarrocksClient.ExecContext(ctx, createTableQuery)
	if err != nil {
		return fmt.Errorf("failed to create Iceberg table %s: %w", createIcebergTableDTO.TableName, err)
	}

	logger.Infof("Successfully created Iceberg table: %s", fullTableName)
	return nil
}
func (controller Controller) InsertOverwrite(ctx context.Context, catalogName, schemaName, tableName, query string) error {
	fullTableName := fmt.Sprintf("%s.%s.%s", catalogName, schemaName, tableName)
	insertQuery := fmt.Sprintf(`
		INSERT OVERWRITE %s
		SELECT * FROM (%s) AS source_query
	`, fullTableName, query)

	logger.Infof("Executing INSERT OVERWRITE query: %s", insertQuery)

	_, err := controller.StarrocksClient.ExecContext(ctx, insertQuery)
	if err != nil {
		return fmt.Errorf("failed to insert overwrite into table %s: %w", fullTableName, err)
	}

	logger.Infof("Successfully executed INSERT OVERWRITE into table: %s", fullTableName)
	return nil
}

// CreateHiveTable creates a new partitioned Hive table in StarRocks using the provided configuration.
// It constructs the CREATE TABLE statement with appropriate Hive format properties and
// executes it against the StarRocks database with the provided query.
func (controller Controller) CreateHiveTableFromQuery(ctx context.Context, createHiveTableDTO *dtos.CreateHiveTable) error {
	if createHiveTableDTO.TableName == "" || createHiveTableDTO.Query == "" {
		return fmt.Errorf("invalid input: table name and query are required")
	}

	// Construct the full qualified table name using configured Hive catalog and database
	fullTableName := fmt.Sprintf("`%s`.`%s`.`%s`",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		createHiveTableDTO.TableName)

	createTableQuery := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s 
		PROPERTIES (
			"file_format" = "parquet"
		)
		AS 
		SELECT * FROM (%s) AS source_query
	`, fullTableName, createHiveTableDTO.Query)

	logger.Infof("Creating Hive table with query: %s", createTableQuery)

	// Execute the CREATE TABLE statement
	_, err := controller.StarrocksClient.ExecContext(ctx, createTableQuery)
	if err != nil {
		return fmt.Errorf("failed to create Hive table %s: %w", createHiveTableDTO.TableName, err)
	}

	logger.Infof("Successfully created Hive table: %s", fullTableName)
	return nil
}

func (controller Controller) CreateFeatureStoreLogTable(ctx context.Context, request *dtos.CreateFeatureStoreLogTable) error {
	// Validate input parameters
	if request.SourceIcebergTable == "" || request.LogTableName == "" || request.FeatureName == "" || request.PartitionDt == "" || request.PartitionHr == "" {
		return fmt.Errorf("invalid input: all fields are required")
	}

	logger.Infof("Creating feature store log table %s from iceberg table %s", request.LogTableName, request.SourceIcebergTable)

	// Define the log table columns - user_id, feature_name, dt, hr
	columnsDefinition := fmt.Sprintf(`
		user_id BIGINT NOT NULL,
		%s BOOLEAN NOT NULL,
		dt STRING NOT NULL,
		hr STRING NOT NULL
	`, request.FeatureName)

	// Define partition by clause for dt and hr
	partitionDefinition := "PARTITION BY (dt, hr)"

	// Create the full CREATE TABLE query
	createTableQuery := fmt.Sprintf(
		"CREATE TABLE IF NOT EXISTS `%s`.`%s`.`%s` (%s) %s PROPERTIES (\"file_format\" = \"parquet\")",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		request.LogTableName,
		columnsDefinition,
		partitionDefinition,
	)

	logger.Infof("Create log table query: %s", createTableQuery)

	// Execute the CREATE TABLE statement
	_, err := controller.StarrocksClient.ExecContext(ctx, createTableQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to create feature store log table %s", request.LogTableName)
	}

	// Insert data from iceberg table to log table with partitioning
	insertQuery := fmt.Sprintf(
		"INSERT OVERWRITE `%s`.`%s`.`%s` PARTITION (dt='%s', hr='%s') SELECT user_id, %s FROM `%s`.`%s`.`%s`",
		controller.StarrocksHiveCatalog,
		controller.StarrocksHiveDatabase,
		request.LogTableName,
		request.PartitionDt,
		request.PartitionHr,
		request.FeatureName,
		controller.StarrocksIcebergCatalog,
		controller.StarrocksIcebergSchema,
		request.SourceIcebergTable,
	)

	logger.Infof("Insert log table query: %s", insertQuery)

	_, err = controller.StarrocksClient.ExecContext(ctx, insertQuery)
	if err != nil {
		return errors.Wrapf(err, "failed to insert data into feature store log table %s", request.LogTableName)
	}

	logger.Infof("Successfully created and populated feature store log table: %s with partition dt=%s, hr=%s",
		request.LogTableName, request.PartitionDt, request.PartitionHr)

	return nil
}
