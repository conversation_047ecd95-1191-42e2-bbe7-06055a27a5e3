package cleanupcontroller

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	datasourcecontroller "github.com/Zomato/cdp-platform/internal/controllers/datasource"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/controllers/trinocontroller"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	DatasourceController *datasourcecontroller.Controller
	TemporalController   *temporalcontroller.Controller
	TrinoController      *trinocontroller.Controller
}

func (c *Controller) ValidateScheduleAttributeQueriesCleanupRequest(ctx context.Context, request *dtos.ScheduleAttributeQueriesCleanupRequest) error {
	if request.DatasourceID == 0 {
		return fmt.Errorf("datasource_id is required")
	}
	if request.RetentionRunCount == 0 {
		return fmt.Errorf("retention_run_count is required")
	}
	if request.CronExpression == "" {
		return fmt.Errorf("cron_expression is required")
	}
	return nil
}

func (c *Controller) ScheduleAttributeQueriesCleanup(ctx context.Context, request *dtos.ScheduleAttributeQueriesCleanupRequest) (*dtos.ScheduleAttributeQueriesCleanupResponse, error) {
	logger.Infof("Processing schedule cleanup request for datasource %d", request.DatasourceID)

	err := c.ValidateScheduleAttributeQueriesCleanupRequest(ctx, request)
	if err != nil {
		return nil, err
	}

	_, err = c.DatasourceController.GetDataSourceConfigMapAndType(ctx, request.DatasourceID)
	if err != nil {
		logger.Error("Datasource not found", "datasourceID", request.DatasourceID, "error", err)
		return nil, fmt.Errorf("datasource with ID %d not found", request.DatasourceID)
	}

	var response *dtos.ScheduleAttributeQueriesCleanupResponse

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.AttributeQueriesCleanupWorkflow,
		ScheduleSpec: dtos.ScheduleSpecRequest{
			CronExpressions: []string{request.CronExpression},
		},
		Args: []interface{}{request.DatasourceID, request.RetentionRunCount},
	}

	err = c.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		logger.Error("Failed to create cleanup schedule", "error", err)
		return nil, fmt.Errorf("failed to create cleanup schedule: %w", err)
	}

	response = &dtos.ScheduleAttributeQueriesCleanupResponse{
		Message:   "Cleanup schedule created successfully",
		CreatedAt: time.Now(),
	}

	return response, nil
}

func (c *Controller) TriggerAttributeQueriesCleanup(ctx context.Context, request *dtos.TriggerAttributeQueriesCleanupRequest) (*dtos.TriggerAttributeQueriesCleanupResponse, error) {
	logger.Infof("Processing trigger cleanup request for datasource %d", request.DatasourceID)

	_, err := c.DatasourceController.GetDataSourceConfigMapAndType(ctx, request.DatasourceID)
	if err != nil {
		logger.Error("Datasource not found", "datasourceID", request.DatasourceID, "error", err)
		return nil, fmt.Errorf("datasource with ID %d not found", request.DatasourceID)
	}

	triggerInput := dtos.TriggerWorkflowInput{
		WorkflowType: enum.AttributeQueriesCleanupWorkflow,
		Args:         []interface{}{request.DatasourceID, request.RetentionRunCount},
	}

	err = c.TemporalController.TriggerWorkflow(ctx, triggerInput)
	if err != nil {
		logger.Error("Failed to trigger cleanup workflow", "error", err)
		return nil, fmt.Errorf("failed to trigger cleanup workflow: %w", err)
	}

	response := &dtos.TriggerAttributeQueriesCleanupResponse{
		TriggeredAt: time.Now(),
		Message:     "Cleanup workflow triggered successfully",
	}

	return response, nil
}

func (c *Controller) DropExpiredTables(ctx context.Context, expiredTables *dtos.ExpiredSyncRunTablesResponse, dataSourceType enum.DataSourceType, configMap json.RawMessage) (*dtos.AttributeQueriesCleanupWorkflowResult, error) {
	logger.Infof("Dropping %d expired tables for datasource type: %s", len(expiredTables.ExpiredTables), dataSourceType)

	result := &dtos.AttributeQueriesCleanupWorkflowResult{
		TablesDropped: 0,
		Errors:        []string{},
	}
	if dataSourceType == "spark" {
		logger.Info("Skipping cleanup for Spark datasource")
		result.Message = "No cleanup required for Spark datasource"
		result.SkippedReason = "Spark datasources do not require temporary table cleanup"
		return result, nil
	}

	var errors []string
	tablesDropped := 0

	for _, tableName := range expiredTables.ExpiredTables {
		if tableName == "" {
			logger.Warn("Skipping empty table name")
			continue
		}
		logger.Infof("Dropping table: %s", tableName)
		switch dataSourceType {
		case enum.Trino:
			err := c.TrinoController.DropTable(ctx, configMap, tableName)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to drop table %s: %v", tableName, err))
			} else {
				logger.Infof("Successfully dropped table: %s", tableName)
				tablesDropped++
			}
		default:
			logger.Warn("Unsupported datasource type for cleanup", "type", dataSourceType)
		}
	}

	if len(errors) == 0 {
		result.Message = fmt.Sprintf("Successfully dropped %d tables", tablesDropped)
	} else {
		result.Message = fmt.Sprintf("Dropped %d tables with %d errors", tablesDropped, len(errors))
	}

	result.TablesDropped = tablesDropped
	result.Errors = errors

	return result, nil
}
