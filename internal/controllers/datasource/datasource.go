package datasourcecontroller

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"

	logger "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) AddDataSource(ctx context.Context, dataSource *dtos.DataSource) (*dtos.DataSource, error) {
	// Fetch config fields for the data source type
	var configFieldsJSON json.RawMessage
	err := c.PostgresClient.GetContext(ctx, &configFieldsJSON,
		`SELECT config_fields FROM data_source_configs WHERE id = $1`,
		dataSource.DataSourceConfigID)
	if err != nil {
		logger.Error("Error fetching config fields for data source config ", err)
		return nil, err
	}

	var configFields map[string]bool
	err = json.Unmarshal(configFieldsJSON, &configFields)
	if err != nil {
		logger.Info("Error unmarshaling config fields", err)
		return nil, err
	}

	// Validate the config map
	var configMap map[string]interface{}
	err = json.Unmarshal(dataSource.ConfigMap, &configMap)
	if err != nil {
		logger.Info("Error unmarshaling config map", err)
		return nil, err
	}

	for field, required := range configFields {
		if required {
			if _, ok := configMap[field]; !ok {
				return nil, fmt.Errorf("missing required field in config map: %s", field)
			}
		}
	}

	query := `INSERT INTO data_sources(
        tenant_id,
        name,
        config_map,
        data_source_config_id
    )
    VALUES(
        :tenant_id,
        :name,
        :config_map,
        :data_source_config_id
    ) RETURNING id, created_at, updated_at`

	dataSourceEntity := &postgres.DataSource{
		TenantID:           dataSource.TenantID,
		Name:               dataSource.Name,
		ConfigMap:          dataSource.ConfigMap,
		DataSourceConfigID: dataSource.DataSourceConfigID,
	}

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, dataSourceEntity)
	if err != nil {
		logger.Info("Error in adding data source in postgres ", err)
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&dataSource.Id, &dataSource.CreatedAt, &dataSource.UpdatedAt)
		if err != nil {
			logger.Info("Error scanning returned values", err)
			return nil, err
		}
	}

	return dataSource, nil
}

func (c Controller) GetDataSourceConfigMapAndType(ctx context.Context, dataSourceID int64) (*dtos.DataSourceInfo, error) {
	var dataSourceInfo dtos.DataSourceInfo

	query := `
		SELECT dsc.data_source_type, ds.config_map
		FROM data_sources ds
		JOIN data_source_configs dsc ON ds.data_source_config_id = dsc.id
		WHERE ds.id = $1
	`

	err := c.PostgresClient.GetContext(ctx, &dataSourceInfo, query, dataSourceID)
	if err != nil {
		logger.Info("Error fetching data source info from postgres", err)
		return nil, err
	}

	return &dataSourceInfo, nil
}

func (c Controller) GetAllDataSources(ctx context.Context) ([]dtos.DataSourceInfo, error) {
	query := `
		SELECT ds.id, ds.name, dsc.data_source_type, ds.created_at, ds.updated_at
		FROM data_sources ds
		JOIN data_source_configs dsc ON ds.data_source_config_id = dsc.id
		ORDER BY ds.name
	`

	var dataSources []dtos.DataSourceInfo
	err := c.PostgresClient.SelectContext(ctx, &dataSources, query)
	if err != nil {
		logger.Info("Error fetching data sources from postgres", err)
		return nil, err
	}

	return dataSources, nil
}

func (c Controller) GetDataSourceByID(ctx context.Context, id int64) (*dtos.DataSourceInfo, error) {
	query := `
		SELECT ds.id, ds.name, dsc.data_source_type, ds.created_at, ds.updated_at
		FROM data_sources ds
		JOIN data_source_configs dsc ON ds.data_source_config_id = dsc.id
		WHERE ds.id = $1
	`

	var dataSource dtos.DataSourceInfo
	err := c.PostgresClient.GetContext(ctx, &dataSource, query, id)
	if err != nil {
		logger.Info("Error fetching data source by ID from postgres", err)
		return nil, err
	}

	return &dataSource, nil
}

func (c Controller) GetExpiredAttributeSyncRunTablesByDataSource(ctx context.Context, datasourceID int64, retentionRunCount int) (*dtos.ExpiredSyncRunTablesResponse, error) {
	query := `
		SELECT sync_table_name
		FROM (
			SELECT
				asr.sync_table_name,
				ROW_NUMBER() OVER (
					PARTITION BY asr.attribute_query_id
					ORDER BY asr.execution_start DESC
				) AS row_num
			FROM attribute_sync_runs asr
			JOIN attribute_queries aq ON asr.attribute_query_id = aq.id
			WHERE aq.data_source_id = $1
				AND asr.status IN ('SUCCESSFUL', 'FAILED')
				AND asr.sync_table_name IS NOT NULL
				AND asr.sync_table_name != ''
		) sub
		WHERE sub.row_num > $2`

	var expiredTables []string
	err := c.PostgresClient.SelectContext(ctx, &expiredTables, query, datasourceID, retentionRunCount)
	if err != nil {
		return nil, fmt.Errorf("failed to query expired sync run tables: %w", err)
	}

	response := &dtos.ExpiredSyncRunTablesResponse{
		ExpiredTables: expiredTables,
	}

	return response, nil
}
