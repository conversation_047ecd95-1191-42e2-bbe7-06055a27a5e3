package destinationsynccontroller

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/go/logger"

	"github.com/Zomato/cdp-platform/dtos"
	entity "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	appsflyercontroller "github.com/Zomato/cdp-platform/internal/controllers/appsflyer"
	destinationsyncrunscontroller "github.com/Zomato/cdp-platform/internal/controllers/destinationsyncruns"
	featurestorecontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestore"
	segmentcontroller "github.com/Zomato/cdp-platform/internal/controllers/segment"
	temporalcontroller "github.com/Zomato/cdp-platform/internal/controllers/temporal"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/cdp-platform/utils"
	"github.com/jmoiron/sqlx"
)

type Controller struct {
	*basecontroller.Controller
	SegmentController             *segmentcontroller.Controller
	AppsflyerController           *appsflyercontroller.Controller
	DestinationSyncRunsController *destinationsyncrunscontroller.Controller
	TemporalController            *temporalcontroller.Controller
	FeatureStoreController        *featurestorecontroller.Controller
}

const (
	MAX_DESTINATION_SYNC_SIZE_APPSFLYER     = 40000000
	MAX_DESTINATION_SYNC_SIZE_FEATURE_STORE = 20000000
)

func (controller Controller) GetAllDestinationSyncs(ctx context.Context) ([]dtos.DestinationSyncInfo, error) {
	query := `
		WITH LastRuns AS (
			SELECT 
				destination_sync_id,
				status,
				rows_affected,
				execution_end,
				ROW_NUMBER() OVER (PARTITION BY destination_sync_id ORDER BY execution_end DESC NULLS LAST) as rn
			FROM destination_sync_runs
		)
		SELECT 
			ds.id,
			ds.created_at,
			ds.updated_at,
			s.name AS segment_name,
			d.destination_type,
			ds.sync_strategy,
			ds.cron_expression,
			u.name AS owner_name,
			lr.rows_affected AS last_sync_rows,
			lr.status AS last_sync_status,
			lr.execution_end AS last_sync_end_time,
			ds.end_date,
			
			-- Choose audience name depending on destination type
			CASE 
				WHEN d.destination_type = 'APPSFLYER' THEN aa.name
				WHEN d.destination_type = 'FEATURE_STORE' THEN f.feature_name
				ELSE NULL
			END AS audience_name

		FROM destination_syncs ds
		JOIN segments s ON ds.segment_id = s.id
		JOIN destinations d ON ds.destination_id = d.id
		JOIN users u ON ds.owner_id = u.id
		LEFT JOIN LastRuns lr ON lr.destination_sync_id = ds.id AND lr.rn = 1

		-- APPSFLYER joins
		LEFT JOIN appsflyer_sync_mappings asm ON asm.destination_sync_id = ds.id
		LEFT JOIN appsflyer_audiences aa ON aa.id = asm.audience_id

		-- FEATURE_STORE joins
		LEFT JOIN feature_store_sync_mapping fsm ON fsm.destination_sync_id = ds.id
		LEFT JOIN feature_store_segments f ON f.id = fsm.feature_id

		ORDER BY ds.created_at DESC;
	`

	var destinationSyncs []dtos.DestinationSyncInfo
	err := controller.PostgresClient.SelectContext(ctx, &destinationSyncs, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching destination syncs: %w", err)
	}

	return destinationSyncs, nil
}

func (controller Controller) GetDestinationSync(ctx context.Context, destinationSyncId int64) (*dtos.DestinationSyncInfo, error) {
	query := `
		SELECT 
			ds.id,
			s.name as segment_name,
			s.id as segment_id,
			d.destination_type,
			ds.sync_strategy,
			ds.cron_expression,
			ds.owner_id,
			ds.end_date,
			u.name as owner_name,
			COALESCE(last_sync.rows_affected, NULL) as last_sync_rows,
			COALESCE(last_sync.status, NULL) as last_sync_status,
			COALESCE(last_sync.execution_end, NULL) as last_sync_end_time,
			COALESCE(aa.name, f.feature_name) as audience_name,
			COALESCE(aa.snapshot_table_name, f.log_table_name) as snapshot_table_name,
			tg_cg.conditions as tg_cg_conditions
		FROM destination_syncs ds
		JOIN segments s ON ds.segment_id = s.id
		JOIN destinations d ON ds.destination_id = d.id
		JOIN users u ON ds.owner_id = u.id
		LEFT JOIN appsflyer_sync_mappings asm ON ds.id = asm.destination_sync_id
		LEFT JOIN appsflyer_audiences aa ON asm.audience_id = aa.id
		LEFT JOIN feature_store_sync_mapping fsm ON ds.id = fsm.destination_sync_id
		LEFT JOIN feature_store_segments f ON fsm.feature_id = f.id
		LEFT JOIN tg_cg_destination_syncs tg_cg ON ds.id = tg_cg.destination_sync_id
		LEFT JOIN LATERAL (
			SELECT 
				rows_affected,
				status,
				execution_end
			FROM destination_sync_runs dsr
			WHERE dsr.destination_sync_id = ds.id
			ORDER BY execution_start DESC
			LIMIT 1
		) last_sync ON true
		WHERE ds.id = $1
	`

	var destinationSync dtos.DestinationSyncInfo
	err := controller.PostgresClient.GetContext(ctx, &destinationSync, query, destinationSyncId)
	if err != nil {
		return nil, err
	}

	return &destinationSync, nil
}

func (controller Controller) GetDestinationSyncOverview(ctx context.Context, destinationSyncId int64) (*dtos.DestinationSyncInfo, error) {
	destinationSync, err := controller.GetDestinationSync(ctx, destinationSyncId)
	if err != nil {
		return nil, err
	}
	destinationType, err := controller.GetDestinationTypeById(ctx, destinationSyncId)
	if err != nil {
		return nil, err
	}
	syncRuns, err := controller.DestinationSyncRunsController.GetAllDestinationSyncRuns(ctx, destinationSyncId, destinationType)
	if err != nil {
		return nil, err
	}
	destinationSync.DestinationSyncRuns = syncRuns
	return destinationSync, nil
}

func (controller Controller) GetDestinationTypeById(ctx context.Context, destinationSyncId int64) (string, error) {
	query := `
		SELECT d.destination_type
		FROM destination_syncs ds
		JOIN destinations d ON ds.destination_id = d.id
		WHERE ds.id = $1
	`

	var destinationType string
	err := controller.PostgresClient.GetContext(ctx, &destinationType, query, destinationSyncId)
	if err != nil {
		return "", fmt.Errorf("failed to get destination type: %w", err)
	}

	return destinationType, nil
}

func (controller Controller) validateDestinationSyncRequest(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	if request.FeatureConfig == nil {
		return fmt.Errorf("feature config is required for feature creation")
	}
	if request.DestinationID == 0 {
		return fmt.Errorf("destination id is required")
	}
	if request.OwnerID == 0 {
		return fmt.Errorf("owner id is required")
	}
	if request.FeatureConfig.FeatureName == nil {
		return fmt.Errorf("feature name is required")
	}
	if request.FeatureConfig.Description == nil {
		return fmt.Errorf("description is required")
	}
	if request.EndDate == nil {
		return fmt.Errorf("expiry is required")
	}
	return nil
}

func (controller Controller) RegisterDestinationSyncFeatureStore(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	if err := controller.validateDestinationSyncRequest(ctx, request); err != nil {
		return err
	}
	featureConfig := dtos.CreateFeatureRequest{
		FeatureName:  request.FeatureConfig.FeatureName,
		Description:  request.FeatureConfig.Description,
		IsActive:     request.FeatureConfig.IsActive,
		ContextType:  request.FeatureConfig.ContextType,
		ContextName:  request.FeatureConfig.ContextName,
		FeatureType:  request.FeatureConfig.FeatureType,
		IsVerified:   request.FeatureConfig.IsVerified,
		IsPII:        request.FeatureConfig.IsPII,
		Destinations: request.FeatureConfig.Destinations,
		Expiry:       request.EndDate,
	}
	// Validate and set end_date based on segment attributes
	err := controller.validateDestinationSyncEndDate(ctx, request)
	if err != nil {
		return err
	}

	createFeatureResponse, err := controller.FeatureStoreController.CreateFeature(ctx, &featureConfig, &request.OwnerID, &request.DestinationID)
	if err != nil {
		return fmt.Errorf("failed to create feature: %w", err)
	}
	tx, err := controller.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	destinationSyncId, err := controller.createDestinationSyncEntry(tx, request)
	if err != nil {
		return fmt.Errorf("failed to create destination sync entry: %w", err)
	}

	mappingQuery := `
		INSERT INTO feature_store_sync_mapping (
			feature_id,
			destination_sync_id,
			created_at,
			updated_at
		) VALUES (
			:feature_id,
			:destination_sync_id,
			CURRENT_TIMESTAMP,
			CURRENT_TIMESTAMP
		)
	`

	featureSyncMapping := map[string]interface{}{
		"feature_id":          createFeatureResponse.FeatureID,
		"destination_sync_id": *destinationSyncId,
	}
	_, err = tx.NamedExec(mappingQuery, featureSyncMapping)
	if err != nil {
		return fmt.Errorf("failed to insert feature sync mapping: %w", err)
	}

	scheduleSpec := dtos.ScheduleSpecRequest{
		CronExpressions: []string{request.CronExpression},
	}

	if request.EndDate != nil && *request.EndDate != "" {
		endTime, err := utils.ParseISTDateToStartOfDay(*request.EndDate)
		if err != nil {
			return fmt.Errorf("failed to parse end date: %w", err)
		}
		scheduleSpec.EndAt = &endTime
	}

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.DestinationSyncWorkflowFeatureStore,
		ScheduleSpec: scheduleSpec,
		Args:         []interface{}{*destinationSyncId},
	}

	err = controller.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Now trigger the first sync immediately after data is committed
	triggerInput := dtos.TriggerWorkflowInput{
		WorkflowType: enum.DestinationSyncWorkflowFeatureStore,
		Args:         []interface{}{*destinationSyncId},
	}

	err = controller.TemporalController.TriggerWorkflow(ctx, triggerInput)
	if err != nil {
		// Log warning but don't fail since registration is already complete
		logger.Warn("Failed to trigger immediate first sync for destination sync, sync will start on next cron schedule",
			"destinationSyncId", *destinationSyncId, "destinationType", "FEATURE_STORE", "error", err)
	} else {
		logger.Info("Successfully triggered immediate first sync for destination sync",
			"destinationSyncId", *destinationSyncId, "destinationType", "FEATURE_STORE")
	}

	return nil
}

func (controller Controller) RegisterDestinationSyncAppsflyer(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	tx, err := controller.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	if err := controller.validateAppsflyerRequest(ctx, request); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	var audienceID int
	audience := &dtos.AppsflyerAudience{
		Name:        request.AppsflyerAudience.Name,
		Platform:    request.AppsflyerAudience.Platform,
		Description: request.AppsflyerAudience.Description,
	}

	// Validate and set end_date based on segment attributes
	err = controller.validateDestinationSyncEndDate(ctx, request)
	if err != nil {
		return err
	}

	audienceID, err = controller.AppsflyerController.CreateAudience(ctx, audience)
	if err != nil {
		return fmt.Errorf("failed to create Appsflyer audience: %w", err)
	}

	destinationSyncId, err := controller.createDestinationSyncEntry(tx, request)
	if err != nil {
		return fmt.Errorf("failed to create destination sync entry: %w", err)
	}

	mappingQuery := `
		INSERT INTO appsflyer_sync_mappings (
			destination_sync_id,
			audience_id,
			created_at,
			updated_at
		) VALUES (
			:destination_sync_id,
			:audience_id,
			CURRENT_TIMESTAMP,
			CURRENT_TIMESTAMP
		)`

	mapping := map[string]interface{}{
		"destination_sync_id": *destinationSyncId,
		"audience_id":         audienceID,
	}

	_, err = tx.NamedExec(mappingQuery, mapping)
	if err != nil {
		return fmt.Errorf("failed to insert appsflyer sync mapping: %w", err)
	}

	if request.TgCgApplied {
		var entityCondition entity.Condition
		conditionsJSON, err := json.Marshal(request.TgCgConditions)
		if err != nil {
			return fmt.Errorf("failed to marshal tg_cg_conditions: %w", err)
		}

		if err := json.Unmarshal(conditionsJSON, &entityCondition); err != nil {
			return fmt.Errorf("failed to unmarshal tg_cg_conditions: %w", err)
		}

		conditionsEntity := &entity.TgCgDestinationSync{
			DestinationSyncID: *destinationSyncId,
			Conditions:        entityCondition,
		}

		conditionsQuery := `
			INSERT INTO tg_cg_destination_syncs (
				destination_sync_id,
				conditions,
				created_at,
				updated_at
			) VALUES (
				:destination_sync_id,
				:conditions,
				CURRENT_TIMESTAMP,
				CURRENT_TIMESTAMP
			) RETURNING id, created_at, updated_at`

		rows, err := tx.NamedQuery(conditionsQuery, conditionsEntity)
		if err != nil {
			return fmt.Errorf("failed to insert tg_cg_destination_syncs: %w", err)
		}

		if rows.Next() {
			err = rows.Scan(&conditionsEntity.Id, &conditionsEntity.CreatedAt, &conditionsEntity.UpdatedAt)
			if err != nil {
				return fmt.Errorf("failed to scan tg_cg_destination_sync result: %w", err)
			}
		}
		rows.Close()
	}

	scheduleSpec := dtos.ScheduleSpecRequest{
		CronExpressions: []string{request.CronExpression},
	}

	if request.EndDate != nil && *request.EndDate != "" {
		endTime, err := utils.ParseISTDateToStartOfDay(*request.EndDate)
		if err != nil {
			return fmt.Errorf("failed to parse end date: %w", err)
		}
		scheduleSpec.EndAt = &endTime
	}

	scheduleInput := dtos.ScheduleInput{
		WorkflowType: enum.DestinationSyncWorkflow,
		ScheduleSpec: scheduleSpec,
		Args:         []interface{}{*destinationSyncId, request.SyncStrategy},
	}

	err = controller.TemporalController.CreateSchedule(ctx, scheduleInput)
	if err != nil {
		return fmt.Errorf("failed to create temporal schedule: %w", err)
	}
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Now trigger the first sync immediately after data is committed
	triggerInput := dtos.TriggerWorkflowInput{
		WorkflowType: enum.DestinationSyncWorkflow,
		Args:         []interface{}{*destinationSyncId, request.SyncStrategy},
	}

	err = controller.TemporalController.TriggerWorkflow(ctx, triggerInput)
	if err != nil {
		// Log warning but don't fail since registration is already complete
		logger.Warn("Failed to trigger immediate first sync for destination sync, sync will start on next cron schedule",
			"destinationSyncId", *destinationSyncId, "destinationType", "APPSFLYER", "syncStrategy", request.SyncStrategy, "error", err)
	} else {
		logger.Info("Successfully triggered immediate first sync for destination sync",
			"destinationSyncId", *destinationSyncId, "destinationType", "APPSFLYER", "syncStrategy", request.SyncStrategy)
	}

	return nil
}
func (controller Controller) validateDestinationSyncEndDate(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	if request.EndDate == nil || *request.EndDate == "" {
		return fmt.Errorf("end_date is required")
	}
	minEndDate, err := controller.getSegmentAttributeEndDates(ctx, request.SegmentID)
	if err != nil {
		return fmt.Errorf("failed to get segment attribute end dates: %w", err)
	}
	userEndDate, err := utils.ParseISTDateToStartOfDay(*request.EndDate)
	if err != nil {
		return fmt.Errorf("failed to parse user end date: %w", err)
	}

	if userEndDate.After(*minEndDate) {
		return fmt.Errorf("destination sync end_date (%s) cannot be later than the earliest attribute end_date (%s) in the segment",
			userEndDate.Format("2006-01-02"), minEndDate.Format("2006-01-02"))
	}
	return nil
}

func (controller Controller) getSegmentAttributeEndDates(ctx context.Context, segmentID int64) (*time.Time, error) {
	query := `
		SELECT DISTINCT aq.id, aq.end_date
		FROM segments s
		JOIN segment_attribute_mapping sam ON s.id = sam.segment_id
		JOIN attributes a ON sam.attribute_id = a.id
		LEFT JOIN base_attribute_info bai ON a.id = bai.attribute_id
		LEFT JOIN attribute_queries aq ON bai.attribute_query_id = aq.id
		LEFT JOIN derived_attribute_mapping dam ON a.id = dam.derived_attribute_id
		LEFT JOIN attributes sa ON dam.source_attribute_id = sa.id
		LEFT JOIN base_attribute_info sbai ON sa.id = sbai.attribute_id
		LEFT JOIN attribute_queries saq ON sbai.attribute_query_id = saq.id
		WHERE s.id = $1 AND (aq.id IS NOT NULL OR saq.id IS NOT NULL)
	`

	type attributeEndDate struct {
		ID      *int64     `db:"id"`
		EndDate *time.Time `db:"end_date"`
	}

	var results []attributeEndDate
	err := controller.PostgresClient.SelectContext(ctx, &results, query, segmentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get segment attribute end dates: %w", err)
	}

	var endDates []*time.Time
	for _, result := range results {
		if result.EndDate != nil {
			endDates = append(endDates, result.EndDate)
		}
	}

	return utils.GetMinimumTime(endDates), nil
}

func (controller Controller) createDestinationSyncEntry(tx *sqlx.Tx, request *dtos.RegisterDestinationSyncRequest) (*int64, error) {
	query := `
        INSERT INTO destination_syncs (
            segment_id,
            destination_id,
            sync_strategy,
            cron_expression,
            owner_id,
            end_date,
            created_at,
            updated_at
        ) VALUES (
            :segment_id,
            :destination_id,
            :sync_strategy,
            :cron_expression,
            :owner_id,
            :end_date,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        ) RETURNING id, created_at, updated_at`

	destinationSync := &entity.DestinationSync{
		SegmentID:      request.SegmentID,
		DestinationID:  request.DestinationID,
		SyncStrategy:   request.SyncStrategy,
		CronExpression: request.CronExpression,
		OwnerID:        request.OwnerID,
		EndDate:        request.EndDate,
	}

	rows, err := tx.NamedQuery(query, destinationSync)
	if err != nil {
		return nil, fmt.Errorf("failed to insert destination sync: %w", err)
	}

	if !rows.Next() {
		return nil, errors.New("no id returned from destination sync insert")
	}

	err = rows.Scan(&destinationSync.Id, &destinationSync.CreatedAt, &destinationSync.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to scan destination sync result: %w", err)
	}
	rows.Close()
	return &destinationSync.Id, nil
}

func (controller Controller) RegisterDestinationSync(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	switch request.DestinationType {
	case "APPSFLYER":
		return controller.RegisterDestinationSyncAppsflyer(ctx, request)
	case "FEATURE_STORE":
		return controller.RegisterDestinationSyncFeatureStore(ctx, request)
	default:
		return fmt.Errorf("unsupported destination type: %s", request.DestinationType)
	}
}

func (controller Controller) validateAppsflyerRequest(ctx context.Context, request *dtos.RegisterDestinationSyncRequest) error {
	if request.AppsflyerAudience == nil {
		return errors.New("appsflyer_audience configuration is required for Appsflyer destination type")
	}

	if request.AppsflyerAudience.Platform == "" {
		return errors.New("platform is required for Appsflyer audience")
	}

	if request.AppsflyerAudience.Platform != "android" && request.AppsflyerAudience.Platform != "ios" {
		return errors.New("platform must be either 'android' or 'ios'")
	}

	return nil
}

func (controller Controller) GetSegmentQueryByDestinationSyncId(ctx context.Context, destinationSyncId int64) (string, error) {
	var conditions dtos.Condition
	var destinationType string
	var tgCgConditions *dtos.Condition

	query := `
        SELECT s.conditions, d.destination_type, tg.conditions
        FROM segments s
        JOIN destination_syncs ds ON s.id = ds.segment_id
        JOIN destinations d ON ds.destination_id = d.id
        LEFT JOIN tg_cg_destination_syncs tg ON ds.id = tg.destination_sync_id
        WHERE ds.id = $1
    `

	row := controller.PostgresClient.QueryRowContext(ctx, query, destinationSyncId)
	err := row.Scan(&conditions, &destinationType, &tgCgConditions)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil
		}
		return "", err
	}

	outputColumns := []string{}
	switch destinationType {
	case "APPSFLYER":
		outputColumns = append(outputColumns, appsflyer.SelectColumns()...)
	case "FEATURE_STORE":
		outputColumns = []string{}
	default:
		return "", fmt.Errorf("unsupported destination type: %s", destinationType)
	}

	segmentQuery, err := controller.SegmentController.GetQueryWithConditions(ctx, &conditions, tgCgConditions, outputColumns)
	if err != nil {
		return "", fmt.Errorf("error getting segment query: %w", err)
	}
	return segmentQuery, nil
}

func (controller Controller) GetIncrementalQueryByDestinationSyncId(ctx context.Context, destinationSyncId int64, currentSegmentTable, previousCommittedSegmentTable string) (string, error) {

	var destinationType string
	query := `
		SELECT d.destination_type
		FROM destination_syncs ds
		JOIN destinations d ON ds.destination_id = d.id
		WHERE ds.id = $1
	`

	row := controller.PostgresClient.QueryRowContext(ctx, query, destinationSyncId)
	err := row.Scan(&destinationType)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil
		}
		return "", err
	}

	switch destinationType {
	case "APPSFLYER":
		incrementalQuery, _ := controller.AppsflyerController.GetAppsflyerIncrementalQuery(currentSegmentTable, previousCommittedSegmentTable)
		return incrementalQuery, nil
	default:
		return "", fmt.Errorf("unsupported destination type: %s", destinationType)
	}
}

func (controller Controller) GetAppsflyerAudienceDetails(ctx context.Context, destinationSyncId int64) (*dtos.AppsflyerAudienceDetails, error) {
	query := `
		SELECT aa.import_key, aa.platform
		FROM destination_syncs ds
		JOIN appsflyer_sync_mappings asm ON ds.id = asm.destination_sync_id
		JOIN appsflyer_audiences aa ON asm.audience_id = aa.id
		WHERE ds.id = $1
	`

	details := &dtos.AppsflyerAudienceDetails{}
	err := controller.PostgresClient.QueryRowContext(ctx, query, destinationSyncId).Scan(&details.ImportKey, &details.Platform)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("error getting appsflyer audience details: %w", err)
	}

	return details, nil
}

func (controller Controller) UpdateDestinationSync(ctx context.Context, destinationSync *dtos.DestinationSync) error {
	query := "UPDATE destination_syncs SET "
	var updates []string
	args := map[string]interface{}{"id": destinationSync.Id}

	if destinationSync.SegmentID != 0 {
		updates = append(updates, "segment_id = :segment_id")
		args["segment_id"] = destinationSync.SegmentID
	}
	if destinationSync.DestinationID != 0 {
		updates = append(updates, "destination_id = :destination_id")
		args["destination_id"] = destinationSync.DestinationID
	}
	if destinationSync.CommittedSegmentTable != "" {
		updates = append(updates, "commited_segment_table_name = :commited_segment_table_name")
		args["commited_segment_table_name"] = destinationSync.CommittedSegmentTable
	}
	if destinationSync.OngoingSegmentTable != "" {
		updates = append(updates, "ongoing_segment_table_name = :ongoing_segment_table_name")
		args["ongoing_segment_table_name"] = destinationSync.OngoingSegmentTable
	}
	if destinationSync.SyncStrategy != "" {
		updates = append(updates, "sync_strategy = :sync_strategy")
		args["sync_strategy"] = destinationSync.SyncStrategy
	}
	if destinationSync.CronExpression != "" {
		updates = append(updates, "cron_expression = :cron_expression")
		args["cron_expression"] = destinationSync.CronExpression
	}
	if destinationSync.OwnerID != 0 {
		updates = append(updates, "owner_id = :owner_id")
		args["owner_id"] = destinationSync.OwnerID
	}
	if destinationSync.EndDate != nil {
		updates = append(updates, "end_date = :end_date")
		args["end_date"] = destinationSync.EndDate
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	updates = append(updates, "updated_at = CURRENT_TIMESTAMP")
	query += strings.Join(updates, ", ") + " WHERE id = :id"

	_, err := controller.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (controller Controller) GetCommittedSegmentTableName(ctx context.Context, destinationSyncID int64) (string, error) {
	query := `
        SELECT commited_segment_table_name
        FROM destination_syncs
        WHERE id = $1
    `

	var committedSegmentTableName string
	err := controller.PostgresClient.QueryRowContext(ctx, query, destinationSyncID).Scan(&committedSegmentTableName)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("no record found for destination_sync_id: %d", destinationSyncID)
		}
		return "", fmt.Errorf("error fetching committed segment table name: %w", err)
	}

	return committedSegmentTableName, nil
}

func (c Controller) ValidateDestinationSyncSegment(ctx context.Context, request *dtos.ValidateDestinationSyncRequest) (*dtos.ValidateDestinationSyncResponse, error) {
	switch request.DestinationType {
	case "APPSFLYER":
		attributes, err := c.SegmentController.GetUpstreamAttributeInfo(ctx, request.SegmentID)
		if err != nil {
			return nil, fmt.Errorf("error fetching segment attributes: %w", err)
		}

		hasPlatform := false
		for _, attr := range attributes {

			if attr.Name == "Recent Transaction Device Platform" {
				hasPlatform = true
				break
			}
		}

		if !hasPlatform {
			return &dtos.ValidateDestinationSyncResponse{
				IsValid: false,
				Message: "segment must include 'platform' attribute for Appsflyer destination",
			}, nil
		}

		return &dtos.ValidateDestinationSyncResponse{
			IsValid: true,
			Message: "validation successful",
		}, nil

	default:
		return &dtos.ValidateDestinationSyncResponse{
			IsValid: false,
			Message: fmt.Sprintf("unsupported destination type: %s", request.DestinationType),
		}, nil
	}
}

func (controller Controller) ValidateDestinationSyncAppsflyerAudienceName(ctx context.Context, request *dtos.ValidateAppsflyerAudienceNameRequest) (*dtos.ValidateAudienceResponse, error) {

	activeAudiences, err := controller.AppsflyerClient.GetActiveAudiences(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active audiences: %w", err)
	}

	normalizedRequestName := strings.ToLower(strings.TrimSpace(request.Name))
	for _, audience := range activeAudiences.Message.Results {
		if strings.ToLower(strings.TrimSpace(audience.Name)) == normalizedRequestName {
			return &dtos.ValidateAudienceResponse{
				IsValid: false,
				Message: fmt.Sprintf("audience name '%s' already exists", request.Name),
			}, nil
		}
	}

	return &dtos.ValidateAudienceResponse{
		IsValid: true,
		Message: "audience name is available",
	}, nil
}

func (controller Controller) ValidateDestinationSyncFeatureStoreSegment(ctx context.Context, request *dtos.ValidateFeatureStoreSegmentRequest) (*dtos.ValidateAudienceResponse, error) {
	normalizedRequestName := strings.ToLower(strings.TrimSpace(request.FeatureName))

	featureExists, err := controller.FeatureStoreController.ValidateFeatureExists(ctx, normalizedRequestName, request.DestinationID)
	if err != nil {
		return nil, fmt.Errorf("failed to validate feature existence: %w", err)
	}

	if featureExists {
		return &dtos.ValidateAudienceResponse{
			IsValid: false,
			Message: fmt.Sprintf("feature/segment '%s' already exists", request.FeatureName),
		}, nil
	}

	return &dtos.ValidateAudienceResponse{
		IsValid: true,
		Message: "feature/segment is available",
	}, nil
}

func (controller Controller) CleanOngoingSegmentTable(ctx context.Context, destinationSyncID int64) error {
	query := `
        UPDATE destination_syncs
        SET ongoing_segment_table_name = ''
        WHERE id = $1
    `

	result, err := controller.PostgresClient.ExecContext(ctx, query, destinationSyncID)
	if err != nil {
		return fmt.Errorf("failed to clean ongoing_segment_table_name: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("error checking rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no destination sync found with id: %d", destinationSyncID)
	}

	return nil
}

func (controller Controller) UpdateDestinationSyncCron(ctx context.Context, destinationSyncUpdateCronRequestDTO *dtos.DestinationSyncUpdateCronRequest) error {

	// Get current cron expression for potential rollback
	var currentCronExpression string
	var syncStrategy string
	err := controller.PostgresClient.GetContext(ctx, &currentCronExpression,
		"SELECT cron_expression FROM destination_syncs WHERE id = $1",
		destinationSyncUpdateCronRequestDTO.Id)
	if err != nil {
		return fmt.Errorf("failed to get current cron expression: %w", err)
	}

	// Only get sync strategy for AppsFlyer workflows
	if destinationSyncUpdateCronRequestDTO.DestinationType == "APPSFLYER" {
		err = controller.PostgresClient.GetContext(ctx, &syncStrategy,
			"SELECT sync_strategy FROM destination_syncs WHERE id = $1",
			destinationSyncUpdateCronRequestDTO.Id)
		if err != nil {
			return fmt.Errorf("failed to get sync strategy: %w", err)
		}
	}

	query := "UPDATE destination_syncs SET "
	var updates []string
	args := map[string]interface{}{"id": destinationSyncUpdateCronRequestDTO.Id}

	if destinationSyncUpdateCronRequestDTO.CronExpression != "" {
		var scheduleInput dtos.ScheduleInput

		// Set workflow type and args based on destination type
		switch destinationSyncUpdateCronRequestDTO.DestinationType {
		case "APPSFLYER":
			scheduleInput = dtos.ScheduleInput{
				WorkflowType: enum.DestinationSyncWorkflow,
				ScheduleSpec: dtos.ScheduleSpecRequest{
					CronExpressions: []string{destinationSyncUpdateCronRequestDTO.CronExpression},
				},
				Args: []interface{}{destinationSyncUpdateCronRequestDTO.Id, syncStrategy},
			}
		case "FEATURE_STORE":
			scheduleInput = dtos.ScheduleInput{
				WorkflowType: enum.DestinationSyncWorkflowFeatureStore,
				ScheduleSpec: dtos.ScheduleSpecRequest{
					CronExpressions: []string{destinationSyncUpdateCronRequestDTO.CronExpression},
				},
				Args: []interface{}{destinationSyncUpdateCronRequestDTO.Id},
			}
		default:
			return fmt.Errorf("unsupported destination type: %s", destinationSyncUpdateCronRequestDTO.DestinationType)
		}

		// Update temporal schedule
		err := controller.TemporalController.UpdateCron(ctx, scheduleInput, []string{destinationSyncUpdateCronRequestDTO.CronExpression})
		if err != nil {
			return fmt.Errorf("failed to update cron: %w", err)
		}

		// Update database
		updates = append(updates, "cron_expression = :cron_expression")
		args["cron_expression"] = destinationSyncUpdateCronRequestDTO.CronExpression

		if len(updates) > 0 {
			query += strings.Join(updates, ", ") + ", updated_at = NOW() WHERE id = :id"
			_, err = controller.PostgresClient.NamedExecContext(ctx, query, args)
			if err != nil {
				// Rollback temporal schedule to previous value
				rollbackScheduleInput := scheduleInput
				rollbackScheduleInput.ScheduleSpec.CronExpressions = []string{currentCronExpression}

				rollbackErr := controller.TemporalController.UpdateCron(ctx, rollbackScheduleInput, []string{currentCronExpression})
				if rollbackErr != nil {
					return fmt.Errorf("failed to update database and failed to rollback temporal schedule: db_error=%w, rollback_error=%v", err, rollbackErr)
				}
				return fmt.Errorf("failed to update database, temporal schedule rolled back: %w", err)
			}
		}
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	return nil
}

// GetFeatureStoreSegmentInfo retrieves all committed segment tables associated with feature store destinations
// along with their related feature information
func (controller Controller) GetFeatureStoreSegmentInfo(ctx context.Context, status string) ([]dtos.FeatureStoreSegmentInfo, error) {
	query := `
        SELECT 
            ds.id AS destination_sync_id,
            ds.commited_segment_table_name,
            f.tenant,
            f.namespace,
            f.context_type,
            f.context_name,
            f.entity,
            f.feature_name,
            f.feature_type,
            s.name AS segment_name,
            last_run.id AS last_destination_sync_run_id
        FROM destination_syncs ds
        JOIN destinations d ON ds.destination_id = d.id
        JOIN feature_store_sync_mapping fsm ON ds.id = fsm.destination_sync_id
        JOIN feature_store_segments f ON fsm.feature_id = f.id
        JOIN segments s ON ds.segment_id = s.id
        LEFT JOIN LATERAL (
            SELECT 
                id
            FROM destination_sync_runs
            WHERE 
                destination_sync_id = ds.id 
                AND status = $1
            ORDER BY execution_start DESC
            LIMIT 1
        ) last_run ON true
        WHERE 
            d.destination_type = 'FEATURE_STORE' 
            AND ds.commited_segment_table_name IS NOT NULL
            AND ds.commited_segment_table_name != ''
            AND f.is_active = true
            AND (f.expiry IS NULL OR f.expiry = '' OR f.expiry::timestamp > (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'))
        ORDER BY f.context_name, f.entity, f.feature_name
    `

	var featureStoreSegmentInfo []dtos.FeatureStoreSegmentInfo
	err := controller.PostgresClient.SelectContext(ctx, &featureStoreSegmentInfo, query, status)
	if err != nil {
		logger.Info("Error fetching feature store segment info from postgres", err)
		return nil, err
	}
	return featureStoreSegmentInfo, nil
}

func (controller Controller) UpdateDestinationSyncTgCg(ctx context.Context, request *dtos.DestinationSyncUpdateTgCgRequest) error {
	query := `
		UPDATE tg_cg_destination_syncs
		SET conditions = :conditions,
			updated_at = CURRENT_TIMESTAMP
		WHERE destination_sync_id = :destination_sync_id
	`
	conditionsJSON, err := json.Marshal(request.Conditions)
	if err != nil {
		return fmt.Errorf("error marshaling conditions: %w", err)
	}
	params := map[string]interface{}{
		"destination_sync_id": request.Id,
		"conditions":          conditionsJSON,
	}

	_, queryErr := controller.PostgresClient.NamedExecContext(ctx, query, params)
	if queryErr != nil {
		return fmt.Errorf("failed to update TGCG conditions: %w", queryErr)
	}

	return nil
}

func (controller Controller) ValidateDestinationSyncSize(ctx context.Context, req *dtos.ValidateDestinationSyncSizeRequest) (*dtos.ValidateDestinationSyncResponse, error) {
	var count int64

	if req.TgCgConditions == nil {
		// Call the regular segment size API when TgCgConditions is null
		sizeResp, err := controller.SegmentController.GetSegmentSizeById(ctx, req.SegmentID)
		if err != nil {
			return &dtos.ValidateDestinationSyncResponse{
				IsValid: false,
				Message: "Failed to get segment size: " + err.Error(),
			}, nil
		}
		count = sizeResp.Count
	} else {
		// Call the TgCg API when TgCgConditions is provided
		sizeReq := &dtos.SegmentSizeTgCgRequest{
			Base:           dtos.Base{Id: req.SegmentID},
			TgCgConditions: req.TgCgConditions,
		}

		sizeResp, err := controller.SegmentController.GetSegmentSizeTgCg(ctx, sizeReq)
		if err != nil {
			return &dtos.ValidateDestinationSyncResponse{
				IsValid: false,
				Message: "Failed to get segment size: " + err.Error(),
			}, nil
		}
		count = sizeResp.FilteredSize
	}

	// Determine the maximum allowed size based on destination type
	var maxSize int64
	switch req.DestinationType {
	case "APPSFLYER":
		maxSize = MAX_DESTINATION_SYNC_SIZE_APPSFLYER
	case "FEATURE_STORE":
		maxSize = MAX_DESTINATION_SYNC_SIZE_FEATURE_STORE
	default:
		maxSize = MAX_DESTINATION_SYNC_SIZE_FEATURE_STORE // Default to feature store limit
	}

	if count > maxSize {
		return &dtos.ValidateDestinationSyncResponse{
			IsValid: false,
			Message: fmt.Sprintf("Destination sync size (%d) exceeds allowed maximum (%d) for %s", count, maxSize, req.DestinationType),
		}, nil
	}

	return &dtos.ValidateDestinationSyncResponse{
		IsValid: true,
		Message: fmt.Sprintf("Destination sync size valid (%d <= %d) for %s", count, maxSize, req.DestinationType),
	}, nil
}

func (controller *Controller) UpdateDestinationSyncEndDate(ctx context.Context, req *dtos.DestinationSyncUpdateEndDateRequest) error {
	// Get current values for potential rollback
	var currentEndDate *string
	var syncStrategy string
	var cronExpression string

	err := controller.PostgresClient.GetContext(ctx, &currentEndDate,
		"SELECT end_date FROM destination_syncs WHERE id = $1", req.Id)
	if err != nil {
		return fmt.Errorf("failed to get current end date: %w", err)
	}

	err = controller.PostgresClient.GetContext(ctx, &cronExpression,
		"SELECT cron_expression FROM destination_syncs WHERE id = $1", req.Id)
	if err != nil {
		return fmt.Errorf("failed to get cron expression: %w", err)
	}

	// Only get sync strategy for AppsFlyer workflows
	if req.DestinationType == "APPSFLYER" {
		err = controller.PostgresClient.GetContext(ctx, &syncStrategy,
			"SELECT sync_strategy FROM destination_syncs WHERE id = $1", req.Id)
		if err != nil {
			return fmt.Errorf("failed to get sync strategy: %w", err)
		}
	}

	// Prepare temporal schedule input first
	var scheduleInput dtos.ScheduleInput
	scheduleSpec := dtos.ScheduleSpecRequest{
		CronExpressions: []string{cronExpression},
	}

	// Parse and set end date for temporal schedule
	endTime, err := utils.ParseISTDateToStartOfDay(req.EndDate)
	if err != nil {
		return fmt.Errorf("failed to parse end date: %w", err)
	}
	scheduleSpec.EndAt = &endTime

	// Set workflow type and args based on destination type
	switch req.DestinationType {
	case "APPSFLYER":
		scheduleInput = dtos.ScheduleInput{
			WorkflowType: enum.DestinationSyncWorkflow,
			ScheduleSpec: scheduleSpec,
			Args:         []interface{}{req.Id, syncStrategy},
		}
	case "FEATURE_STORE":
		scheduleInput = dtos.ScheduleInput{
			WorkflowType: enum.DestinationSyncWorkflowFeatureStore,
			ScheduleSpec: scheduleSpec,
			Args:         []interface{}{req.Id},
		}
	default:
		return fmt.Errorf("unsupported destination type: %s", req.DestinationType)
	}

	// Update temporal schedule first
	cronExpressions := []string{cronExpression}
	err = controller.TemporalController.UpdateScheduleEndDate(ctx, scheduleInput, cronExpressions)
	if err != nil {
		return fmt.Errorf("failed to update temporal schedule end date: %w", err)
	}

	// Begin transaction for database updates
	tx, err := controller.PostgresClient.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Update destination_syncs table
	updateSyncQuery := `UPDATE destination_syncs SET end_date = $1, updated_at = NOW() WHERE id = $2`
	if _, err := tx.ExecContext(ctx, updateSyncQuery, req.EndDate, req.Id); err != nil {
		return fmt.Errorf("error updating destination_syncs end_date: %w", err)
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		// Rollback temporal schedule to previous value
		rollbackScheduleInput := scheduleInput
		if currentEndDate != nil && *currentEndDate != "" {
			rollbackEndTime, parseErr := utils.ParseISTDateToStartOfDay(*currentEndDate)
			if parseErr == nil {
				rollbackScheduleInput.ScheduleSpec.EndAt = &rollbackEndTime
			}
		} else {
			rollbackScheduleInput.ScheduleSpec.EndAt = nil
		}
		cronExpressions := []string{cronExpression}
		rollbackErr := controller.TemporalController.UpdateScheduleEndDate(ctx, rollbackScheduleInput, cronExpressions)
		if rollbackErr != nil {
			return fmt.Errorf("failed to commit transaction and failed to rollback temporal schedule: db_error=%w, rollback_error=%v", err, rollbackErr)
		}
		return fmt.Errorf("failed to commit transaction, temporal schedule rolled back: %w", err)
	}

	// Call downstream Feature Store API if required (after transaction commit)
	if req.DestinationType == "FEATURE_STORE" && controller.FeatureStoreController != nil {
		// Get feature details for the API call
		getFeatureDetailsQ := `
        SELECT fss.context_name, fss.feature_name, ds.destination_id
        FROM feature_store_sync_mapping fsm
        JOIN feature_store_segments fss ON fsm.feature_id = fss.id
        JOIN destination_syncs ds ON fsm.destination_sync_id = ds.id
        WHERE fsm.destination_sync_id = $1`

		var featureDetails struct {
			ContextName   string `db:"context_name"`
			FeatureName   string `db:"feature_name"`
			DestinationID int64  `db:"destination_id"`
		}

		err := controller.PostgresClient.GetContext(ctx, &featureDetails, getFeatureDetailsQ, req.Id)
		if err != nil {
			logger.Warn("Failed to get feature details for API call after successful DB update",
				"destinationSyncId", req.Id, "error", err)
		} else {
			updateRequest := &dtos.UpdateFeatureRequest{
				Expiry:          &req.EndDate,
				UpdatedByUserID: &req.OwnerID,
			}

			_, err := controller.FeatureStoreController.UpdateFeature(ctx, featureDetails.ContextName, featureDetails.FeatureName, updateRequest, featureDetails.DestinationID)
			if err != nil {
				logger.Warn("Failed to update feature store via API after successful DB update",
					"destinationSyncId", req.Id, "contextName", featureDetails.ContextName,
					"featureName", featureDetails.FeatureName, "error", err)
			}
		}
	}

	return nil
}

func (controller Controller) GetDestinationSyncOwnerID(ctx context.Context, id int64) (int64, error) {
	var ownerID int64
	query := `SELECT owner_id FROM destination_syncs WHERE id = $1`
	err := controller.PostgresClient.GetContext(ctx, &ownerID, query, id)
	if err != nil {
		return 0, fmt.Errorf("failed to get destination sync owner ID: %w", err)
	}
	return ownerID, nil
}
