package temporalcontroller

import (
	"context"
	"fmt"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	"github.com/Zomato/cdp-platform/internal/enum"
	"github.com/Zomato/go/logger"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/sdk/client"
)

const (
	defaultTimezone = "Asia/Kolkata" // IST
)

type Controller struct {
	*basecontroller.Controller
	Workflows map[enum.WorkflowType]interface{}
}

func (controller *Controller) convertScheduleRange(ranges []dtos.ScheduleRangeRequest) []client.ScheduleRange {
	if len(ranges) == 0 {
		return nil
	}

	result := make([]client.ScheduleRange, len(ranges))
	for i, r := range ranges {
		result[i] = client.ScheduleRange{
			Start: r.Start,
			End:   r.End,
			Step:  r.Step,
		}
	}
	return result
}

func (controller *Controller) convertToTemporalSpec(req dtos.ScheduleSpecRequest) client.ScheduleSpec {
	spec := client.ScheduleSpec{}

	for _, interval := range req.Intervals {
		spec.Intervals = append(spec.Intervals, client.ScheduleIntervalSpec{
			Every:  time.Duration(interval.Every) * time.Minute,
			Offset: time.Duration(interval.Offset) * time.Minute,
		})
	}

	for _, cal := range req.Calendars {
		spec.Calendars = append(spec.Calendars, client.ScheduleCalendarSpec{
			Second:     controller.convertScheduleRange(cal.Second),
			Minute:     controller.convertScheduleRange(cal.Minute),
			Hour:       controller.convertScheduleRange(cal.Hour),
			DayOfMonth: controller.convertScheduleRange(cal.DayOfMonth),
			Month:      controller.convertScheduleRange(cal.Month),
			Year:       controller.convertScheduleRange(cal.Year),
			DayOfWeek:  controller.convertScheduleRange(cal.DayOfWeek),
			Comment:    cal.Comment,
		})
	}

	if req.Timezone == "" {
		spec.TimeZoneName = defaultTimezone
	} else {
		spec.TimeZoneName = req.Timezone
	}

	if req.Jitter != 0 {
		spec.Jitter = time.Duration(req.Jitter) * time.Minute
	}

	if req.EndAt != nil {
		spec.EndAt = *req.EndAt
	}

	if len(req.CronExpressions) > 0 {
		spec.CronExpressions = req.CronExpressions
	}

	return spec
}

func (controller *Controller) getScheduleIDFromInput(req dtos.ScheduleInput) (string, error) {
	var identifier string
	if len(req.Args) > 0 {
		identifier = fmt.Sprintf("%v", req.Args[0])
	} else {
		// For workflows that don't require arguments, use a default identifier
		identifier = "default"
	}

	scheduleID := fmt.Sprintf("Schedule-%s-%s", req.WorkflowType, identifier)
	return scheduleID, nil
}

func (controller *Controller) getWorkflowIDFromInput(workflowType enum.WorkflowType, args []interface{}) (string, error) {
	var identifier string
	if len(args) > 0 {
		identifier = fmt.Sprintf("%v", args[0])
	} else {
		// For workflows that don't require arguments, use a default identifier
		identifier = "default"
	}

	workflowID := fmt.Sprintf("Workflow-%s-%s", workflowType, identifier)
	return workflowID, nil
}

func (controller *Controller) TriggerWorkflow(ctx context.Context, req dtos.TriggerWorkflowInput) error {
	if req.WorkflowType == "" {
		logger.Error("Workflow type is required")
		return fmt.Errorf("workflow type is required")
	}

	workflowID, err := controller.getWorkflowIDFromInput(req.WorkflowType, req.Args)
	if err != nil {
		logger.Error("Failed to generate workflow ID", "error", err)
		return fmt.Errorf("failed to generate workflow ID: %w", err)
	}

	workflowImpl, exists := controller.Workflows[req.WorkflowType]
	if !exists {
		logger.Error("Workflow type not registered", "workflowType", req.WorkflowType)
		return fmt.Errorf("workflow type %s not registered", req.WorkflowType)
	}

	describeCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	desc, err := controller.TemporalClient.Client.DescribeWorkflowExecution(
		describeCtx,
		workflowID,
		"", // Empty RunID gets the current execution
	)
	if err == nil {
		status := desc.WorkflowExecutionInfo.Status

		if status == enums.WORKFLOW_EXECUTION_STATUS_RUNNING {
			logger.Info("Workflow is already running, skipping execution", "workflowID", workflowID)
			return nil // Return success to avoid reprocessing the message
		}

		// If workflow completed successfully, also return success
		if status == enums.WORKFLOW_EXECUTION_STATUS_COMPLETED {
			logger.Info("Workflow already completed successfully", "workflowID", workflowID)
			return nil
		}

		// For other statuses (FAILED, CANCELED, TERMINATED, TIMED_OUT), we'll restart the workflow
		logger.Info("Existing workflow in non-running state, restarting workflow",
			"workflowID", workflowID,
			"status", status.String())
	} else {
		// Check if it's a "not found" error, which is expected for new workflows
		if _, ok := err.(*serviceerror.NotFound); !ok {
			logger.Warn("Error checking workflow execution", "workflowID", workflowID, "error", err)
		} else {
			logger.Info("No existing workflow found, proceeding with execution", "workflowID", workflowID)
		}
	}

	options := client.StartWorkflowOptions{
		ID:                    workflowID,
		TaskQueue:             controller.TemporalTaskQueue,
		WorkflowIDReusePolicy: enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
	}

	// Execute the workflow with proper context and error handling
	execCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	run, err := controller.TemporalClient.Client.ExecuteWorkflow(execCtx, options, workflowImpl, req.Args...)
	if err != nil {
		logger.Error("Workflow execution failed", "workflowID", workflowID, "error", err)

		// Handle specific temporal errors
		switch err.(type) {
		case *serviceerror.WorkflowExecutionAlreadyStarted:
			// Workflow is already running, which is fine for our idempotency requirements
			logger.Info("Workflow already started (confirmed by Temporal)")
			return nil
		case *serviceerror.NamespaceNotFound:
			logger.Error("Temporal namespace not found", "error", err)
			return fmt.Errorf("temporal namespace not found: %w", err)
		case *serviceerror.InvalidArgument:
			logger.Error("Invalid arguments for workflow", "error", err)
			return fmt.Errorf("invalid workflow arguments: %w", err)
		default:
			logger.Error("Failed to execute workflow", "error", err)
			return fmt.Errorf("failed to execute workflow: %w", err)
		}
	}

	logger.Info("Successfully triggered workflow", "workflowID", workflowID, "runID", run.GetRunID())

	return nil
}

// Creates a Schedule
func (controller *Controller) CreateSchedule(ctx context.Context, req dtos.ScheduleInput) error {
	if err := controller.validateCreateRequest(req); err != nil {
		return fmt.Errorf("invalid request: %w", err)
	}

	scheduleID, err := controller.getScheduleIDFromInput(req)
	if err != nil {
		return err
	}

	workflowID, err := controller.getWorkflowIDFromInput(req.WorkflowType, req.Args)
	if err != nil {
		return err
	}

	workflowImpl, exists := controller.Workflows[req.WorkflowType]
	if !exists {
		return fmt.Errorf("workflow type %s not registered", req.WorkflowType)
	}

	scheduleSpec := controller.convertToTemporalSpec(req.ScheduleSpec)

	_, err = controller.TemporalClient.Client.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   scheduleID,
		Spec: scheduleSpec,
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowID,
			Workflow:  workflowImpl,
			TaskQueue: controller.TemporalTaskQueue,
			Args:      req.Args,
		},
		Overlap:        enums.SCHEDULE_OVERLAP_POLICY_BUFFER_ALL,
		CatchupWindow:  24 * time.Hour,
		PauseOnFailure: false,
	})

	if err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}

	return nil
}

func (controller *Controller) validateCreateRequest(req dtos.ScheduleInput) error {
	if req.WorkflowType == "" {
		return fmt.Errorf("workflow type is required")
	}

	if len(req.ScheduleSpec.Intervals) == 0 && len(req.ScheduleSpec.Calendars) == 0 && len(req.ScheduleSpec.CronExpressions) == 0 {
		return fmt.Errorf("at least one interval, calendar or cron spec is required")
	}

	return nil
}

// Helper function to get a schedule handle by ID
func (controller *Controller) getScheduleHandle(ctx context.Context, scheduleID string) (client.ScheduleHandle, error) {
	scheduleHandle := controller.TemporalClient.Client.ScheduleClient().GetHandle(ctx, scheduleID)
	if scheduleHandle == nil {
		return nil, fmt.Errorf("schedule not found: %s", scheduleID)
	}
	return scheduleHandle, nil
}

func (controller *Controller) getScheduleHandleFromReq(ctx context.Context, req dtos.ScheduleInput) (client.ScheduleHandle, error) {
	scheduleID, err := controller.getScheduleIDFromInput(req)
	if err != nil {
		return nil, err
	}
	logger.Info("scheduleID: ", scheduleID)
	scheduleHandle, err := controller.getScheduleHandle(ctx, scheduleID)
	if err != nil {
		return nil, err
	}
	return scheduleHandle, nil
}

// Returns the next N scheduled times for a given schedule
func (controller *Controller) GetSchedulePreview(ctx context.Context, req dtos.ScheduleInput, previewCount int) ([]time.Time, error) {
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return nil, err
	}

	desc, err := scheduleHandle.Describe(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to describe schedule: %w", err)
	}

	nextTimes := desc.Info.NextActionTimes

	if previewCount < len(nextTimes) {
		return nextTimes[:previewCount], nil
	}

	return nextTimes, nil
}

// Pauses a schedule with an optional note
func (controller *Controller) PauseSchedule(ctx context.Context, req dtos.ScheduleInput, note string) error {
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return err
	}

	err = scheduleHandle.Pause(ctx, client.SchedulePauseOptions{
		Note: note,
	})
	if err != nil {
		return fmt.Errorf("failed to pause schedule: %w", err)
	}

	return nil
}

// Unpauses a schedule with an optional note
func (controller *Controller) UnpauseSchedule(ctx context.Context, req dtos.ScheduleInput, note string) error {
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return err
	}

	err = scheduleHandle.Unpause(ctx, client.ScheduleUnpauseOptions{
		Note: note,
	})
	if err != nil {
		return fmt.Errorf("failed to unpause schedule: %w", err)
	}

	return nil
}

// TriggerSchedule immediately executes a scheduled workflow
func (controller *Controller) TriggerSchedule(ctx context.Context, req dtos.ScheduleInput) error {
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return err
	}

	overlap := enums.SCHEDULE_OVERLAP_POLICY_BUFFER_ALL

	err = scheduleHandle.Trigger(ctx, client.ScheduleTriggerOptions{
		Overlap: overlap,
	})
	if err != nil {
		return fmt.Errorf("failed to trigger schedule: %w", err)
	}

	return nil
}

// Removes a schedule
func (controller *Controller) DeleteSchedule(ctx context.Context, req dtos.ScheduleInput) error {
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return err
	}

	err = scheduleHandle.Delete(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}

	return nil
}

// Returns all available schedules
func (controller *Controller) ListSchedules(ctx context.Context, pageSize int) ([]*client.ScheduleListEntry, error) {
	listView, err := controller.TemporalClient.Client.ScheduleClient().List(ctx, client.ScheduleListOptions{
		PageSize: pageSize,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list schedules: %w", err)
	}

	var schedules []*client.ScheduleListEntry
	for listView.HasNext() {
		schedule, err := listView.Next()
		if err != nil {
			return nil, fmt.Errorf("failed to get next schedule: %w", err)
		}
		schedules = append(schedules, schedule)
	}

	return schedules, nil
}

// Updates an existing schedule's configuration
func (controller *Controller) UpdateSchedule(ctx context.Context, scheduleID string, updateFn func(input client.ScheduleUpdateInput) (*client.ScheduleUpdate, error)) error {
	scheduleHandle, err := controller.getScheduleHandle(ctx, scheduleID)
	if err != nil {
		return err
	}

	err = scheduleHandle.Update(ctx, client.ScheduleUpdateOptions{
		DoUpdate: updateFn,
	})
	if err != nil {
		return fmt.Errorf("failed to update schedule: %w", err)
	}

	return nil
}

// Updates both date range of a schedule
func (controller *Controller) UpdateScheduleStartEndDate(ctx context.Context, req dtos.ScheduleInput, startDate, endDate time.Time) error {
	scheduleID, err := controller.getScheduleIDFromInput(req)
	if err != nil {
		return fmt.Errorf("failed to get schedule ID: %w", err)
	}

	return controller.UpdateSchedule(ctx, scheduleID, func(input client.ScheduleUpdateInput) (*client.ScheduleUpdate, error) {
		schedule := input.Description.Schedule
		schedule.Spec.StartAt = startDate
		schedule.Spec.EndAt = endDate
		return &client.ScheduleUpdate{
			Schedule: &schedule,
		}, nil
	})
}

func (controller *Controller) UpdateCron(ctx context.Context, req dtos.ScheduleInput, cronExpressions []string) error {
	// First get the existing schedule to preserve its configuration
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return fmt.Errorf("schedule not found: %w", err)
	}

	// Get the current schedule description to preserve settings
	desc, err := scheduleHandle.Describe(ctx)
	if err != nil {
		return fmt.Errorf("failed to describe schedule: %w", err)
	}

	// Store the original configuration
	originalSpec := desc.Schedule.Spec

	// Create new schedule input with preserved configs but new cron
	newScheduleSpec := dtos.ScheduleSpecRequest{
		Intervals:       make([]dtos.IntervalSpecRequest, 0), // Empty as we're using cron
		Calendars:       make([]dtos.CalendarSpecRequest, 0), // Empty as we're using cron
		CronExpressions: cronExpressions,
		Timezone:        originalSpec.TimeZoneName,
		Jitter:          int(originalSpec.Jitter.Minutes()),
		EndAt:           &originalSpec.EndAt, // Preserve end date
	}

	newReq := dtos.ScheduleInput{
		WorkflowType: req.WorkflowType,
		Args:         req.Args,
		ScheduleSpec: newScheduleSpec,
	}

	// Delete the existing schedule
	err = controller.DeleteSchedule(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to delete existing schedule: %w", err)
	}

	time.Sleep(1 * time.Second)

	// Try to create the new schedule with updated cron
	err = controller.CreateSchedule(ctx, newReq)
	if err != nil {
		// Creation failed, attempt to restore the original schedule
		originalReq := dtos.ScheduleInput{
			WorkflowType: req.WorkflowType,
			Args:         req.Args,
			ScheduleSpec: dtos.ScheduleSpecRequest{
				Intervals:       make([]dtos.IntervalSpecRequest, 0),
				Calendars:       make([]dtos.CalendarSpecRequest, 0),
				CronExpressions: originalSpec.CronExpressions,
				Timezone:        originalSpec.TimeZoneName,
				Jitter:          int(originalSpec.Jitter.Minutes()),
				EndAt:           &originalSpec.EndAt, // Restore original end date
			},
		}

		restoreErr := controller.CreateSchedule(ctx, originalReq)
		if restoreErr != nil {
			return fmt.Errorf("failed to update cron and restore original schedule failed. Update error: %v, Restore error: %v", err, restoreErr)
		}
		return fmt.Errorf("failed to create schedule with new cron, original schedule restored: %w", err)
	}

	return nil
}

func (controller *Controller) UpdateScheduleEndDate(ctx context.Context, req dtos.ScheduleInput, cronExpressions []string) error {
	// First get the existing schedule to preserve its configuration
	scheduleHandle, err := controller.getScheduleHandleFromReq(ctx, req)
	if err != nil {
		return fmt.Errorf("schedule not found: %w", err)
	}

	// Get the current schedule description to preserve settings
	desc, err := scheduleHandle.Describe(ctx)
	if err != nil {
		return fmt.Errorf("failed to describe schedule: %w", err)
	}

	// Store the original configuration
	originalSpec := desc.Schedule.Spec

	// Create new schedule input with preserved configs but new end date
	newScheduleSpec := dtos.ScheduleSpecRequest{
		Intervals:       make([]dtos.IntervalSpecRequest, 0), // Empty as we're using cron
		Calendars:       make([]dtos.CalendarSpecRequest, 0), // Empty as we're using cron
		CronExpressions: cronExpressions,                     // !! IMP originalSpec.CronExpressions doesn't give cron so we have to pass it to the func
		Timezone:        originalSpec.TimeZoneName,
		Jitter:          int(originalSpec.Jitter.Minutes()),
		EndAt:           req.ScheduleSpec.EndAt, // Use the new end date from request
	}

	newReq := dtos.ScheduleInput{
		WorkflowType: req.WorkflowType,
		Args:         req.Args,
		ScheduleSpec: newScheduleSpec,
	}

	// Delete the existing schedule
	err = controller.DeleteSchedule(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to delete existing schedule: %w", err)
	}

	time.Sleep(1 * time.Second)

	// Try to create the new schedule with updated end date
	err = controller.CreateSchedule(ctx, newReq)
	if err != nil {
		// Creation failed, attempt to restore the original schedule
		originalReq := dtos.ScheduleInput{
			WorkflowType: req.WorkflowType,
			Args:         req.Args,
			ScheduleSpec: dtos.ScheduleSpecRequest{
				Intervals:       make([]dtos.IntervalSpecRequest, 0),
				Calendars:       make([]dtos.CalendarSpecRequest, 0),
				CronExpressions: cronExpressions,
				Timezone:        originalSpec.TimeZoneName,
				Jitter:          int(originalSpec.Jitter.Minutes()),
				EndAt:           &originalSpec.EndAt, // Restore original end date
			},
		}

		restoreErr := controller.CreateSchedule(ctx, originalReq)
		if restoreErr != nil {
			return fmt.Errorf("failed to update end date and restore original schedule failed. Update error: %v, Restore error: %v", err, restoreErr)
		}
		return fmt.Errorf("failed to create schedule with new end date, original schedule restored: %w", err)
	}

	return nil
}
