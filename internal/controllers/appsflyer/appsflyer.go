package appsflyercontroller

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/Zomato/cdp-platform/dtos"
	entity "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	kafkacontroller "github.com/Zomato/cdp-platform/internal/controllers/kafka"
	starrockscontroller "github.com/Zomato/cdp-platform/internal/controllers/starrocks"
	appsflyer "github.com/Zomato/cdp-platform/pkg/appsflyer"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	StarrocksController *starrockscontroller.Controller
	KafkaController     *kafkacontroller.Controller
	AppsflyerClient     *appsflyer.Client
}

func (c *Controller) getAppsflyerAppID(platform string) string {
	if strings.ToLower(platform) == "ios" {
		return c.AppsflyerIOSAppID
	}
	return c.AppsflyerAndroidAppID
}

func (controller Controller) CreateAudience(ctx context.Context, audience *dtos.AppsflyerAudience) (int, error) {
	if controller.AppsflyerClient == nil {
		return 0, fmt.Errorf("AppsFlyer client is nil")
	}

	logger.Infof("Creating audience with name: %s, platform: %s, description: %s", audience.Name, audience.Platform, audience.Description)

	// Call AppsFlyer API to create audience
	response, err := controller.AppsflyerClient.CreateAudience(
		ctx,
		audience.Name,
		audience.Platform,
		controller.getAppsflyerAppID(audience.Platform),
	)
	if err != nil {
		return 0, fmt.Errorf("failed to create audience in AppsFlyer: %w", err)
	}

	// Create entity for database insert
	audienceEntity := &entity.AppsflyerAudience{
		Name:                audience.Name,
		Platform:            audience.Platform,
		Description:         audience.Description,
		ImportKey:           response.Message.ImportKey,
		AppsflyerAudienceID: response.Message.AudienceID,
	}

	query := `
		INSERT INTO appsflyer_audiences 
		(name, platform, import_key, appsflyer_audience_id, description)
		VALUES (:name, :platform, :import_key, :appsflyer_audience_id, :description)
		RETURNING id
	`

	rows, err := controller.PostgresClient.NamedQueryContext(ctx, query, audienceEntity)
	if err != nil {
		return 0, fmt.Errorf("failed to store audience in database: %w", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return 0, fmt.Errorf("no id returned from audience insert")
	}

	var id int
	if err := rows.Scan(&id); err != nil {
		return 0, fmt.Errorf("failed to scan returned id: %w", err)
	}

	return id, nil
}

func (controller Controller) GetAllAudiences(ctx context.Context) (*appsflyer.ActiveAudienceResponse, error) {
	if controller.AppsflyerClient == nil {
		return nil, fmt.Errorf("AppsFlyer client is nil")
	}

	response, err := controller.AppsflyerClient.GetActiveAudiences(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get audiences from AppsFlyer: %w", err)
	}

	return response, nil
}

func (controller Controller) SyncDataToAppsflyer(ctx context.Context, selectFromHiveRunTableQuery string, audienceDetails dtos.AppsflyerAudienceDetails, appsflyerOperation string) error {
	errChan := make(chan error, 3)
	var cancel func()
	ctx, cancel = context.WithCancel(ctx)
	queryDataChan := make(chan *dtos.Result, 10000)
	payloadBatchChan := make(chan *appsflyer.AudienceRequest, 150)

	var wg sync.WaitGroup
	var cancelMutex sync.Mutex
	wg.Add(3)

	go func() {
		defer wg.Done()
		startTime := time.Now()
		err := controller.StarrocksController.QueryDataFromStarrocks(ctx, queryDataChan, selectFromHiveRunTableQuery)
		if err != nil {
			errChan <- err
			cancelMutex.Lock()
			cancel()
			cancelMutex.Unlock()
		}
		logger.Infof("Query Starrocks completed in %v\n", time.Since(startTime))
	}()

	go func() {
		defer wg.Done()
		startTime := time.Now()

		err := controller.ProcessDataForAppsflyer(ctx, queryDataChan, payloadBatchChan, audienceDetails, appsflyerOperation)
		if err != nil {
			errChan <- err
			cancelMutex.Lock()
			cancel()
			cancelMutex.Unlock()
		}
		logger.Infof("Data processing completed in %v\n", time.Since(startTime))
	}()

	go func() {
		defer wg.Done()
		startTime := time.Now()
		err := controller.KafkaController.ProduceToKafka(ctx, payloadBatchChan, controller.AppsflyerKafkaTopic)
		if err != nil {
			errChan <- err
			cancelMutex.Lock()
			cancel()
			cancelMutex.Unlock()
		}
		logger.Infof("Production to kafka completed in %v\n", time.Since(startTime))
	}()

	wg.Wait()
	close(errChan)
	for err := range errChan {
		return err
	}

	return nil
}

func (controller Controller) ProcessDataForAppsflyer(ctx context.Context, queryDataChan chan *dtos.Result, payloadBatchChan chan *appsflyer.AudienceRequest, audienceDetails dtos.AppsflyerAudienceDetails, appsflyerOperation string) error {
	var devices []appsflyer.Device
	var schema *dtos.MessageSchema
	columnIndexes := make(map[string]int)

	const bufferSize = 20000 // Buffer size to accumulate more devices before chunking

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled while processing data: %w", ctx.Err())

		case result, ok := <-queryDataChan:
			if !ok {
				// Channel closed, process remaining devices
				if len(devices) > 0 {
					deviceChunks := appsflyer.SplitDevices(devices)
					for _, chunk := range deviceChunks {
						payloadBatchChan <- &appsflyer.AudienceRequest{
							ImportKey: audienceDetails.ImportKey,
							Platform:  audienceDetails.Platform,
							Devices:   chunk,
							Operation: appsflyerOperation,
						}
					}
				}
				close(payloadBatchChan)
				return nil
			}

			// Store schema from first result and map column indexes
			if schema == nil && result.Schema != nil {
				schema = result.Schema
				for i, colName := range schema.ColumnNames {
					columnIndexes[colName] = i
				}
			}

			// Transform row to device
			if device := appsflyer.TransformRowToDevice(result, columnIndexes, controller.getAppsflyerAppID(audienceDetails.Platform), audienceDetails.Platform); device != nil {
				devices = append(devices, *device)
			}

			// Process in chunks when buffer is full
			if len(devices) >= bufferSize {
				deviceChunks := appsflyer.SplitDevices(devices)
				for _, chunk := range deviceChunks {
					payloadBatchChan <- &appsflyer.AudienceRequest{
						ImportKey: audienceDetails.ImportKey,
						Platform:  audienceDetails.Platform,
						Devices:   chunk,
						Operation: appsflyerOperation,
					}
				}
				devices = make([]appsflyer.Device, 0, bufferSize) // Preallocate capacity
			}
		}
	}
}

// IncrementalColumnSet represents a set of columns used in the incremental query
type incrementalColumnSet struct {
	DeviceID  string
	Phone     string
	Email     string
	PhoneE164 string
	Platform  string
}

// buildChangeDetectionCase constructs the CASE statement for detecting changes
func (controller Controller) buildChangeDetectionCase(columns *incrementalColumnSet) string {
	return fmt.Sprintf(`
        CASE 
            WHEN p.user_id IS NULL THEN 1
            WHEN (
                COALESCE(c.%[1]s, '') != COALESCE(p.%[1]s, '') OR
                COALESCE(c.%[2]s, '') != COALESCE(p.%[2]s, '') OR
                COALESCE(c.%[3]s, '') != COALESCE(p.%[3]s, '') OR
                COALESCE(c.%[4]s, '') != COALESCE(p.%[4]s, '') OR
                COALESCE(c.%[5]s, '') != COALESCE(p.%[5]s, '')
            ) THEN 1
            ELSE 0
        END`,
		columns.DeviceID,
		columns.Phone,
		columns.Email,
		columns.PhoneE164,
		columns.Platform,
	)
}

// buildColumnList returns the list of columns for SELECT statements
// if tableAlias is present then format is tableAlias.column_name, else column_name
func (controller Controller) buildColumnList(tableAlias string, columns *incrementalColumnSet) string {
	if tableAlias == "" {
		return fmt.Sprintf("%s, %s, %s, %s, %s",
			columns.DeviceID,
			columns.Phone,
			columns.Email,
			columns.PhoneE164,
			columns.Platform,
		)
	} else {
		return fmt.Sprintf("%s.%s, %s.%s, %s.%s, %s.%s, %s.%s",
			tableAlias, columns.DeviceID,
			tableAlias, columns.Phone,
			tableAlias, columns.Email,
			tableAlias, columns.PhoneE164,
			tableAlias, columns.Platform,
		)
	}
}

// buildAppsFlyerQuery constructs the complete AppsFlyer incremental query
func (controller Controller) buildAppsFlyerQuery(columns *incrementalColumnSet, currentTable, previousTable string) string {
	return fmt.Sprintf(`
    SELECT 
		user_id, 
		%s, 
		status,
		true as is_tg
	FROM (
        SELECT 
            c.user_id,
            %s, 
            '1' as status,
            %s as change_flag
        FROM %s c
        LEFT JOIN %s p ON c.user_id = p.user_id
        WHERE c.is_tg = true AND (p.is_tg = true OR p.user_id IS NULL)

        UNION ALL
        
        SELECT 
            p.user_id,
            %s,
            '0' as status,
            1 as change_flag
        FROM %s p
        LEFT JOIN %s c ON p.user_id = c.user_id
        WHERE p.is_tg = true AND c.user_id IS NULL
    ) subq
	WHERE change_flag = 1`,
		controller.buildColumnList("", columns),
		controller.buildColumnList("c", columns),
		controller.buildChangeDetectionCase(columns),
		fmt.Sprintf("%s.%s.%s", controller.StarrocksHiveCatalog, controller.StarrocksHiveDatabase, currentTable),
		fmt.Sprintf("%s.%s.%s", controller.StarrocksHiveCatalog, controller.StarrocksHiveDatabase, previousTable),
		controller.buildColumnList("p", columns),
		fmt.Sprintf("%s.%s.%s", controller.StarrocksHiveCatalog, controller.StarrocksHiveDatabase, previousTable),
		fmt.Sprintf("%s.%s.%s", controller.StarrocksHiveCatalog, controller.StarrocksHiveDatabase, currentTable),
	)
}

func (controller Controller) GetAppsflyerIncrementalQuery(currentSegmentTable, previousCommittedSegmentTable string) (string, error) {
	columns := incrementalColumnSet{
		DeviceID:  appsflyer.DeviceIdAttributeColumnName,
		Phone:     appsflyer.PhoneAttributeColumnName,
		Email:     appsflyer.EmailAttributeColumnName,
		PhoneE164: appsflyer.PhoneE164AttributeColumnName,
		Platform:  appsflyer.PlatformAttributeColumnName,
	}
	return controller.buildAppsFlyerQuery(&columns, currentSegmentTable, previousCommittedSegmentTable), nil
}
