package usercontroller

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	log "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
}

// GetUserByEmail retrieves a user by email
func (c Controller) GetUserByEmail(ctx context.Context, email string) (*postgres.User, error) {
	query := `
		SELECT id, tenant_id, name, email, google_id, is_allowed, created_at, updated_at
		FROM users
		WHERE email = $1
	`

	var user postgres.User
	err := c.PostgresClient.GetContext(ctx, &user, query, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user with email %s not found", email)
		}
		return nil, fmt.Errorf("error fetching user: %w", err)
	}

	return &user, nil
}

// GetUserByID retrieves a user by ID
func (c Controller) GetUserByID(ctx context.Context, id int64) (*postgres.User, error) {
	query := `
		SELECT id, tenant_id, name, email, google_id, is_allowed, created_at, updated_at
		FROM users
		WHERE id = $1
	`

	var user postgres.User
	err := c.PostgresClient.GetContext(ctx, &user, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user with id %d not found", id)
		}
		return nil, fmt.Errorf("error fetching user: %w", err)
	}

	return &user, nil
}

// CreateUser creates a new user using named parameters
func (c Controller) CreateUser(ctx context.Context, user *postgres.User) error {
	query := `
		INSERT INTO users (
			tenant_id, name, email, google_id, is_allowed, created_at, updated_at
		) VALUES (
			:tenant_id, :name, :email, :google_id, :is_allowed, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
		) RETURNING id, created_at, updated_at
	`

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, user)
	if err != nil {
		return fmt.Errorf("failed to insert user: %w", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return fmt.Errorf("no id returned from user insert")
	}

	err = rows.Scan(&user.Id, &user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		// If it fails, log the specific error
		return fmt.Errorf("failed to scan user result: %w", err)
	}

	return nil
}

// UpdateUser updates an existing user using named parameters
func (c Controller) UpdateUser(ctx context.Context, user *postgres.User) error {
	query := `
		UPDATE users
		SET 
			name = :name,
			google_id = :google_id,
			is_allowed = :is_allowed,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = :id
		RETURNING updated_at
	`

	rows, err := c.PostgresClient.NamedQueryContext(ctx, query, user)
	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return fmt.Errorf("no rows affected when updating user with id %d", user.Id)
	}

	err = rows.Scan(&user.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to scan updated user result: %w", err)
	}

	return nil
}

// GetOrCreateUser gets a user by email or creates a new one if not found
func (c Controller) GetOrCreateUser(ctx context.Context, email string, name string, googleID string) (*postgres.User, error) {
	// Try to get the user first
	user, err := c.GetUserByEmail(ctx, email)
	if err != nil {
		// If user not found, create a new one
		if err.Error() == fmt.Sprintf("user with email %s not found", email) {
			log.Info("User not found, creating new user", email)

			// Default tenant ID (assuming 1 is the default tenant)
			defaultTenantID := int64(1)

			newUser := &postgres.User{
				TenantID:  defaultTenantID,
				Name:      name,
				Email:     email,
				GoogleID:  googleID,
				IsAllowed: false, // Default not allowed
			}

			err = c.CreateUser(ctx, newUser)
			if err != nil {
				return nil, fmt.Errorf("error creating new user: %w", err)
			}

			return newUser, nil
		}

		// For other errors, return the error
		return nil, err
	}

	// If user exists but some fields need updating
	if user.GoogleID != googleID || user.Name != name {
		user.GoogleID = googleID
		user.Name = name

		err = c.UpdateUser(ctx, user)
		if err != nil {
			return nil, fmt.Errorf("error updating existing user: %w", err)
		}
	}

	return user, nil
}

// GetAllUsers retrieves all users
func (c Controller) GetAllUsers(ctx context.Context) ([]postgres.User, error) {
	query := `
		SELECT id, tenant_id, name, email, google_id, is_allowed, created_at, updated_at
		FROM users
		ORDER BY name
	`

	var users []postgres.User
	err := c.PostgresClient.SelectContext(ctx, &users, query)
	if err != nil {
		return nil, fmt.Errorf("error fetching users: %w", err)
	}

	return users, nil
}

// IsUserAllowed checks if a user is allowed to access the system
func (c Controller) IsUserAllowed(ctx context.Context, email string) (bool, error) {
	query := `
		SELECT is_allowed
		FROM users
		WHERE email = $1
	`

	var isAllowed bool
	err := c.PostgresClient.GetContext(ctx, &isAllowed, query, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, fmt.Errorf("user with email %s not found", email)
		}
		return false, fmt.Errorf("error checking user permission: %w", err)
	}

	return isAllowed, nil
}
