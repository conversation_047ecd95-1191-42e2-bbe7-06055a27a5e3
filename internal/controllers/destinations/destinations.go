package destinationcontroller

import (
	"context"
	"fmt"

	"github.com/Zomato/cdp-platform/dtos"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	log "github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
}

func (c Controller) GetAllDestinations(ctx context.Context) ([]dtos.Destination, error) {
	query := `
		SELECT 
			id,
			name,
			destination_type
		FROM destinations
	`

	var destinations []dtos.Destination
	err := c.PostgresClient.SelectContext(ctx, &destinations, query)
	if err != nil {
		log.Info("Error fetching destinations from postgres", err)
		return nil, err
	}

	return destinations, nil
}
func (c Controller) GetDestinationConfigById(ctx context.Context, destinationID int64) (*dtos.Destination, error) {
	query := `
		SELECT 
			id,
			name,
			destination_type,
			config
		FROM destinations
		WHERE id = $1
	`

	var destination dtos.Destination
	err := c.PostgresClient.GetContext(ctx, &destination, query, destinationID)
	if err != nil {
		log.Info("Error fetching feature store destination from postgres", err)
		return nil, fmt.Errorf("failed to fetch feature store destination: %w", err)
	}
	return &destination, nil
}
