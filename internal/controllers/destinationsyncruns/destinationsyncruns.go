package destinationsyncrunscontroller

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"errors"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	entities "github.com/Zomato/cdp-platform/entities/postgres"
	basecontroller "github.com/Zomato/cdp-platform/internal/controllers"
	featurestorecontroller "github.com/Zomato/cdp-platform/internal/controllers/featurestore"
	segmentcontroller "github.com/Zomato/cdp-platform/internal/controllers/segment"
	"github.com/Zomato/go/logger"
)

type Controller struct {
	*basecontroller.Controller
	SegmentController      *segmentcontroller.Controller
	FeatureStoreController *featurestorecontroller.Controller
}

func (controller Controller) AddDestinationSyncRun(ctx context.Context, destinationSyncRunDTO *dtos.DestinationSyncRun) (int64, error) {
	query := `
		INSERT INTO destination_sync_runs (
			destination_sync_id,
			execution_start,
			status
		) VALUES (
			:destination_sync_id,
			:execution_start,
			:status
		) RETURNING id`

	destinationSyncRunEntity := &entities.DestinationSyncRun{
		DestinationSyncID: destinationSyncRunDTO.DestinationSyncID,
		ExecutionStart:    destinationSyncRunDTO.ExecutionStart,
		Status:            destinationSyncRunDTO.Status,
	}

	rows, err := controller.PostgresClient.NamedQueryContext(ctx, query, destinationSyncRunEntity)
	if err != nil {
		return -1, err
	}
	defer rows.Close()
	var destinationSyncRunId int64
	if rows.Next() {
		err = rows.Scan(&destinationSyncRunId)
		if err != nil {
			return -1, err
		}
	}

	return destinationSyncRunId, nil
}

func (controller Controller) UpdateDestinationSyncRun(ctx context.Context, destinationSyncRunDTO *dtos.DestinationSyncRun) error {
	logger.Infoln("Update method started")
	query := "UPDATE destination_sync_runs SET "
	var updates []string
	args := map[string]interface{}{"id": destinationSyncRunDTO.Id}

	if destinationSyncRunDTO.ExecutionStart != nil && !destinationSyncRunDTO.ExecutionStart.IsZero() {
		updates = append(updates, "execution_start = :execution_start")
		args["execution_start"] = *destinationSyncRunDTO.ExecutionStart
	}
	if destinationSyncRunDTO.ExecutionEnd != nil && !destinationSyncRunDTO.ExecutionEnd.IsZero() {
		updates = append(updates, "execution_end = :execution_end")
		args["execution_end"] = *destinationSyncRunDTO.ExecutionEnd
	}
	if destinationSyncRunDTO.Status != "" {
		updates = append(updates, "status = :status")
		args["status"] = destinationSyncRunDTO.Status
	}
	if destinationSyncRunDTO.SyncTableName != nil && *destinationSyncRunDTO.SyncTableName != "" {
		updates = append(updates, "sync_table_name = :sync_table_name")
		args["sync_table_name"] = destinationSyncRunDTO.SyncTableName
	}
	if destinationSyncRunDTO.RowsAffected != nil && *destinationSyncRunDTO.RowsAffected != 0 {
		updates = append(updates, "rows_affected = :rows_affected")
		args["rows_affected"] = destinationSyncRunDTO.RowsAffected
	}
	if destinationSyncRunDTO.ErrorMessage != nil && *destinationSyncRunDTO.ErrorMessage != "" {
		updates = append(updates, "error_message = :error_message")
		args["error_message"] = destinationSyncRunDTO.ErrorMessage
	}

	if len(updates) == 0 {
		return errors.New("no fields to update")
	}

	query += strings.Join(updates, ", ") + " WHERE id = :id"

	_, err := controller.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (controller Controller) GetOngoingSegmentTableNameByDestinationSyncRunID(ctx context.Context, destinationSyncRunId int64) (string, error) {
	query := `
		SELECT ds.ongoing_segment_table_name
		FROM destination_sync_runs dsr
		JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
		WHERE dsr.id = $1
	`
	var ongoingSegmentTableName string
	err := controller.PostgresClient.GetContext(ctx, &ongoingSegmentTableName, query, destinationSyncRunId)
	if err != nil {
		return "", err
	}
	return ongoingSegmentTableName, nil
}

func (controller Controller) UpdateSnapshotTableNameInAppsflyer(ctx context.Context, destinationSyncRunId int64, snapshotTableName string) error {
	query := `
		UPDATE appsflyer_audiences
		SET snapshot_table_name = :ongoing_segment_table_name
		WHERE id = (
			SELECT asm.audience_id
			FROM destination_sync_runs dsr
			JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
			JOIN appsflyer_sync_mappings asm ON ds.id = asm.destination_sync_id
			WHERE dsr.id = :destination_sync_run_id
		)
	`

	args := map[string]interface{}{
		"ongoing_segment_table_name": snapshotTableName,
		"destination_sync_run_id":    destinationSyncRunId,
	}

	_, err := controller.PostgresClient.NamedExecContext(ctx, query, args)
	return err
}

func (controller Controller) CommitSegmentTableName(ctx context.Context, destinationSyncRunId int64) error {
	query := `
		UPDATE destination_syncs
		SET 
			commited_segment_table_name = ongoing_segment_table_name,
			ongoing_segment_table_name = ''
		WHERE id = (
			SELECT ds.id
			FROM destination_sync_runs dsr
			JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
			WHERE dsr.id = $1
		)
	`

	_, err := controller.PostgresClient.ExecContext(ctx, query, destinationSyncRunId)
	return err
}

func (controller Controller) GetAllDestinationSyncRuns(ctx context.Context, destinationSyncId int64, destinationType string) ([]dtos.DestinationSyncRun, error) {
	query := `
		SELECT 
			dsr.id,
			dsr.execution_start,
			dsr.execution_end,
			dsr.status,
			dsr.sync_table_name,
			dsr.rows_affected,
			dsr.error_message,
			pr.run_id AS feature_store_spark_run_id
		FROM destination_sync_runs dsr
		LEFT JOIN feature_store_spark_destination_sync_run_mapping pdm
			ON dsr.id = pdm.destination_sync_run_id
		LEFT JOIN feature_store_spark_runs pr
			ON pdm.feature_store_spark_run_id = pr.id
		WHERE dsr.destination_sync_id = $1
		ORDER BY dsr.execution_start DESC
		LIMIT 7;
	`

	var destinationSyncRuns []dtos.DestinationSyncRun
	err := controller.PostgresClient.SelectContext(ctx, &destinationSyncRuns, query, destinationSyncId)
	if err != nil {
		return nil, err
	}
	if len(destinationSyncRuns) == 0 {
		return destinationSyncRuns, nil
	}
	logger.Infof("Total runs count: %v", len(destinationSyncRuns))

	return destinationSyncRuns, nil
}

func (controller Controller) IsFirstTimeDestinationSync(ctx context.Context, destinationSyncId int64) (bool, error) {
	query := `
		SELECT COUNT(*) 
		FROM destination_sync_runs 
		WHERE destination_sync_id = $1 AND status = 'SUCCESSFUL'
	`

	var count int
	err := controller.PostgresClient.GetContext(ctx, &count, query, destinationSyncId)
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (controller Controller) GetAudienceNameByDestinationSyncRunID(ctx context.Context, destinationSyncRunId int64) (string, error) {
	query := `
		SELECT aa.name
		FROM destination_sync_runs dsr
		JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
		JOIN appsflyer_sync_mappings asm ON ds.id = asm.destination_sync_id
		JOIN appsflyer_audiences aa ON asm.audience_id = aa.id
		WHERE dsr.id = $1
	`
	var audienceName string
	err := controller.PostgresClient.GetContext(ctx, &audienceName, query, destinationSyncRunId)
	if err != nil {
		return "", err
	}
	return audienceName, nil
}

func (controller Controller) GetLastRunStatus(ctx context.Context, destinationSyncId int64) (string, error) {
	query := `
        SELECT status 
        FROM destination_sync_runs 
        WHERE destination_sync_id = $1 
        ORDER BY execution_start DESC 
        LIMIT 1
    `

	var status string
	err := controller.PostgresClient.GetContext(ctx, &status, query, destinationSyncId)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil // No previous runs exist
		}
		return "", fmt.Errorf("failed to get last run status: %w", err)
	}

	return status, nil
}

func (controller Controller) VerifyLastRunTerminalState(ctx context.Context, destinationSyncId int64) error {
	status, err := controller.GetLastRunStatus(ctx, destinationSyncId)
	if err != nil {
		return fmt.Errorf("failed to verify last run status: %w", err)
	}

	if status != "" && status != "SUCCESSFUL" {
		return fmt.Errorf("last run status was %s, cannot proceed with new sync", status)
	}

	return nil
}

// DestinationSyncRunCompletedAfterGivenTime checks if there are any destination sync runs
// of the specified destination type that completed (have execution_end) after the provided timestamp
func (controller Controller) DestinationSyncRunCompletedAfterGivenTime(ctx context.Context, timestamp time.Time, destinationType string, status string) (bool, error) {
	query := `
        SELECT COUNT(*) 
        FROM destination_sync_runs dsr
        JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
        JOIN destinations d ON ds.destination_id = d.id
        WHERE dsr.execution_end > $1 
        AND dsr.status = $2
        AND d.destination_type = $3
    `
	// status => interm table created
	var count int
	err := controller.PostgresClient.GetContext(ctx, &count, query, timestamp, status, destinationType)
	if err != nil {
		return false, fmt.Errorf("failed to count destination sync runs after timestamp: %w", err)
	}

	return count > 0, nil
}

// UpdatePendingFeatureStoreRuns checks and updates all feature store destination sync runs
// that are in DATA_PUSHED_FOR_CONSUMPTION state by calling the observability API
func (controller Controller) UpdatePendingFeatureStoreRuns(ctx context.Context) ([]int64, error) {
	// Query to get all pending feature store runs with their spark run IDs
	query := `
		SELECT 
			dsr.id,
			dsr.destination_sync_id,
			dsr.execution_start,
			dsr.status,
			pr.run_id AS feature_store_spark_run_id
		FROM destination_sync_runs dsr
		JOIN destination_syncs ds ON dsr.destination_sync_id = ds.id
		JOIN destinations d ON ds.destination_id = d.id
		LEFT JOIN feature_store_spark_destination_sync_run_mapping pdm
			ON dsr.id = pdm.destination_sync_run_id
		LEFT JOIN feature_store_spark_runs pr
			ON pdm.feature_store_spark_run_id = pr.id
		WHERE d.destination_type = 'FEATURE_STORE'
		AND dsr.status = 'DATA_PUSHED_FOR_CONSUMPTION'
		AND pr.run_id IS NOT NULL
		ORDER BY dsr.execution_start ASC
	`

	var pendingRuns []dtos.DestinationSyncRun
	err := controller.PostgresClient.SelectContext(ctx, &pendingRuns, query)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch pending feature store runs: %w", err)
	}

	if len(pendingRuns) == 0 {
		logger.Info("No pending feature store runs found")
		return nil, nil
	}

	logger.Infof("Found %d pending feature store runs to check", len(pendingRuns))

	successfulRunIDs := make([]int64, 0)
	failedCount := 0

	// Process each pending run
	for _, run := range pendingRuns {
		if run.FeatureStoreSparkRunID == nil {
			logger.Warnf("Skipping run %d: no feature store spark run ID", run.Id)
			continue
		}

		// Check completion status using the existing method
		completionStatus, err := controller.FeatureStoreController.GetCompletionStatus(ctx, *run.FeatureStoreSparkRunID)
		if err != nil {
			logger.Errorf("Failed to get completion status for run_id %s (destination_sync_run_id: %d): %v",
				*run.FeatureStoreSparkRunID, run.Id, err)
			failedCount++
			continue
		}

		if completionStatus {
			// Update the run to SUCCESSFUL with proper execution end time
			runUpdate := run
			runUpdate.Status = "SUCCESSFUL"

			// Set execution end time to current time since the sync has actually completed
			now := time.Now()
			runUpdate.ExecutionEnd = &now

			err := controller.UpdateDestinationSyncRun(ctx, &runUpdate)
			if err != nil {
				logger.Errorf("Failed to update destination sync run %d to SUCCESSFUL: %v", run.Id, err)
				failedCount++
				continue
			}

			logger.Infof("Successfully updated destination sync run %d (spark_run_id: %s) to SUCCESSFUL",
				run.Id, *run.FeatureStoreSparkRunID)
			successfulRunIDs = append(successfulRunIDs, run.Id)
		} else {
			// Check if the run has been in DATA_PUSHED_FOR_CONSUMPTION state for too long
			if run.ExecutionStart != nil {
				timeInState := time.Since(*run.ExecutionStart)

				// If it's been more than 2 hours in this state, log a warning
				if timeInState > 2*time.Hour {
					logger.Warnf("Run %d (spark_run_id: %s) has been in DATA_PUSHED_FOR_CONSUMPTION state for %v",
						run.Id, *run.FeatureStoreSparkRunID, timeInState)
				}

				// If it's been more than 6 hours, consider it potentially failed
				if timeInState > 6*time.Hour {
					logger.Errorf("Run %d (spark_run_id: %s) has been in DATA_PUSHED_FOR_CONSUMPTION state for %v - may need manual intervention",
						run.Id, *run.FeatureStoreSparkRunID, timeInState)
				}
			}
		}
	}

	logger.Infof("Processed %d pending runs: %d updated to SUCCESSFUL, %d failed to process",
		len(pendingRuns), len(successfulRunIDs), failedCount)

	return successfulRunIDs, nil
}

func (controller Controller) GetDestinationSyncRunByID(ctx context.Context, runID int64) (*dtos.DestinationSyncRun, error) {
	query := `
		SELECT id, destination_sync_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message
		FROM destination_sync_runs 
		WHERE id = $1
	`

	var run dtos.DestinationSyncRun
	err := controller.PostgresClient.GetContext(ctx, &run, query, runID)
	if err != nil {
		return nil, fmt.Errorf("failed to get destination sync run: %w", err)
	}

	return &run, nil
}
