  
service:
  port: ":8000"
  environment: "local"
  name: "cdp-platform"

org: "blinkit"

log:
  level: "info"
  format: "json"

auth:
  signing-key: "your-secure-signing-key"
  google:
    client-id: "your-google-client-id"
    client-secret: "your-google-client-secret"
    redirect-url: "http://localhost:8080/api/auth/google/callback"

# whitelist:
#   domains:
#     - "http://localhost:3000"
#     - "https://your-frontend-domain.com"

whitelist:
  domains: ["http://localhost:5173", "http://localhost:7999", "http://localhost:3000"]

slack:
  api-key: "YOUR_TOKEN_HERE"

postgres:
  kill_switch: false
  strong_consistency: 
    http: false
    worker: false
    appsflyer_consumer: false
  master:
    host: cdp-platform-postgres
    port: 5432
    dbname: cdp_platform
    username: cdp
    password: cdp
  replica:
    host: cdp-platform-postgres
    port: 5432
    dbname: cdp_platform
    username: cdp
    password: cdp

temporal:
  server:
    host: temporal
    port: 7233
  ui:
    host: localhost
    port: 8080
  metrics:
    host: localhost
    port: 49421
    path: metrics
  task_queue: "cdp_task_queue"

starrocks:
  strong_consistency:
    http: false
    worker: false
    appsflyer_consumer: false
  kill_switch: false
  master:
    host: host.docker.internal
    port: 9030
    username: root
    password: ""
    dbname: ""
  replica:
    host: host.docker.internal
    port: 9030
    username: root
    password: ""
    dbname: ""
  catalog:
    hive:
      name: "hive"
      schema: "cdp"
    default:
      name: "default_catalog"
      schema: "cdp"
      tables:
        user_attribute: "user_properties"
    iceberg: 
      name: "iceberg"
      feature_store_schema: "cdp"
      schema: "cdp"
  storage:
    s3:
      broker_load_bucket: "blinkit-data-staging/hive"
      aws_iam_role_arn: "arn:aws:iam::183295456051:role/stag-blinkit-cdp-starrocks-role"
      aws_region: "ap-southeast-1"

kafka: 
  brokers:
    - kafka1:9092
  security:
    protocol: ""
    sasl: 
      mechanism: ""
      username: ""
      password: ""

appsflyer:
  enable: true
  token: ""
  android_app_id: "com.application.zomato"
  ios_app_id: "id434613896" 

appsflyer_consumer:
  kafka_topic: "cdp.test.appflyer"
  consumer_group: "cdp_appsflyer_consumer"
  dlq_topic: "cdp.dlq.appsflyer"

query_engine:
  trino:
    hive:
      catalog: "blinkit_staging"
      schema: "cdp"

emr:
  region: "ap-south-1"
  endpoint: "http://localstack:4566"

s3:
  endpoint: "http://localstack:4566"
  region: "ap-south-1"  # TODO : to remove


feature-store:
  enable: true
  endpoint: "http://localstack:4566"
  api-key: "test"
  tenant-id: "1"
  tenant-namespace : "user"
