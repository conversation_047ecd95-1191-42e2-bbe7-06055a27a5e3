# End Date Feature Testing Guide

This document provides comprehensive testing for the end_date feature implementation from PR #169.

## Overview

The end_date feature adds support for setting expiration dates on attribute queries and destination syncs. Key changes include:

1. **Database Migration**: Added `end_date` column to `attribute_queries` table
2. **API Endpoints**: New endpoint for updating end dates
3. **Validation Logic**: End dates must be in the future and can only be increased
4. **Filtering**: Expired queries are excluded from listing endpoints
5. **Destination Sync**: End date validation based on segment attributes

## Quick Start

### 1. Start the Server
```bash
# Using the provided aliases
dcb && dcu

# Or using the test script
./run_tests.sh start
```

### 2. Run Tests

#### Dry Run (Test Mode)
```bash
# Test mode - no actual API calls or database operations
python3 test_end_date_feature.py --test-mode

# Or using the script
./run_tests.sh test
```

#### Real Tests
```bash
# Real tests against running server
python3 test_end_date_feature.py

# Or using the script
./run_tests.sh run
```

## Test Coverage

### 1. Database Migration Tests
- ✅ Verify `end_date` column exists in `attribute_queries` table
- ✅ Check column properties (type, nullable, default)

### 2. API Endpoint Tests
- ✅ `GET /api/attribute-queries/all` includes `end_date` field
- ✅ `GET /api/attribute-queries?id={id}` includes `end_date` field
- ✅ `POST /api/attribute-queries/update/end-date` updates end date
- ✅ `POST /api/attributes/register` accepts `end_date` parameter

### 3. Validation Tests
- ✅ Valid future end date updates succeed
- ✅ Past end dates are rejected
- ✅ Decreasing end dates are rejected
- ✅ End date format validation

### 4. Filtering Tests
- ✅ Expired queries are excluded from `/all` endpoint
- ✅ Only active queries (end_date > NOW()) are returned

### 5. Integration Tests
- ✅ Temporal schedule updates with end dates
- ✅ Destination sync end date validation

## Manual Testing Commands

### Database Operations
```bash
# Check table schema
./run_tests.sh schema

# View sample data
./run_tests.sh data

# Insert test data
./run_tests.sh insert

# Clean up test data
./run_tests.sh cleanup
```

### API Testing with curl

#### Get All Attribute Queries
```bash
curl -X GET "http://localhost:8000/api/attribute-queries/all" \
  -H "Content-Type: application/json"
```

#### Get Specific Attribute Query
```bash
curl -X GET "http://localhost:8000/api/attribute-queries?id=1" \
  -H "Content-Type: application/json"
```

#### Update End Date
```bash
curl -X POST "http://localhost:8000/api/attribute-queries/update/end-date" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "end_date": "2025-12-31"
  }'
```

#### Register Attribute with End Date
```bash
curl -X POST "http://localhost:8000/api/attributes/register" \
  -H "Content-Type: application/json" \
  -d '{
    "data_source_id": 1,
    "cron_expression": "0 */6 * * *",
    "query_text": "SELECT user_id, test_column FROM test_table",
    "attributes": [{
      "name": "Test Attribute",
      "description": "Test attribute with end date",
      "query_column_name": "test_column",
      "data_type": "varchar",
      "attribute_category": "METRIC",
      "attribute_purpose": "GENERAL"
    }],
    "owner_id": 1,
    "entity_id": 1,
    "end_date": "2025-12-31"
  }'
```

## Database Test Queries

### Check Migration Applied
```sql
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'attribute_queries' AND column_name = 'end_date';
```

### View Queries with End Dates
```sql
SELECT id, data_source_id, owner_id, cron_expression, 
       end_date, created_at, updated_at 
FROM attribute_queries 
ORDER BY id;
```

### Test Filtering Logic
```sql
-- This should match the application logic
SELECT id, end_date, 
       CASE WHEN end_date > NOW() THEN 'ACTIVE' ELSE 'EXPIRED' END as status
FROM attribute_queries 
WHERE end_date > NOW()
ORDER BY id;
```

## Expected Behavior

### End Date Validation Rules
1. **Future Dates Only**: End dates must be in the future
2. **Increase Only**: End dates can only be increased, never decreased
3. **Format**: Accepts "YYYY-MM-DD" format
4. **Timezone**: Parsed as IST (Indian Standard Time)

### API Response Changes
- All attribute query responses now include `end_date` field
- Expired queries are automatically filtered from listing endpoints
- Update operations return success/error messages

### Error Scenarios
- Past end dates return 400/500 error
- Decreasing end dates return validation error
- Invalid date formats return parsing error
- Missing authentication returns 401/403 error

## Troubleshooting

### Server Not Starting
```bash
# Check if containers are running
docker ps

# Check logs
docker-compose logs cdp-platform-server

# Restart services
docker-compose down && docker-compose up -d
```

### Database Connection Issues
```bash
# Test database connection
docker exec -it cdp-platform-postgres psql -U cdp -d cdp_platform -c "SELECT 1;"

# Check if migration applied
./run_tests.sh schema
```

### API Authentication Issues
- The current implementation uses cookie-based authentication
- For testing, you may need to handle CSRF tokens
- Check server logs for authentication errors

## Files Created

1. **`test_end_date_feature.py`** - Main Python test script
2. **`run_tests.sh`** - Bash wrapper script for easy testing
3. **`TEST_END_DATE_FEATURE.md`** - This documentation

## Next Steps

After running the tests:

1. **Review Results**: Check which tests pass/fail
2. **Fix Issues**: Address any failing tests
3. **Manual Verification**: Test edge cases manually
4. **Performance Testing**: Test with large datasets
5. **Integration Testing**: Test with real workflows

## Support

For issues or questions:
1. Check server logs: `docker-compose logs cdp-platform-server`
2. Check database state: `./run_tests.sh data`
3. Run in test mode first: `./run_tests.sh test`
4. Verify server is running: `./run_tests.sh check`
