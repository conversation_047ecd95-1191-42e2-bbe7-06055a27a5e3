name: Build and Push to ECR

on:
  release:
    types: [published]

permissions:
  id-token: write
  pull-requests: write
  checks: write
  contents: write

env:
  AWS_REGION: ap-southeast-1
  ECR_REPOSITORY: cdp-platform
  COMMIT_HASH: commit-${{ github.sha }}
  RELEASE_TAG: ${{ github.event.release.tag_name }}

jobs:
  build-and-push:
    runs-on: [ self-hosted, 8v, od, acc-infraprod, arch-x64 ]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Create .netrc file
      run: |
        echo "machine github.com" > .netrc
        echo "login ${GITHUB_BOT_NAME}" >> .netrc
        echo "password ${GITHUB_BOT_PAC}" >> .netrc
        chmod 600 .netrc

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        role-to-assume: arn:aws:iam::125719378300:role/github-runner-role
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build Docker image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$RELEASE_TAG .

    - name: Tag Docker image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$RELEASE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_HASH

    - name: Push Docker images to ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$RELEASE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_HASH
