name: Build and Push PR to ECR.

on:
  pull_request:
    branches: [master]

permissions:
  id-token: write
  pull-requests: write
  checks: write
  contents: write

env:
  AWS_REGION: ap-southeast-1
  ECR_REPOSITORY: cdp-platform
  COMMIT_HASH: commit-${{ github.sha }}

jobs:
  build-and-push:
    runs-on: [ self-hosted, 8v, od, acc-infraprod, arch-x64 ]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v2 
      with:
        ref: ${{ github.event.pull_request.head.sha }}
        fetch-depth: 0

    - name: Check for build command
      id: check-changes
      run: |
        LATEST_COMMIT_MSG=$(git log -1 --pretty=%B)
        if [[ "$LATEST_COMMIT_MSG" == "[build]"* ]]; then
          echo "build_image=true" >> $GITHUB_OUTPUT
        else
          echo "build_image=false" >> $GITHUB_OUTPUT
        fi
 
    - name: Create .netrc file
      if: steps.check-changes.outputs.build_image == 'true'
      run: |
        echo "machine github.com" > .netrc
        echo "login ${GITHUB_BOT_NAME}" >> .netrc
        echo "password ${GITHUB_BOT_PAC}" >> .netrc
        chmod 600 .netrc

    - name: Configure AWS credentials
      if: steps.check-changes.outputs.build_image == 'true'
      uses: aws-actions/configure-aws-credentials@v2
      with:
        role-to-assume: arn:aws:iam::125719378300:role/github-runner-role
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      if: steps.check-changes.outputs.build_image == 'true'
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build Docker image
      if: steps.check-changes.outputs.build_image == 'true'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_HASH .

    - name: Push Docker image to ECR
      if: steps.check-changes.outputs.build_image == 'true'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_HASH

    - name: Comment PR
      uses: actions/github-script@v6
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        build_image: ${{ steps.check-changes.outputs.build_image }}
      with:
        script: |
          const changes = process.env.build_image === 'true';
          let message;
          
          if (changes) {
            const image = `${process.env.ECR_REGISTRY}/${process.env.ECR_REPOSITORY}:${process.env.COMMIT_HASH}`;
            message = `🐳 Docker image has been built and pushed to ECR:\n\`\`\`\n${image}\n\`\`\``;
          } else {
            message = `ℹ️ Skipped image build and push. When you are ready, start your commit message with "[build]" to trigger a build.`;
          }

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: message
          })
