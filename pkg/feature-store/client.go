package featurestore

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"time"

	"github.com/Zomato/go/logger"
)

type Client struct {
	config     Config
	httpClient *http.Client
}

func NewClient(config Config) (*Client, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

func (c *Client) do(ctx context.Context, method, urlPath string, queryParams url.Values, body interface{}, result interface{}) error {
	u, err := url.Parse(c.config.BaseURL)
	if err != nil {
		return fmt.Errorf("failed to parse base URL: %w", err)
	}
	u.Path = path.Join(u.Path, urlPath)

	if queryParams != nil {
		u.RawQuery = queryParams.Encode()
	}

	var reqBody io.Reader
	if body != nil {
		var jsonBody []byte
		jsonBody, err = json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
		logger.Infof("FeatureStore Request Payload: %s", string(jsonBody))
	}

	req, err := http.NewRequestWithContext(ctx, method, u.String(), reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	logger.Infof("Feature store response status: %d\n", resp.StatusCode)
	logger.Infof("Feature store response body: %s\n", string(respBody))

	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err == nil && errResp.Error != "" {
			return fmt.Errorf("Feature store API error: %s, message: %s, status: %d", errResp.Error, errResp.Message, errResp.Status)
		}
		return fmt.Errorf("unexpected status code: %d, response: %s", resp.StatusCode, string(respBody))
	}

	if result != nil {
		if len(respBody) == 0 {
			return nil
		}

		if err := json.NewDecoder(bytes.NewReader(respBody)).Decode(result); err != nil {
			var rawMap map[string]json.RawMessage
			if mapErr := json.Unmarshal(respBody, &rawMap); mapErr == nil {
				if dataJSON, ok := rawMap["data"]; ok {
					if dataErr := json.Unmarshal(dataJSON, result); dataErr != nil {
						return fmt.Errorf("failed to decode response data field: %w", dataErr)
					}
					return nil
				}
			}
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("x-api-key", c.config.APIKey)
	req.Header.Set("x-tenant-id", c.config.TenantID)
	req.Header.Set("x-tenant-namespace", c.config.TenantNamespace)
}
