package featurestore

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
)

func (c *Client) GetContextDetails(ctx context.Context, entity, contextName string, tenant, namespace string) (*Context, error) {
	urlPath := fmt.Sprintf("/properties/%s/%s", entity, contextName)
	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)

	var contextData Context
	err := c.do(ctx, http.MethodGet, urlPath, queryParams, nil, &contextData)
	if err != nil {
		return nil, fmt.Errorf("failed to get context details: %w", err)
	}
	return &contextData, nil
}

func (c *Client) GetActiveContexts(ctx context.Context, tenant, namespace string) ([]Context, error) {
	urlPath := "/properties/active/contexts"

	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)
	var contexts []Context
	err := c.do(ctx, http.MethodGet, urlPath, queryParams, nil, &contexts)
	if err != nil {
		return nil, fmt.Errorf("failed to get active contexts: %w", err)
	}

	return contexts, nil
}

func (c *Client) UpdateContext(ctx context.Context, entity, contextName string, tenant, namespace string, updateRequest ContextUpdateRequest) error {
	urlPath := fmt.Sprintf("/properties/%s/%s", entity, contextName)

	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)

	var response StatusResponse
	err := c.do(ctx, http.MethodPatch, urlPath, queryParams, updateRequest, &response)
	if err != nil {
		return fmt.Errorf("failed to update context: %w", err)
	}

	return nil
}

func (c *Client) CreateContext(ctx context.Context, contextRequest Context) error {
	urlPath := "/properties/context"

	var response StatusResponse
	err := c.do(ctx, http.MethodPut, urlPath, nil, contextRequest, &response)
	if err != nil {
		return fmt.Errorf("failed to create context: %w", err)
	}

	return nil
}
