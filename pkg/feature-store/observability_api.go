package featurestore

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
)

// func (c *Client) IncreaseExpectedCount(ctx context.Context, runID string, request IncreaseExpectedCountRequest) (*ObservabilityResponse, error) {
// 	encodedRunID := url.QueryEscape(runID)
// 	urlPath := fmt.Sprintf("/observability/%s", encodedRunID)
// 	var response ObservabilityResponse
// 	err := c.do(ctx, http.MethodPut, urlPath, nil, request, &response)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to increase expected count for DAG run %s: %w", runID, err)
// 	}

// 	return &response, nil
// }

func (c *Client) GetObservabilityStatus(ctx context.Context, runID string) (*ObservabilityResponse, error) {
	encodedRunID := url.QueryEscape(runID)
	urlPath := fmt.Sprintf("/observability/%s", encodedRunID)

	var response ObservabilityResponse
	err := c.do(ctx, http.MethodGet, urlPath, nil, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get observability status for DAG run %s: %w", runID, err)
	}

	return &response, nil
}
