package featurestore

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
)

func (c *Client) GetFeature(ctx context.Context, entity, contextName, featureName string, tenant, namespace string) (*Feature, error) {
	urlPath := fmt.Sprintf("/properties/%s/%s/%s", entity, contextName, featureName)

	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)

	var featureData Feature
	err := c.do(ctx, http.MethodGet, urlPath, queryParams, nil, &featureData)

	if err != nil {
		return nil, fmt.Errorf("failed to get feature details: %w", err)
	}
	return &featureData, nil
}

func (c *Client) GetContextFeatures(ctx context.Context, entity, contextName string, tenant, namespace string, active bool, validate bool) ([]Feature, error) {
	urlPath := fmt.Sprintf("/properties/%s/%s/features", entity, contextName)

	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)

	if active {
		queryParams.Set("active", "true")
	}

	if validate {
		queryParams.Set("validate", "true")
	}
	var features []Feature
	err := c.do(ctx, http.MethodGet, urlPath, queryParams, nil, &features)
	if err != nil {
		return nil, fmt.Errorf("failed to get context features: %w", err)
	}

	return features, nil
}

func (c *Client) CreateFeature(ctx context.Context, featureRequest Feature) error {
	urlPath := "/properties/feature"

	var response StatusResponse
	err := c.do(ctx, http.MethodPut, urlPath, nil, featureRequest, &response)
	if err != nil {
		return fmt.Errorf("failed to create feature: %w", err)
	}

	return nil
}

func (c *Client) UpdateFeature(ctx context.Context, entity, contextName, featureName string, tenant, namespace string, updateRequest FeatureUpdateRequest) error {
	urlPath := fmt.Sprintf("/properties/%s/%s/%s", entity, contextName, featureName)

	queryParams := url.Values{}
	queryParams.Set("tenant", tenant)
	queryParams.Set("namespace", namespace)

	var response StatusResponse
	err := c.do(ctx, http.MethodPatch, urlPath, queryParams, updateRequest, &response)
	if err != nil {
		return fmt.Errorf("failed to update feature: %w", err)
	}
	return nil
}
