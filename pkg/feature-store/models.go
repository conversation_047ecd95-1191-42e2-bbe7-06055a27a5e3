package featurestore

import (
	"time"
)

const (
	// Device identifier columns for queries
	UserIdAttributeColumnName = "user_id"
	DefaultEntity             = "user"
	DefaultContextName        = "audiences_cdp_1"
	DefaultContextType        = "user"
	DefaultFeatureType        = "boolean"
	DefaultIsActive           = true
	DefaultIsVerified         = false
	DefaultIsPII              = false
)

type User struct {
	User  string `json:"user,omitempty"`
	Email string `json:"email,omitempty"`
}

type Context struct {
	Tenant      string    `json:"tenant"`
	Namespace   string    `json:"namespace"`
	CreatedBy   User      `json:"created_by"`
	IsActive    bool      `json:"is_active"`
	ContextType string    `json:"context_type"`
	ContextName string    `json:"context_name"`
	Entity      string    `json:"entity"`
	TTL         int       `json:"ttl"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at,omitempty"`
	UpdatedAt   time.Time `json:"updated_at,omitempty"`
	UpdatedBy   *User     `json:"updated_by,omitempty"`
}

type Feature struct {
	Tenant       string    `json:"tenant,omitempty"`
	Namespace    string    `json:"namespace,omitempty"`
	CreatedBy    User      `json:"created_by"`
	IsActive     bool      `json:"is_active"`
	ContextType  string    `json:"context_type,omitempty"`
	ContextName  string    `json:"context_name,omitempty"`
	Entity       string    `json:"entity,omitempty"`
	FeatureName  string    `json:"feature_name"`
	FeatureType  string    `json:"feature_type"`
	IsVerified   bool      `json:"is_verified"`
	IsPII        bool      `json:"is_pii"`
	Destinations []string  `json:"destinations,omitempty"`
	Description  string    `json:"description"`
	Expiry       string    `json:"expiry"`
	CreatedAt    time.Time `json:"created_at,omitempty"`
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	UpdatedBy    *User     `json:"updated_by,omitempty"`
}

type ContextUpdateRequest struct {
	IsActive  *bool `json:"is_active,omitempty"`
	UpdatedBy User  `json:"updated_by"`
}

type FeatureUpdateRequest struct {
	IsActive  *bool   `json:"is_active,omitempty"`
	CreatedAt *string `json:"created_at,omitempty"`
	Expiry    *string `json:"expiry,omitempty"`
	UpdatedBy User    `json:"updated_by"`
}

type StatusResponse struct {
	Status  string `json:"status,omitempty"`
	Error   string `json:"error,omitempty"`
	Message string `json:"message,omitempty"`
	Success *bool  `json:"success,omitempty"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Status  int    `json:"status"`
}

// type IncreaseExpectedCountRequest struct {
// 	IncreaseExpectedCount int `json:"increase_expected_count"`
// }

type Data struct {
	DagRunID      string `json:"dag_run_id"`
	ExpectedCount int    `json:"expected_count"`
	SuccessCount  int    `json:"success_count"`
	FailureCount  int    `json:"failure_count"`
}

type ObservabilityResponse struct {
	Data    Data   `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// SelectColumns returns the columns needed for FeatureStore
func SelectColumns() []string {
	return []string{UserIdAttributeColumnName}
}
