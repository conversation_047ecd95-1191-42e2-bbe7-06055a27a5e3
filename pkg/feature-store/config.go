package featurestore

import (
	"fmt"
	"net/url"
)

type Config struct {
	BaseURL         string `json:"base_url" yaml:"base_url"`
	APIKey          string `json:"api_key" yaml:"api_key"`
	TenantID        string `json:"tenant_id" yaml:"tenant_id"`
	TenantNamespace string `json:"tenant_namespace" yaml:"tenant_namespace"`
}

func (c *Config) Validate() error {
	if c.BaseURL == "" {
		return fmt.Errorf("base URL is required")
	}

	// Parse the URL first to check if it has a scheme
	parsedURL, err := url.Parse(c.BaseURL)
	if err != nil {
		return fmt.Errorf("invalid base URL: %w", err)
	}

	// If no scheme is provided, auto-prepend https:// for security by default
	if parsedURL.Scheme == "" {
		c.BaseURL = "https://" + c.BaseURL
		// Re-parse after adding the scheme
		parsedURL, err = url.Parse(c.BaseURL)
		if err != nil {
			return fmt.Errorf("invalid base URL after adding scheme: %w", err)
		}
	}

	// Validate that the scheme is either http or https
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("base URL must use http or https scheme, got: %s", parsedURL.Scheme)
	}

	if c.APIKey == "" {
		return fmt.Errorf("API key is required")
	}

	if c.TenantID == "" {
		return fmt.Errorf("tenant ID is required")
	}

	if c.TenantNamespace == "" {
		return fmt.Errorf("tenant namespace is required")
	}

	return nil
}
