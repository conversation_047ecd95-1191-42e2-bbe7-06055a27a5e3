package trino

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"runtime/debug"
	"time"

	"database/sql"

	"github.com/Zomato/cdp-platform/dtos"
	log "github.com/Zomato/go/logger"

	// Using trino client
	trinoclient "github.com/trinodb/trino-go-client/trino"
)

var (
	ErrContextClosed = errors.New("context closed while query was running")
)

// TODO Separate this config out
type Config struct {
	Host     string `json:"host"`
	Port     int32  `json:"port"`
	Catalog  string `json:"catalog"`
	Schema   string `json:"schema"`
	Username string `json:"username"`
	Password string `json:"password"`
	SSL      bool   `json:"ssl"`
	Source   string `json:"source"`
}

type RunRequest struct {
	TrinoConfig     *Config
	Query           string
	ReplyChan       chan *dtos.Result
	ShutdownChannel *ShutdownChannel
}

type ShutdownChannel struct {
	Completed chan struct{}
	Error<PERSON>han chan error
}

func ExecuteSQL(ctx context.Context, runRequest RunRequest) {
	log := log.FromContext(ctx)
	log.Info("Starting ExecuteSQL")

	// Go routine panic and channel completion handling
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("[PANIC] | Stacktrace: %s | error: %+v", debug.Stack(), r)
			log.WithError(err).Error("Handling panic")
		} else {
			log.Info("Batch Read completed")
			runRequest.ShutdownChannel.Completed <- struct{}{}
		}
	}()

	trino, err := GetTrinoClient(runRequest.TrinoConfig)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "trino_connection",
		}).Error("Got error while connecting to trino cluster")
		runRequest.ShutdownChannel.ErrorChan <- err
		return
	}

	rows, err := trino.QueryContext(ctx, runRequest.Query, sql.Named("X-Trino-User", runRequest.TrinoConfig.Username))
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"event_name": "trino_query_error",
		}).Error("Got error while running trino query")
		runRequest.ShutdownChannel.ErrorChan <- err
		return
	}

	// Start a goroutine to read the rows
	batchRead(ctx, log, rows, runRequest.ReplyChan, runRequest.ShutdownChannel)
}

func GetTrinoClient(trinoConfig *Config) (*sql.DB, error) {
	trinoclient.DefaultQueryTimeout = 10 * time.Minute
	trinoclient.DefaultCancelQueryTimeout = 10 * time.Minute
	log.Info(fmt.Sprintf("Set DefaultQueryTimeout to %v and DefaultCancelQueryTimeout to %v",
		trinoclient.DefaultQueryTimeout,
		trinoclient.DefaultCancelQueryTimeout))

	log.Info("Creating Trino client")
	log.Info(trinoConfig)
	authPrefix := fmt.Sprintf("http://%s", trinoConfig.Host)
	if trinoConfig.SSL {
		authPrefix = fmt.Sprintf("http://%s:%s", trinoConfig.Username, url.QueryEscape(trinoConfig.Password))
	}
	dsn := fmt.Sprintf("%s@%s:%d?catalog=%s&schema=%s&source=%s",
		authPrefix,
		trinoConfig.Host,
		trinoConfig.Port,
		trinoConfig.Catalog,
		trinoConfig.Schema,
		trinoConfig.Source)
	return sql.Open("trino", dsn)
}

func batchRead(ctx context.Context, log *log.Logger, rows *sql.Rows, replyChan chan *dtos.Result, shutdownChannel *ShutdownChannel) {
	columns, err := rows.Columns()
	if err != nil {
		shutdownChannel.ErrorChan <- err
		return
	}
	colTypes, err := rows.ColumnTypes()
	if err != nil {
		shutdownChannel.ErrorChan <- err
		return
	}
	var columnNames, columnSchemas []string
	for _, colType := range colTypes {
		columnNames = append(columnNames, colType.Name())
		columnSchemas = append(columnSchemas, GetColType(colType.DatabaseTypeName()))
	}

	sendSchema := true
	var noOfRows int32
	defer func() {
		log.Info(fmt.Sprintf(`No of rows: %d`, noOfRows))
	}()

	for {
		select {
		// Handling context close
		case <-ctx.Done():
			log.Warn("Context done, exiting batchRead")
			shutdownChannel.ErrorChan <- ErrContextClosed
			return // Exit the function to avoid leaking the goroutine
		default:
			if rows.Next() {
				log.Info("Processing next row")
				cols := make([]interface{}, len(columns))
				columnPointers := make([]interface{}, len(columns))
				for i := range columns {
					columnPointers[i] = &cols[i]
				}
				if err := rows.Scan(columnPointers...); err != nil {
					log.WithError(err).Error("Error scanning row")
					shutdownChannel.ErrorChan <- err
					return
				}
				var processedcols []*dtos.AnyValue
				for i := range columns {
					processedcols = append(processedcols, &dtos.AnyValue{
						Type:  columnSchemas[i],
						Value: *columnPointers[i].(*interface{}),
					})
				}
				row := &dtos.Row{
					Columns: processedcols,
				}
				response := &dtos.Result{
					Row: row,
				}
				if sendSchema {
					response.Schema = &dtos.MessageSchema{
						ColumnSchemas: columnSchemas,
						ColumnNames:   columnNames,
					}
					sendSchema = false
				}
				noOfRows++
				replyChan <- response
			} else {
				if err := rows.Err(); err != nil {
					log.WithError(err).Error("Error while processing the query result")
					shutdownChannel.ErrorChan <- err
				}

				if err = rows.Close(); err != nil {
					log.WithError(err).Error("Got error while closing the rows")
					shutdownChannel.ErrorChan <- err
				}
				return // Exit the function after processing all rows
			}
		}
	}
}

func GetColType(dbType string) string {
	switch dbType {
	case "integer", "bigint", "smallint", "tinyint":
		return "INT"
	case "varchar":
		return "STRING"
	case "double", "real":
		return "DOUBLE"
	case "boolean":
		return "BOOLEAN"
	default:
		return dbType
	}
}
