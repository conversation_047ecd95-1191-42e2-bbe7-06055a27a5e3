package sqlx

import (
	"fmt"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq" // PostgreSQL driver
	"github.com/pkg/errors"
)

var (
	ErrInvalidConfig = errors.New("invalid config")
)

type role string

const (
	roleMaster = role("master")
	roleSlave  = role("slave")
)

// DB is goroutine safe from DB usage perspective.
// The unexported fields are not thread safe and are not intended to be used
// outside the scope of this package.
type DB struct {
	role   role
	config *Config
	*sqlx.DB
}

type DBPool struct {
	master    *DB
	replica   *DB
	closeChan chan struct{}
}

func open(driverName string, role role, cfg *Config) (*DB, error) {
	if cfg == nil {
		return nil, ErrInvalidConfig
	}

	sqlxDB, err := sqlx.Open(driverName, cfg.FormatDSN())
	if err != nil {
		return nil, err
	}

	return &DB{
		role:   role,
		config: cfg,
		DB:     sqlxDB,
	}, nil
}

// New creates a pool of databases to master and slave based on the config
func New(driverName string, masterConfig *Config, replicaConfig *Config) (*DBPool, error) {
	if masterConfig == nil {
		return nil, ErrInvalidConfig
	}

	// Set the driver name in the config
	masterConfig.DriverName = driverName

	masterDB, dbOpenErr := open(driverName, roleMaster, masterConfig)
	if dbOpenErr != nil {
		return nil, dbOpenErr
	}

	masterDB.SetMaxOpenConns(masterConfig.MaxOpenConn)
	masterDB.SetMaxIdleConns(masterConfig.MaxIdleConn)
	masterDB.SetConnMaxLifetime(masterConfig.ConnMaxLifetime)
	masterDB.SetConnMaxIdleTime(masterConfig.ConnMaxIdleTime)

	if dbOpenErr := masterDB.Ping(); dbOpenErr != nil {
		return nil, fmt.Errorf("connect failed for %s - %w", masterConfig.Host, dbOpenErr)
	}

	// For StarRocks (mysql driver), we might not have a replica config
	// In this case, use master as replica
	var replicaDB *DB
	if replicaConfig == nil {
		replicaDB = masterDB
	} else {
		replicaConfig.DriverName = driverName
		replicaDB, dbOpenErr = open(driverName, roleSlave, replicaConfig)
		if dbOpenErr != nil {
			return nil, dbOpenErr
		}

		replicaDB.SetMaxOpenConns(replicaConfig.MaxOpenConn)
		replicaDB.SetMaxIdleConns(replicaConfig.MaxIdleConn)
		replicaDB.SetConnMaxLifetime(replicaConfig.ConnMaxLifetime)
		replicaDB.SetConnMaxIdleTime(replicaConfig.ConnMaxIdleTime)

		if dbOpenErr = replicaDB.Ping(); dbOpenErr != nil {
			return nil, fmt.Errorf("connect failed for %s - %w", replicaConfig.Host, dbOpenErr)
		}
	}

	pool := &DBPool{master: masterDB, replica: replicaDB, closeChan: make(chan struct{})}
	return pool, nil
}

func (db *DBPool) Close() error {
	// ideally, we should send something on an unbuffered channel so that flush is complete
	close(db.closeChan)

	if err := db.master.Close(); err != nil {
		return err
	}

	if err := db.replica.Close(); err != nil {
		return err
	}

	return nil
}

// Consistency interface declares methods that will be used to resolve the database
// from a pool of databases. The database returned from the pool may differ for different
// types of consistencies.
type Consistency interface {
	// Reader takes input a database pool and returns a db instance which is suitable
	// for reading.
	Reader(*DBPool) *DB

	// Writer takes input a database pool and returns a *DB instance which is suitable
	// for writing (and hence reading).
	Writer(*DBPool) *DB

	// Resolve takes input a database pool and returns *DB instance which is suitable
	// for execution of the input query.
	Resolve(pool *DBPool, query string) *DB
}

type ConsistencyEventual struct{}

func (ConsistencyEventual) Reader(pool *DBPool) *DB {
	return pool.replica
}
func (ConsistencyEventual) Writer(pool *DBPool) *DB {
	return pool.master
}
func (ConsistencyEventual) Resolve(_ *DBPool, _ string) *DB {
	panic("not implemented!")
}

type ConsistencyStrong struct{}

func (ConsistencyStrong) Reader(pool *DBPool) *DB {
	return pool.master
}
func (ConsistencyStrong) Writer(pool *DBPool) *DB {
	return pool.master
}
func (ConsistencyStrong) Resolve(pool *DBPool, _ string) *DB {
	return pool.master
}

func (p *DBPool) Ping() error {
	if p == nil {
		return errors.New("connection pool is nil")
	}

	// Try master first
	if err := p.master.DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping master: %w", err)
	}

	// If replica exists and is different from master, ping it too
	if p.replica != nil && p.replica != p.master {
		if err := p.replica.DB.Ping(); err != nil {
			return fmt.Errorf("failed to ping replica: %w", err)
		}
	}

	return nil
}
