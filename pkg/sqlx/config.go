package sqlx

import (
	"fmt"
	"time"
)

const (
	defaultMaxOpenConn     = 6
	defaultMaxIdleConn     = 4
	defaultConnMaxLifetime = time.Hour
	defaultConnMaxIdleTime = 30 * time.Second
)

// Config for PostgreSQL.
type Config struct {
	Host            string
	Port            int
	User            string
	Password        string
	DBName          string
	MaxOpenConn     int
	MaxIdleConn     int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
	DriverName      string
}

func (c *Config) FormatDSN() string {
	switch c.DriverName {
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			c.Host, c.Port, c.User, c.Password, c.DBName)
	case "mysql":
		database := c.DBName
		if database == "" {
			database = "/"
		} else {
			database = "/" + database
		}

		return fmt.Sprintf("%s:%s@tcp(%s:%d)%s?charset=utf8mb4&parseTime=true",
			c.User,
			c.Password,
			c.Host,
			c.Port,
			database)
	default:
		return ""
	}
}

func NewConfig() *Config {
	return &Config{
		MaxOpenConn:     defaultMaxOpenConn,
		MaxIdleConn:     defaultMaxIdleConn,
		ConnMaxLifetime: defaultConnMaxLifetime,
		ConnMaxIdleTime: defaultConnMaxIdleTime,
	}
}

func (c *Config) Clone() *Config {
	return &Config{
		User:            c.User,
		Password:        c.Password,
		DBName:          c.DBName,
		Host:            c.Host,
		Port:            c.Port,
		MaxOpenConn:     c.MaxOpenConn,
		MaxIdleConn:     c.MaxIdleConn,
		ConnMaxLifetime: c.ConnMaxLifetime,
		ConnMaxIdleTime: c.ConnMaxIdleTime,
	}
}
