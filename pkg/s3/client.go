package s3

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// Client represents an AWS S3 client
type Client struct {
	s3Client *s3.Client
	region   string
}

// ClientConfig contains configuration for S3 client
type ClientConfig struct {
	Region   string
	Endpoint string // Optional, for local testing with LocalStack
}

// NewClient creates a new AWS S3 client
func NewClient(ctx context.Context, cfg *ClientConfig) (*Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("client config cannot be nil")
	}

	awsOpts := []func(*config.LoadOptions) error{
		config.WithRegion(cfg.Region),
	}

	// If endpoint is specified, use it (for local testing with LocalStack)
	if cfg.Endpoint != "" {
		customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
			return aws.Endpoint{
				URL:               cfg.Endpoint,
				HostnameImmutable: true,
			}, nil
		})
		awsOpts = append(awsOpts, config.WithEndpointResolverWithOptions(customResolver))
	}

	awsCfg, err := config.LoadDefaultConfig(ctx, awsOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return &Client{
		s3Client: s3.NewFromConfig(awsCfg),
		region:   cfg.Region,
	}, nil
}

// UploadInput contains parameters for uploading files to S3
type UploadInput struct {
	Bucket      string
	Key         string
	Body        io.Reader
	ContentType string
	ACL         types.ObjectCannedACL
	Metadata    map[string]string
}

// UploadFile uploads a file to S3
func (c *Client) UploadFile(ctx context.Context, input *UploadInput) error {
	if input == nil {
		return fmt.Errorf("upload input cannot be nil")
	}

	putInput := &s3.PutObjectInput{
		Bucket:      aws.String(input.Bucket),
		Key:         aws.String(input.Key),
		Body:        input.Body,
		ContentType: aws.String(input.ContentType),
	}

	if input.ACL != "" {
		putInput.ACL = input.ACL
	}

	if input.Metadata != nil {
		putInput.Metadata = input.Metadata
	}

	_, err := c.s3Client.PutObject(ctx, putInput)
	if err != nil {
		return fmt.Errorf("failed to upload file to s3://%s/%s: %w", input.Bucket, input.Key, err)
	}

	return nil
}

// DownloadInput contains parameters for downloading files from S3
type DownloadInput struct {
	Bucket string
	Key    string
}

// DownloadFile downloads a file from S3
func (c *Client) DownloadFile(ctx context.Context, input *DownloadInput) (io.ReadCloser, error) {
	if input == nil {
		return nil, fmt.Errorf("download input cannot be nil")
	}

	getInput := &s3.GetObjectInput{
		Bucket: aws.String(input.Bucket),
		Key:    aws.String(input.Key),
	}

	result, err := c.s3Client.GetObject(ctx, getInput)
	if err != nil {
		return nil, fmt.Errorf("failed to download file from s3://%s/%s: %w", input.Bucket, input.Key, err)
	}

	return result.Body, nil
}

// ListObjectsInput contains parameters for listing objects in S3
type ListObjectsInput struct {
	Bucket string
	Prefix string
}

// ListObjectsOutput contains the results of listing objects
type ListObjectsOutput struct {
	Objects []*ObjectMetadata
}

// ObjectMetadata contains metadata about an S3 object
type ObjectMetadata struct {
	Key          string
	LastModified time.Time
	Size         int64
	ETag         string
}

// ListObjects lists objects in an S3 bucket
func (c *Client) ListObjects(ctx context.Context, input *ListObjectsInput) (*ListObjectsOutput, error) {
	if input == nil {
		return nil, fmt.Errorf("list objects input cannot be nil")
	}

	listInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(input.Bucket),
		Prefix: aws.String(input.Prefix),
	}

	result, err := c.s3Client.ListObjectsV2(ctx, listInput)
	if err != nil {
		return nil, fmt.Errorf("failed to list objects in s3://%s/%s: %w", input.Bucket, input.Prefix, err)
	}

	output := &ListObjectsOutput{
		Objects: make([]*ObjectMetadata, 0, len(result.Contents)),
	}

	for _, obj := range result.Contents {
		output.Objects = append(output.Objects, &ObjectMetadata{
			Key:          aws.ToString(obj.Key),
			LastModified: aws.ToTime(obj.LastModified),
			Size:         aws.ToInt64(obj.Size),
			ETag:         aws.ToString(obj.ETag),
		})
	}

	return output, nil
}

// DeleteObjectInput contains parameters for deleting an object from S3
type DeleteObjectInput struct {
	Bucket string
	Key    string
}

// DeleteObject deletes an object from S3
func (c *Client) DeleteObject(ctx context.Context, input *DeleteObjectInput) error {
	if input == nil {
		return fmt.Errorf("delete object input cannot be nil")
	}

	deleteInput := &s3.DeleteObjectInput{
		Bucket: aws.String(input.Bucket),
		Key:    aws.String(input.Key),
	}

	_, err := c.s3Client.DeleteObject(ctx, deleteInput)
	if err != nil {
		return fmt.Errorf("failed to delete object s3://%s/%s: %w", input.Bucket, input.Key, err)
	}

	return nil
}
