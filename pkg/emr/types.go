package emr

import "time"

type SparkConfig struct {
	Schema  string `json:"schema"`
	Catalog string `json:"catalog"`
}

type ClusterConfig struct {
	Name                        string
	LogURI                      string
	ReleaseLabel                string
	Applications                []string
	InstanceType                string
	InstanceCount               int32
	InstanceFleets              []InstanceFleetConfig
	InstanceGroups              bool
	ServiceRole                 string
	JobFlowRole                 string
	KeepJobFlowAliveWhenNoSteps bool
	VisibleToAllUsers           bool
	StepConcurrencyLevel        int32
	EC2SubnetIDs                []string
	EC2KeyName                  string
	EmrManagedMasterSG          string
	EmrManagedSlaveSG           string
	ServiceAccessSG             string
	BootstrapActions            []BootstrapAction
	Tags                        map[string]string
}

type SparkJobConfig struct {
	Name                string
	MainClass           string
	JarURI              string
	Args                []string
	Properties          map[string]string
	DeployMode          string
	Master              string
	DriverMemory        string
	DriverMaxResultSize string
	ExecutorCores       int32
	ExecutorMemory      string
	ExecutorInstances   int32
	AdditionalJars      []string
	AdditionalConfigs   map[string]string
}

// EMRStatus represents the status of EMR clusters and steps
type EMRStatus struct {
	State       string
	StateReason string
	Timeline    Timeline
}

// Timeline represents time-related data for EMR resources
type Timeline struct {
	CreationDateTime *time.Time
	StartDateTime    *time.Time
	EndDateTime      *time.Time
	ReadyDateTime    *time.Time
}

// InstanceFleetConfig defines the configuration for an instance fleet
type InstanceFleetConfig struct {
	Name                   string
	InstanceFleetType      string // "MASTER" or "CORE" or "TASK"
	TargetOnDemandCapacity int32
	TargetSpotCapacity     int32
	InstanceTypeConfigs    []InstanceTypeConfig
	OnDemandSpecification  *OnDemandSpecification
	SpotSpecification      *SpotSpecification
}

// InstanceTypeConfig defines the configuration for an instance type
type InstanceTypeConfig struct {
	InstanceType     string
	WeightedCapacity int32
	EBSConfiguration *EBSConfiguration
}

// EBSConfiguration defines the EBS configuration for instances
type EBSConfiguration struct {
	EBSBlockDeviceConfigs []EBSBlockDeviceConfig
	EBSOptimized          bool
}

// EBSBlockDeviceConfig defines the EBS block device configuration
type EBSBlockDeviceConfig struct {
	VolumeSpecification VolumeSpecification
	VolumesPerInstance  int32
}

// VolumeSpecification defines the specification for an EBS volume
type VolumeSpecification struct {
	SizeInGB   int32
	VolumeType string
}

// OnDemandSpecification defines the on-demand instance allocation strategy
type OnDemandSpecification struct {
	AllocationStrategy string
}

// SpotSpecification defines the spot instance allocation strategy
type SpotSpecification struct {
	TimeoutDurationMinutes int32
	TimeoutAction          string
	AllocationStrategy     string
}

// BootstrapAction defines a bootstrap action for the EMR cluster
type BootstrapAction struct {
	Name       string
	ScriptPath string
	ScriptArgs []string
}

// Add these constants for cluster states
const (
	StateStarting             = "STARTING"
	StateBootstrapping        = "BOOTSTRAPPING"
	StateRunning              = "RUNNING"
	StateWaiting              = "WAITING"
	StateTerminating          = "TERMINATING"
	StateTerminated           = "TERMINATED"
	StateTerminatedWithErrors = "TERMINATED_WITH_ERRORS"
)
