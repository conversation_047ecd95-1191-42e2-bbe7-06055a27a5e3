package emr

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/emr"
)

// Client represents an AWS EMR client with operations for managing EMR clusters and jobs
type Client struct {
	emrClient *emr.Client
	region    string
}

// ClientConfig contains configuration for EMR client
type ClientConfig struct {
	Region   string
	Endpoint string
}

// NewClient creates a new AWS EMR client
func NewClient(ctx context.Context, cfg *ClientConfig) (*Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("client config cannot be nil")
	}

	awsOpts := []func(*config.LoadOptions) error{
		config.WithRegion(cfg.Region),
	}

	// If endpoint is specified, use it (for local testing with LocalStack)
	if cfg.Endpoint != "" {
		customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
			return aws.Endpoint{
				URL:               cfg.Endpoint,
				HostnameImmutable: true,
			}, nil
		})
		awsOpts = append(awsOpts, config.WithEndpointResolverWithOptions(customResolver))
	}

	awsCfg, err := config.LoadDefaultConfig(ctx, awsOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return &Client{
		emrClient: emr.NewFromConfig(awsCfg),
		region:    cfg.Region,
	}, nil
}
