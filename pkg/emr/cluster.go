package emr

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/emr"
	"github.com/aws/aws-sdk-go-v2/service/emr/types"
)

// CreateCluster creates a new EMR cluster with the specified configuration
func (c *Client) CreateCluster(ctx context.Context, config ClusterConfig) (string, error) {
	// Convert application names to EMR applications
	applications := make([]types.Application, 0, len(config.Applications))
	for _, app := range config.Applications {
		applications = append(applications, types.Application{
			Name: aws.String(app),
		})
	}

	// Convert tags to EMR tags
	tags := make([]types.Tag, 0, len(config.Tags))
	for k, v := range config.Tags {
		tags = append(tags, types.Tag{
			Key:   aws.String(k),
			Value: aws.String(v),
		})
	}

	// Create the cluster input
	input := &emr.RunJobFlowInput{
		Name:                 aws.String(config.Name),
		LogUri:               aws.String(config.LogURI),
		ReleaseLabel:         aws.String(config.ReleaseLabel),
		Applications:         applications,
		ServiceRole:          aws.String(config.ServiceRole),
		JobFlowRole:          aws.String(config.JobFlowRole),
		Tags:                 tags,
		VisibleToAllUsers:    aws.Bool(config.VisibleToAllUsers),
		StepConcurrencyLevel: aws.Int32(config.StepConcurrencyLevel),
		Instances: &types.JobFlowInstancesConfig{
			KeepJobFlowAliveWhenNoSteps: aws.Bool(config.KeepJobFlowAliveWhenNoSteps),
			InstanceCount:               aws.Int32(config.InstanceCount),
		},
	}

	// Add EC2 subnet IDs if specified
	if len(config.EC2SubnetIDs) > 0 {
		input.Instances.Ec2SubnetIds = config.EC2SubnetIDs
	}

	// Add EC2 key name if specified
	if config.EC2KeyName != "" {
		input.Instances.Ec2KeyName = aws.String(config.EC2KeyName)
	}

	// Add security groups if specified
	if config.EmrManagedMasterSG != "" {
		input.Instances.EmrManagedMasterSecurityGroup = aws.String(config.EmrManagedMasterSG)
	}

	if config.EmrManagedSlaveSG != "" {
		input.Instances.EmrManagedSlaveSecurityGroup = aws.String(config.EmrManagedSlaveSG)
	}

	if config.ServiceAccessSG != "" {
		input.Instances.ServiceAccessSecurityGroup = aws.String(config.ServiceAccessSG)
	}

	// Configure instance fleets or instance groups based on the configuration
	if !config.InstanceGroups {
		// Use instance fleets
		if len(config.InstanceFleets) > 0 {
			instanceFleets := make([]types.InstanceFleetConfig, 0, len(config.InstanceFleets))

			for _, fleet := range config.InstanceFleets {
				// Convert instance type configs
				instanceTypeConfigs := make([]types.InstanceTypeConfig, 0, len(fleet.InstanceTypeConfigs))

				for _, itc := range fleet.InstanceTypeConfigs {
					var ebsConfig *types.EbsConfiguration

					if itc.EBSConfiguration != nil {
						ebsBlockDeviceConfigs := make([]types.EbsBlockDeviceConfig, 0, len(itc.EBSConfiguration.EBSBlockDeviceConfigs))

						for _, ebsBlockConfig := range itc.EBSConfiguration.EBSBlockDeviceConfigs {
							ebsBlockDeviceConfigs = append(ebsBlockDeviceConfigs, types.EbsBlockDeviceConfig{
								VolumeSpecification: &types.VolumeSpecification{
									SizeInGB:   aws.Int32(ebsBlockConfig.VolumeSpecification.SizeInGB),
									VolumeType: aws.String(ebsBlockConfig.VolumeSpecification.VolumeType),
								},
								VolumesPerInstance: aws.Int32(ebsBlockConfig.VolumesPerInstance),
							})
						}

						ebsConfig = &types.EbsConfiguration{
							EbsBlockDeviceConfigs: ebsBlockDeviceConfigs,
							EbsOptimized:          aws.Bool(itc.EBSConfiguration.EBSOptimized),
						}
					}

					instanceTypeConfig := types.InstanceTypeConfig{
						InstanceType:     aws.String(itc.InstanceType),
						EbsConfiguration: ebsConfig,
					}

					if itc.WeightedCapacity > 0 {
						instanceTypeConfig.WeightedCapacity = aws.Int32(itc.WeightedCapacity)
					}

					instanceTypeConfigs = append(instanceTypeConfigs, instanceTypeConfig)
				}

				fleetConfig := types.InstanceFleetConfig{
					Name:                aws.String(fleet.Name),
					InstanceFleetType:   types.InstanceFleetType(fleet.InstanceFleetType),
					InstanceTypeConfigs: instanceTypeConfigs,
				}

				if fleet.TargetOnDemandCapacity > 0 {
					fleetConfig.TargetOnDemandCapacity = aws.Int32(fleet.TargetOnDemandCapacity)
				}

				if fleet.TargetSpotCapacity > 0 {
					fleetConfig.TargetSpotCapacity = aws.Int32(fleet.TargetSpotCapacity)
				}

				// Add on-demand specifications if provided
				if fleet.OnDemandSpecification != nil {
					fleetConfig.LaunchSpecifications = &types.InstanceFleetProvisioningSpecifications{
						OnDemandSpecification: &types.OnDemandProvisioningSpecification{
							AllocationStrategy: types.OnDemandProvisioningAllocationStrategy(fleet.OnDemandSpecification.AllocationStrategy),
						},
					}
				}

				// Add spot specifications if provided
				if fleet.SpotSpecification != nil {
					// Only create LaunchSpecifications if it doesn't exist yet
					if fleetConfig.LaunchSpecifications == nil {
						fleetConfig.LaunchSpecifications = &types.InstanceFleetProvisioningSpecifications{}
					}

					fleetConfig.LaunchSpecifications.SpotSpecification = &types.SpotProvisioningSpecification{
						TimeoutDurationMinutes: aws.Int32(fleet.SpotSpecification.TimeoutDurationMinutes),
						TimeoutAction:          types.SpotProvisioningTimeoutAction(fleet.SpotSpecification.TimeoutAction),
						AllocationStrategy:     types.SpotProvisioningAllocationStrategy(fleet.SpotSpecification.AllocationStrategy),
					}
				}

				instanceFleets = append(instanceFleets, fleetConfig)
			}

			input.Instances.InstanceFleets = instanceFleets
		}
	} else {
		// Use instance groups (backward compatibility)
		input.Instances.InstanceGroups = []types.InstanceGroupConfig{
			{
				InstanceCount: aws.Int32(config.InstanceCount),
				InstanceType:  aws.String(config.InstanceType),
				Market:        types.MarketTypeOnDemand,
				Name:          aws.String("Master"),
				InstanceRole:  types.InstanceRoleTypeMaster,
			},
			{
				InstanceCount: aws.Int32(config.InstanceCount),
				InstanceType:  aws.String(config.InstanceType),
				Market:        types.MarketTypeOnDemand,
				Name:          aws.String("Core"),
				InstanceRole:  types.InstanceRoleTypeCore,
			},
		}
	}

	// Add bootstrap actions if specified
	if len(config.BootstrapActions) > 0 {
		bootstrapActions := make([]types.BootstrapActionConfig, 0, len(config.BootstrapActions))

		for _, action := range config.BootstrapActions {
			bootstrapAction := types.BootstrapActionConfig{
				Name: aws.String(action.Name),
				ScriptBootstrapAction: &types.ScriptBootstrapActionConfig{
					Path: aws.String(action.ScriptPath),
				},
			}

			if len(action.ScriptArgs) > 0 {
				bootstrapAction.ScriptBootstrapAction.Args = action.ScriptArgs
			}

			bootstrapActions = append(bootstrapActions, bootstrapAction)
		}

		input.BootstrapActions = bootstrapActions
	}

	// Create the cluster
	response, err := c.emrClient.RunJobFlow(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to create EMR cluster: %w", err)
	}

	return *response.JobFlowId, nil
}

// TerminateCluster terminates an EMR cluster
func (c *Client) TerminateCluster(ctx context.Context, clusterID string) error {
	input := &emr.TerminateJobFlowsInput{
		JobFlowIds: []string{clusterID},
	}

	_, err := c.emrClient.TerminateJobFlows(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to terminate cluster %s: %w", clusterID, err)
	}

	return nil
}

// GetClusterStatus gets the status of an EMR cluster
func (c *Client) GetClusterStatus(ctx context.Context, clusterID string) (*EMRStatus, error) {
	input := &emr.DescribeClusterInput{
		ClusterId: aws.String(clusterID),
	}

	response, err := c.emrClient.DescribeCluster(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster status for %s: %w", clusterID, err)
	}

	status := &EMRStatus{
		State: string(response.Cluster.Status.State),
		Timeline: Timeline{
			CreationDateTime: response.Cluster.Status.Timeline.CreationDateTime,
			StartDateTime:    response.Cluster.Status.Timeline.ReadyDateTime,
			EndDateTime:      response.Cluster.Status.Timeline.EndDateTime,
		},
	}

	if response.Cluster.Status.StateChangeReason != nil {
		status.StateReason = aws.ToString(response.Cluster.Status.StateChangeReason.Message)
	}

	return status, nil
}

// WaitForClusterStatus waits for a cluster to reach a specific status with optional timeout
func (c *Client) WaitForClusterStatus(ctx context.Context, clusterID string, targetState string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		status, err := c.GetClusterStatus(ctx, clusterID)
		if err != nil {
			return err
		}

		// Check if target state reached
		if status.State == targetState {
			return nil
		}

		// Check for terminal states that aren't the target
		if (status.State == StateTerminated || status.State == StateTerminatedWithErrors) && targetState != status.State {
			return fmt.Errorf("cluster reached terminal state %s: %s", status.State, status.StateReason)
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(30 * time.Second):
			// Continue polling
		}
	}

	return fmt.Errorf("timeout waiting for cluster %s to reach state %s", clusterID, targetState)
}

// ListClusters lists all EMR clusters filtered by optional states
func (c *Client) ListClusters(ctx context.Context, states []string) ([]string, error) {
	var clusterStates []types.ClusterState
	for _, state := range states {
		clusterStates = append(clusterStates, types.ClusterState(state))
	}

	input := &emr.ListClustersInput{
		ClusterStates: clusterStates,
	}

	response, err := c.emrClient.ListClusters(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to list EMR clusters: %w", err)
	}

	var clusterIDs []string
	for _, cluster := range response.Clusters {
		clusterIDs = append(clusterIDs, *cluster.Id)
	}

	return clusterIDs, nil
}
