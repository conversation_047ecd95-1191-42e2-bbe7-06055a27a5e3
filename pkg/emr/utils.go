package emr

// DefaultSparkClusterConfig returns a default configuration for a Spark cluster
func DefaultSparkClusterConfig(config *Config, name string, instanceType string, instanceCount int32) ClusterConfig {
	if config == nil {
		config = DefaultConfig()
	}

	return ClusterConfig{
		Name:                        name,
		LogURI:                      config.LogURI,
		ReleaseLabel:                config.DefaultReleaseLabel,
		Applications:                []string{"Spark", "Hadoop", "Hive"},
		InstanceType:                instanceType,
		InstanceCount:               instanceCount,
		InstanceGroups:              true, // Use instance groups by default for backward compatibility
		ServiceRole:                 config.ServiceRole,
		JobFlowRole:                 config.JobFlowRole,
		KeepJobFlowAliveWhenNoSteps: true,
		VisibleToAllUsers:           true,
		StepConcurrencyLevel:        1,
		Tags: map[string]string{
			"Name":    name,
			"Service": "cdp-platform",
			"Created": "temporal-workflow",
		},
	}
}

// DefaultInstanceFleetClusterConfig returns a configuration for an EMR cluster with instance fleets
func DefaultInstanceFleetClusterConfig(config *Config, name string) ClusterConfig {
	if config == nil {
		config = DefaultConfig()
	}

	return ClusterConfig{
		Name:                        name,
		LogURI:                      config.LogURI,
		ReleaseLabel:                config.DefaultReleaseLabel,
		Applications:                []string{"Spark", "Hadoop", "Hive"},
		InstanceGroups:              false, // Use instance fleets
		ServiceRole:                 config.ServiceRole,
		JobFlowRole:                 config.JobFlowRole,
		KeepJobFlowAliveWhenNoSteps: true,
		VisibleToAllUsers:           true,
		StepConcurrencyLevel:        1,
		Tags: map[string]string{
			"Name":    name,
			"Service": "cdp-platform",
			"Created": "temporal-workflow",
		},
	}
}

// BuildSparkJobConfig creates a Spark job configuration with the given parameters
func BuildSparkJobConfig(name, jarURI, mainClass string, args []string, props map[string]string) SparkJobConfig {
	return SparkJobConfig{
		Name:       name,
		JarURI:     jarURI,
		MainClass:  mainClass,
		Args:       args,
		Properties: props,
		DeployMode: "cluster",
		Master:     "yarn",
	}
}

// BuildAdvancedSparkJobConfig creates a more detailed Spark job configuration
func BuildAdvancedSparkJobConfig(
	name string,
	jarURI string,
	mainClass string,
	args []string,
	props map[string]string,
	deployMode string,
	master string,
	driverMemory string,
	executorCores int32,
	executorMemory string,
	executorInstances int32,
	additionalJars []string,
	additionalConfigs map[string]string,
) SparkJobConfig {
	return SparkJobConfig{
		Name:              name,
		JarURI:            jarURI,
		MainClass:         mainClass,
		Args:              args,
		Properties:        props,
		DeployMode:        deployMode,
		Master:            master,
		DriverMemory:      driverMemory,
		ExecutorCores:     executorCores,
		ExecutorMemory:    executorMemory,
		ExecutorInstances: executorInstances,
		AdditionalJars:    additionalJars,
		AdditionalConfigs: additionalConfigs,
	}
}
