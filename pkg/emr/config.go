package emr

// Config represents EMR configuration that can be loaded from the application config
type Config struct {
	Region              string `yaml:"region"`
	ServiceRole         string `yaml:"service_role"`
	JobFlowRole         string `yaml:"job_flow_role"`
	LogURI              string `yaml:"log_uri"`
	DefaultReleaseLabel string `yaml:"default_release_label"`
	Endpoint            string `yaml:"endpoint"`
}

// DefaultConfig returns a default configuration for EMR
func DefaultConfig() *Config {
	return &Config{
		Region:              "ap-south-1",
		ServiceRole:         "EMR_DefaultRole",
		JobFlowRole:         "EMR_EC2_DefaultRole",
		LogURI:              "s3://aws-logs-{account-id}-{region}/elasticmapreduce/",
		DefaultReleaseLabel: "emr-6.10.0",
		// Endpoint is empty by default, only set for local testing
	}
}
