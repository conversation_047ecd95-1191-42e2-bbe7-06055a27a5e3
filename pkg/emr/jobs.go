package emr

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Zomato/go/logger"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/emr"
	"github.com/aws/aws-sdk-go-v2/service/emr/types"
)

func (c *Client) AddSparkJobStep(ctx context.Context, clusterID string, config SparkJobConfig) (string, error) {

	sparkSubmitArgs := []string{"spark-submit"}

	if config.DeployMode != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--deploy-mode", config.DeployMode)
	}

	if config.Master != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--master", config.Master)
	}

	if config.DriverMemory != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", "spark.driver.memory="+config.DriverMemory)
	}

	if config.DriverMaxResultSize != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", "spark.driver.maxResultSize="+config.DriverMaxResultSize)
	}

	if config.ExecutorCores > 0 {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", fmt.Sprintf("spark.executor.cores=%d", config.ExecutorCores))
	}

	if config.ExecutorMemory != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", "spark.executor.memory="+config.ExecutorMemory)
	}

	if config.ExecutorInstances > 0 {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", fmt.Sprintf("spark.executor.instances=%d", config.ExecutorInstances))
	}

	for k, v := range config.AdditionalConfigs {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", fmt.Sprintf("%s=%s", k, v))
	}

	for k, v := range config.Properties {
		sparkSubmitArgs = append(sparkSubmitArgs, "--conf", fmt.Sprintf("%s=%s", k, v))
	}

	if len(config.AdditionalJars) > 0 {
		sparkSubmitArgs = append(sparkSubmitArgs, "--jars", strings.Join(config.AdditionalJars, ","))
	}

	if config.MainClass != "" {
		sparkSubmitArgs = append(sparkSubmitArgs, "--class", config.MainClass)
	}

	sparkSubmitArgs = append(sparkSubmitArgs, config.JarURI)

	sparkSubmitArgs = append(sparkSubmitArgs, config.Args...)
	logger.Infof("Spark submit args: %v", sparkSubmitArgs)

	step := types.StepConfig{
		Name:            aws.String(config.Name),
		ActionOnFailure: types.ActionOnFailureContinue,
		HadoopJarStep: &types.HadoopJarStepConfig{
			Jar:  aws.String("command-runner.jar"),
			Args: sparkSubmitArgs,
		},
	}

	input := &emr.AddJobFlowStepsInput{
		JobFlowId: aws.String(clusterID),
		Steps:     []types.StepConfig{step},
	}

	response, err := c.emrClient.AddJobFlowSteps(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to add step to cluster %s: %w", clusterID, err)
	}

	return aws.ToString(&response.StepIds[0]), nil
}

func (c *Client) GetStepStatus(ctx context.Context, clusterID, stepID string) (*EMRStatus, error) {
	input := &emr.DescribeStepInput{
		ClusterId: aws.String(clusterID),
		StepId:    aws.String(stepID),
	}

	response, err := c.emrClient.DescribeStep(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to get step status for cluster %s step %s: %w", clusterID, stepID, err)
	}
	logger.Infof("StepStatus: State=%v, StateChangeReason=%+v, Timeline=%+v, FailureDetails=%+v",
		response.Step.Status.State,
		response.Step.Status.StateChangeReason,
		response.Step.Status.Timeline,
		response.Step.Status.FailureDetails,
	)

	status := &EMRStatus{
		State: string(response.Step.Status.State),
		Timeline: Timeline{
			CreationDateTime: response.Step.Status.Timeline.CreationDateTime,
			StartDateTime:    response.Step.Status.Timeline.StartDateTime,
			EndDateTime:      response.Step.Status.Timeline.EndDateTime,
		},
	}

	if response.Step.Status.StateChangeReason != nil {
		status.StateReason = aws.ToString(response.Step.Status.StateChangeReason.Message)
	}

	return status, nil
}

func (c *Client) WaitForStepCompletion(ctx context.Context, clusterID, stepID string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		status, err := c.GetStepStatus(ctx, clusterID, stepID)
		logger.Infof("EMR step %s state: %s", stepID, status.State)
		if err != nil {
			return err
		}

		if status.State == string(types.StepStateCompleted) {
			return nil
		}

		if status.State == string(types.StepStateFailed) || status.State == string(types.StepStateCancelled) {
			return fmt.Errorf("step failed: %s", status.StateReason)
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(30 * time.Second):

		}
	}

	return fmt.Errorf("timeout waiting for step %s in cluster %s to complete", stepID, clusterID)
}

func (c *Client) ListSteps(ctx context.Context, clusterID string, states []string) ([]string, error) {
	var stepStates []types.StepState
	for _, state := range states {
		stepStates = append(stepStates, types.StepState(state))
	}

	input := &emr.ListStepsInput{
		ClusterId:  aws.String(clusterID),
		StepStates: stepStates,
	}

	response, err := c.emrClient.ListSteps(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to list steps for cluster %s: %w", clusterID, err)
	}

	var stepIDs []string
	for _, step := range response.Steps {
		stepIDs = append(stepIDs, *step.Id)
	}

	return stepIDs, nil
}

func (c *Client) CancelStep(ctx context.Context, clusterID, stepID string) error {
	input := &emr.CancelStepsInput{
		ClusterId: aws.String(clusterID),
		StepIds:   []string{stepID},
	}

	response, err := c.emrClient.CancelSteps(ctx, input)
	if err != nil {
		return fmt.Errorf("failed to cancel step %s in cluster %s: %w", stepID, clusterID, err)
	}

	for _, result := range response.CancelStepsInfoList {
		if result.Status == types.CancelStepsRequestStatusFailed {
			return fmt.Errorf("failed to cancel step %s: %s", stepID, *result.Reason)
		}
	}

	return nil
}
