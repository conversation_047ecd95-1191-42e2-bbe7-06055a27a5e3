package appsflyer

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/Zomato/cdp-platform/dtos"
	"github.com/Zomato/go/logger"
)

const (
	baseURL          = "https://hq1.appsflyer.com"
	maxDevicesPerReq = 12000      // 12000 devices
	maxRequestSize   = 800 * 1024 // 800 KB

	// Device identifier columns for queries
	DeviceIdAttributeColumnName  = "advertising_id"
	PhoneAttributeColumnName     = "phone"
	EmailAttributeColumnName     = "email"
	PhoneE164AttributeColumnName = "phone_plus_91"
	PlatformAttributeColumnName  = "trx_recent_device_platform"
)

// SelectColumns returns the columns needed for AppsFlyer device identification
func SelectColumns() []string {
	return []string{DeviceIdAttributeColumnName, PhoneAttributeColumnName, EmailAttributeColumnName, PhoneE164AttributeColumnName, PlatformAttributeColumnName}
}

// GetSelectClause returns the SELECT clause for AppsFlyer queries
// If prefix is not empty, it will be appended to each column name
func GetSelectClause(prefix string) string {
	columns := SelectColumns()

	// Apply prefix if provided
	if prefix != "" {
		prefixedColumns := make([]string, len(columns))
		for i, col := range columns {
			prefixedColumns[i] = prefix + "." + col
		}
		return strings.Join(prefixedColumns, ", ")
	}

	return strings.Join(columns, ", ")
}

// Client represents an AppsFlyer API client
type Client struct {
	httpClient *http.Client
	token      string
}

// Device represents a single device in the audience
type Device struct {
	IDFA      string `json:"idfa,omitempty"`
	GAID      string `json:"gaid,omitempty"`
	Phone     string `json:"phone,omitempty"`
	Email     string `json:"emails,omitempty"`
	PhoneE164 string `json:"phone_e164,omitempty"`
	AppID     string `json:"app_id"`
}

// AudienceRequest represents the request to create/update an audience
type AudienceRequest struct {
	ImportKey string   `json:"import_key"`
	Platform  string   `json:"platform"`
	Devices   []Device `json:"devices"`
	Operation string   `json:"operation"`
}

// CreateAudienceRequest represents the request to create a new audience
type CreateAudienceRequest struct {
	AudienceName                   string `json:"audience_name"`
	Platform                       string `json:"platform"`
	AppID                          string `json:"app_id"`
	IgnoreAccountIdentifiersPolicy bool   `json:"ignore_account_identifiers_policy"`
}

// CreateAudienceResponse represents the response from creating an audience
type CreateAudienceResponse struct {
	Message struct {
		AudienceID int64  `json:"audience_id"`
		ImportKey  string `json:"import_key"`
	} `json:"message"`
}

// NewClient creates a new AppsFlyer client
func NewClient(token string) (*Client, error) {
	return &Client{
		httpClient: &http.Client{},
		token:      token,
	}, nil
}

type ActiveAudienceResponse struct {
	Message struct {
		Results []struct {
			AudienceID   int64  `json:"audience_id"`
			Name         string `json:"name"`
			UpdatingUser string `json:"updating_user"`
		} `json:"results"`
	} `json:"message"`
}

func (c *Client) GetActiveAudiences(ctx context.Context) (*ActiveAudienceResponse, error) {
	url := fmt.Sprintf("%s/api/audiences-external-api/active-audiences", baseURL)

	request, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(request)

	response, err := c.httpClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer response.Body.Close()

	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if response.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", response.StatusCode, string(respBody))
	}

	var result ActiveAudienceResponse
	if err := json.NewDecoder(bytes.NewReader(respBody)).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// CreateAudience creates a new audience and returns the import key
func (c *Client) CreateAudience(ctx context.Context, name, platform, appID string) (*CreateAudienceResponse, error) {
	url := fmt.Sprintf("%s/api/audiences-external-api/audience", baseURL)

	req := CreateAudienceRequest{
		AudienceName:                   name,
		Platform:                       platform,
		AppID:                          appID,
		IgnoreAccountIdentifiersPolicy: true,
	}

	// Log request payload
	reqJSON, _ := json.MarshalIndent(req, "", "  ")
	logger.Infof("AppsFlyer CreateAudience Request: %s", string(reqJSON))

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(request)

	// Log request headers
	// logger.Infof("Request Headers: %v", request.Header)

	resp, err := c.httpClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log response details
	// logger.Infof("AppsFlyer Response Status: %d", resp.StatusCode)
	// logger.Infof("AppsFlyer Response Body: %s", string(respBody))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, response: %s", resp.StatusCode, string(respBody))
	}

	var response CreateAudienceResponse
	if err := json.NewDecoder(bytes.NewReader(respBody)).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// ProcessAudienceRequest sends the audience update request to AppsFlyer
func (c *Client) ProcessAudienceRequest(ctx context.Context, request *AudienceRequest, operation string) error {
	logger.Infof("Starting ProcessAudienceRequest for operation: %s", operation)

	if err := c.validateRequest(request, operation); err != nil {
		return fmt.Errorf("invalid request: %w", err)
	}

	url := fmt.Sprintf("%s/api/audiences-import-api/v2/%s", baseURL, operation)
	// logger.Infof("API URL: %s", url)

	body, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}
	// logger.Infof("Request Body: %s", string(body))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body for error reporting
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}
	// logger.Infof("Response Status: %d", resp.StatusCode)
	// logger.Infof("Response Body: %s", string(respBody))

	if resp.StatusCode != http.StatusOK {
		logger.Infof("[FAILED] Response Status: %d", resp.StatusCode)
		logger.Infof("[FAILED] Response Body: %s", string(respBody))
		return fmt.Errorf("unexpected status code: %d, response: %s", resp.StatusCode, string(respBody))
	}

	// logger.Infof("Successfully processed audience request for operation: %s", operation)
	return nil
}

func (c *Client) validateRequest(request *AudienceRequest, operation string) error {
	if request == nil {
		return errors.New("request cannot be nil")
	}
	if request.ImportKey == "" {
		return errors.New("import key is required")
	}
	if request.Platform == "" {
		return errors.New("platform is required")
	}
	if len(request.Devices) == 0 {
		return errors.New("devices cannot be empty")
	}
	if operation != "add" && operation != "remove" {
		return fmt.Errorf("invalid operation: %s", operation)
	}
	return nil
}

func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Host", "hq1.appsflyer.com")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.token))
}

func SplitDevices(devices []Device) [][]Device {
	var chunks [][]Device
	var currentChunk []Device
	currentSize := 0

	// Helper function to calculate device JSON size
	calculateDeviceSize := func(device Device) (int, error) {
		bytes, err := json.Marshal(device)
		if err != nil {
			return 0, fmt.Errorf("failed to marshal device: %w", err)
		}
		return len(bytes), nil
	}

	// Calculate base request size with empty devices array
	baseRequest := AudienceRequest{
		ImportKey: "dummy",
		Platform:  "android",
		Devices:   []Device{},
	}
	baseBytes, _ := json.Marshal(baseRequest)
	baseSize := len(baseBytes)

	for _, device := range devices {
		// Calculate size of this device
		deviceSize, err := calculateDeviceSize(device)
		if err != nil {
			// Handle error appropriately
			continue
		}

		// Check if adding this device would exceed either limit
		newSize := currentSize + deviceSize
		if len(currentChunk) >= maxDevicesPerReq || (baseSize+newSize) >= maxRequestSize {
			// Current chunk is full, append it to chunks and start new chunk
			if len(currentChunk) > 0 {
				chunks = append(chunks, currentChunk)
				currentChunk = []Device{}
				currentSize = 0
			}
		}

		// Add device to current chunk
		currentChunk = append(currentChunk, device)
		currentSize += deviceSize
	}

	// Don't forget the last chunk
	if len(currentChunk) > 0 {
		chunks = append(chunks, currentChunk)
	}

	return chunks
}

// func validateChunkSize(importKey, platform string, devices []Device) error {
// 	req := AudienceRequest{
// 		ImportKey: importKey,
// 		Platform:  platform,
// 		Devices:   devices,
// 	}

// 	bytes, err := json.Marshal(req)
// 	if err != nil {
// 		return fmt.Errorf("failed to marshal request: %w", err)
// 	}

// 	if len(bytes) > maxRequestSize {
// 		return fmt.Errorf("request size %d exceeds maximum allowed size of %d bytes", len(bytes), maxRequestSize)
// 	}

// 	if len(devices) > maxDevicesPerReq {
// 		return fmt.Errorf("device count %d exceeds maximum allowed count of %d", len(devices), maxDevicesPerReq)
// 	}

// 	return nil
// }

// TransformRowToDevice converts a result row into an AppsFlyer Device
func TransformRowToDevice(result *dtos.Result, columnIndexes map[string]int, appID string, platform string) *Device {
	device := &Device{
		AppID: appID,
	}

	// Helper function to convert interface{} to string, handling byte slices
	toString := func(value interface{}) string {
		if value == nil {
			return ""
		}
		switch v := value.(type) {
		case []byte:
			return string(v)
		default:
			return fmt.Sprintf("%v", v)
		}
	}

	// Map device ID based on platform
	if idx, ok := columnIndexes[DeviceIdAttributeColumnName]; ok && result.Row.Columns[idx].Value != nil {
		deviceID := toString(result.Row.Columns[idx].Value)
		if deviceID != "" {
			// Set the appropriate field based on platform
			if platform == "ios" {
				device.IDFA = deviceID
			} else {
				device.GAID = deviceID
			}
		}
	}

	// Map other values to corresponding fields
	if idx, ok := columnIndexes[PhoneAttributeColumnName]; ok && result.Row.Columns[idx].Value != nil {
		device.Phone = toString(result.Row.Columns[idx].Value)
	}
	if idx, ok := columnIndexes[EmailAttributeColumnName]; ok && result.Row.Columns[idx].Value != nil {
		device.Email = toString(result.Row.Columns[idx].Value)
	}
	if idx, ok := columnIndexes[PhoneE164AttributeColumnName]; ok && result.Row.Columns[idx].Value != nil {
		device.PhoneE164 = toString(result.Row.Columns[idx].Value)
	}

	// Return nil if device has no identifiers
	if device.IDFA == "" && device.GAID == "" && device.Phone == "" && device.Email == "" && device.PhoneE164 == "" {
		return nil
	}

	return device
}

// Close closes the AppsFlyer client and cleans up any resources
func (c *Client) Close() error {
	if c.httpClient != nil {
		c.httpClient.CloseIdleConnections()
	}
	return nil
}
