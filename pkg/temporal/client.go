package temporal

import (
	"context"
	"fmt"
	"log"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/worker"
)

type Client struct {
	config *Config
	Client client.Client
}

func NewClient(cfg *Config) (*Client, error) {
	if cfg == nil {
		return nil, errors.New("invalid config")
	}

	c, err := client.Dial(client.Options{
		HostPort: cfg.Address(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Temporal client: %w", err)
	}

	return &Client{
		config: cfg,
		Client: c,
	}, nil
}

func (c *Client) Close() {
	if c.Client != nil {
		c.Client.Close()
	}
}

func (c *Client) NewWorker(taskQueue string) worker.Worker {
	return worker.New(c.Client, taskQueue, worker.Options{})
}

// RunWorker starts the worker
func (c *Client) RunWorker(w worker.Worker) error {
	err := w.Run(worker.InterruptCh())
	if err != nil {
		log.Fatalln("Unable to start worker", err)
		return err
	}
	return nil
}

// RegisterWorkflow registers a workflow with the worker
func (c *Client) RegisterWorkflow(w worker.Worker, workflow interface{}) {
	w.RegisterWorkflow(workflow)
}

// RegisterActivity registers an activity with the worker
func (c *Client) RegisterActivity(w worker.Worker, activity interface{}) {
	w.RegisterActivity(activity)
}

// ExecuteWorkflow starts a new workflow execution
func (c *Client) ExecuteWorkflow(ctx context.Context, workflowID string, taskQueue string, workflow interface{}, args ...interface{}) (client.WorkflowRun, error) {
	options := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: taskQueue,
	}
	return c.Client.ExecuteWorkflow(ctx, options, workflow, args...)
}

// ExecuteCronWorkflow starts a new cron workflow execution
func (c *Client) ExecuteCronWorkflow(ctx context.Context, workflowID string, taskQueue string, cron string, workflow interface{}, args ...interface{}) (client.WorkflowRun, error) {
	options := client.StartWorkflowOptions{
		ID:           workflowID,
		TaskQueue:    taskQueue,
		CronSchedule: cron,
	}
	return c.Client.ExecuteWorkflow(ctx, options, workflow, args...)
}

func (c *Client) ScheduleCronWorkflow(ctx context.Context, workflowID string, scheduleID string, taskQueue string, cron string, workflow interface{}, args ...interface{}) error {
	_, err := c.Client.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID: scheduleID,
		Spec: client.ScheduleSpec{
			CronExpressions: []string{cron},
		},
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowID,
			TaskQueue: taskQueue,
			Workflow:  workflow,
			Args:      args,
		},
	})

	return err
}

// SignalWorkflow sends a signal to a workflow execution
func (c *Client) SignalWorkflow(ctx context.Context, workflowID, runID, signalName string, arg interface{}) error {
	return c.Client.SignalWorkflow(ctx, workflowID, runID, signalName, arg)
}

// CancelWorkflow cancels a workflow execution
func (c *Client) CancelWorkflow(ctx context.Context, workflowID, runID string) error {
	return c.Client.CancelWorkflow(ctx, workflowID, runID)
}

// RegisterWorkerWithWorkflowsAndActivities registers a worker with multiple workflows and activities
func (c *Client) RegisterWorkerWithWorkflowsAndActivities(taskQueue string, workflows []interface{}, activities []interface{}) worker.Worker {
	w := c.NewWorker(taskQueue)
	for _, wf := range workflows {
		c.RegisterWorkflow(w, wf)
	}
	for _, act := range activities {
		c.RegisterActivity(w, act)
	}
	return w
}

// StartWorkflow starts a new workflow execution with more options
func (c *Client) StartWorkflow(ctx context.Context, options client.StartWorkflowOptions, workflowType string, args ...interface{}) (client.WorkflowRun, error) {
	return c.Client.ExecuteWorkflow(ctx, options, workflowType, args...)
}

// TerminateWorkflow terminates a workflow execution
func (c *Client) TerminateWorkflow(ctx context.Context, workflowID, runID, reason string, details ...interface{}) error {
	return c.Client.TerminateWorkflow(ctx, workflowID, runID, reason, details...)
}

// GetWorkflowResult waits for a workflow to complete and returns its result
func (c *Client) GetWorkflowResult(ctx context.Context, workflowID, runID string, valuePtr interface{}) error {
	run := c.Client.GetWorkflow(ctx, workflowID, runID)
	return run.Get(ctx, valuePtr)
}
