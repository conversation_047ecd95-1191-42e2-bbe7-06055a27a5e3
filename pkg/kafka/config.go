package kafka

import (
	"fmt"
)

// KafkaConfig holds the configuration for Kafka connections.
type KafkaConfig struct {
	Brokers          []string
	SecurityProtocol string
	SaslMechanism    string
	SaslUsername     string
	SaslPassword     string
}

// NewKafkaConfigWithPassword creates a Kafka configuration with SASL credentials.
func NewKafkaConfigWithPassword(brokers []string, securityProtocol, saslMechanism, saslUsername, saslPassword string) *KafkaConfig {
	return &KafkaConfig{
		Brokers:          brokers,
		SecurityProtocol: securityProtocol,
		SaslMechanism:    saslMechanism,
		SaslUsername:     saslUsername,
		SaslPassword:     saslPassword,
	}
}

// NewKafkaConfigWithoutPassword creates a Kafka configuration without SASL credentials.
func NewKafkaConfigWithoutPassword(brokers []string) *KafkaConfig {
	return &KafkaConfig{
		Brokers:          brokers,
		SecurityProtocol: "",
		SaslMechanism:    "",
		SaslUsername:     "",
		SaslPassword:     "",
	}
}

// Validate checks if the KafkaConfig contains all required fields.
func (c *KafkaConfig) Validate() error {
	if len(c.Brokers) == 0 {
		return fmt.Errorf("at least one broker must be specified")
	}

	// if c.SecurityProtocol == "" {
	// 	return fmt.Errorf("security protocol must be specified")
	// }

	if c.SaslMechanism != "" && (c.SaslUsername == "" || c.SaslPassword == "") {
		return fmt.Errorf("SASL credentials (username and password) must be specified for mechanism %s", c.SaslMechanism)
	}

	return nil
}
