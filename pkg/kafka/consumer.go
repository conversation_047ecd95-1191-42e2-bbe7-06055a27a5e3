package kafka

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"
)

type Consumer struct {
	Topic               string
	InitialOffset       int64
	ConsumerGroup       string
	ConsumerGroupClient sarama.ConsumerGroup
	Config              *KafkaConfig
	Handler             MessageHandler
}

type MessageHandler func(context.Context, *ConsumedMessage) error

// consumerGroupHandler implements sarama.ConsumerGroupHandler for a single topic.
type consumerGroupHandler struct {
	handler MessageHandler
}

// NewConsumer initializes a new Kafka consumer for a single topic.
func NewConsumer(cfg *KafkaConfig, consumerGroup, topic string, initialOffset int64, handler MessageHandler) (*Consumer, error) {
	// Validate Kafka configuration.
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid Kafka config: %w", err)
	}
	if topic == "" {
		return nil, fmt.Errorf("topic must be specified")
	}
	if consumerGroup == "" {
		return nil, fmt.Errorf("consumer group must be specified for consumers")
	}

	// Set up Sarama configuration.
	saramaConfig := sarama.NewConfig()
	saramaConfig.Consumer.Group.Rebalance.Strategy = sarama.NewBalanceStrategyRange()
	saramaConfig.Consumer.Offsets.Initial = initialOffset

	// Apply security configuration if provided.
	if cfg.SecurityProtocol != "" {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.User = cfg.SaslUsername
		saramaConfig.Net.SASL.Password = cfg.SaslPassword
		saramaConfig.Net.SASL.Mechanism = sarama.SASLMechanism(cfg.SaslMechanism)
	}

	// Create a consumer group.
	group, err := sarama.NewConsumerGroup(cfg.Brokers, consumerGroup, saramaConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create consumer group: %w", err)
	}

	return &Consumer{
		Topic:               topic,
		ConsumerGroup:       consumerGroup,
		ConsumerGroupClient: group,
		Config:              cfg,
		Handler:             handler,
	}, nil
}

// Start begins consuming messages for the configured topic.
func (c *Consumer) Start(ctx context.Context) error {
	handler := &consumerGroupHandler{handler: c.Handler}

	for {
		err := c.ConsumerGroupClient.Consume(ctx, []string{c.Topic}, handler)
		if ctx.Err() != nil {
			return ctx.Err() // Context cancellation or deadline exceeded.
		}
		if err != nil {
			return fmt.Errorf("error consuming messages: %w", err)
		}
	}
}

// Close shuts down the consumer group.
func (c *Consumer) Close() error {
	return c.ConsumerGroupClient.Close()
}

// Setup is called before consuming messages.
func (h *consumerGroupHandler) Setup(_ sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup is called after consuming messages.
func (h *consumerGroupHandler) Cleanup(_ sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim processes messages from the Kafka topic.
func (h *consumerGroupHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		consumedMsg := &ConsumedMessage{
			Message: Message{
				Topic:     msg.Topic,
				Key:       msg.Key,
				Value:     msg.Value,
				Partition: msg.Partition,
				Offset:    msg.Offset,
			},
			Timestamp: msg.Timestamp.Unix(),
		}

		// Add headers.
		for _, header := range msg.Headers {
			consumedMsg.Headers = append(consumedMsg.Headers, Header{
				Key:   string(header.Key),
				Value: header.Value,
			})
		}

		// Handle the message.
		if err := h.handler(session.Context(), consumedMsg); err != nil {
			return fmt.Errorf("handler error: %w", err)
		}

		// Mark message as processed.
		session.MarkMessage(msg, "")
	}
	return nil
}
