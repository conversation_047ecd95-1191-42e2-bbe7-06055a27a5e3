package kafka

import (
	"encoding/json"
	"fmt"

	"github.com/IBM/sarama"
	"github.com/Zomato/go/logger"
)

type Producer struct {
	Config                *KafkaConfig
	DefaultProducer       sarama.SyncProducer
	DefaultProducerClient sarama.Client

	ManualProducer       sarama.SyncProducer
	ManualProducerClient sarama.Client
}

func createDefaultProducer(config *KafkaConfig) (sarama.SyncProducer, sarama.Client, error) {
	defaultConfig := sarama.NewConfig()
	defaultConfig.Producer.RequiredAcks = sarama.WaitForLocal
	defaultConfig.Producer.Return.Successes = true
	defaultConfig.Producer.Partitioner = sarama.NewHashPartitioner

	if config.SaslUsername != "" {
		defaultConfig.Net.SASL.Enable = true
		defaultConfig.Net.SASL.User = config.SaslUsername
		defaultConfig.Net.SASL.Password = config.SaslPassword
	}

	client, err := sarama.NewClient(config.Brokers, defaultConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create default Kafka client: %w", err)
	}

	producer, err := sarama.NewSyncProducerFromClient(client)
	if err != nil {
		client.Close()
		return nil, nil, fmt.Errorf("failed to create default producer: %w", err)
	}

	return producer, client, nil
}

func createManualProducer(config *KafkaConfig) (sarama.SyncProducer, sarama.Client, error) {
	manualConfig := sarama.NewConfig()
	manualConfig.Producer.RequiredAcks = sarama.WaitForAll
	manualConfig.Producer.Return.Successes = true
	manualConfig.Producer.Partitioner = sarama.NewManualPartitioner

	if config.SaslUsername != "" {
		manualConfig.Net.SASL.Enable = true
		manualConfig.Net.SASL.User = config.SaslUsername
		manualConfig.Net.SASL.Password = config.SaslPassword
	}

	client, err := sarama.NewClient(config.Brokers, manualConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create manual Kafka client: %w", err)
	}

	producer, err := sarama.NewSyncProducerFromClient(client)
	if err != nil {
		client.Close()
		return nil, nil, fmt.Errorf("failed to create manual producer: %w", err)
	}

	return producer, client, nil
}

func NewProducer(config *KafkaConfig) (*Producer, error) {
	defaultProducer, defaultClient, err := createDefaultProducer(config)
	if err != nil {
		return nil, err
	}

	manualProducer, manualClient, err := createManualProducer(config)
	if err != nil {
		defaultProducer.Close()
		defaultClient.Close()
		return nil, err
	}

	return &Producer{
		Config:                config,
		DefaultProducer:       defaultProducer,
		DefaultProducerClient: defaultClient,
		ManualProducer:        manualProducer,
		ManualProducerClient:  manualClient,
	}, nil
}

// SendMessage uses default partitioning
func (p *Producer) SendMessage(key string, value interface{}, topic string) error {
	if topic == "" {
		return fmt.Errorf("topic must be specified")
	}

	jsonValue, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	message := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(jsonValue),
	}

	// Only set the key if it's not empty
	if key != "" {
		message.Key = sarama.StringEncoder(key)
	}

	partition, offset, err := p.DefaultProducer.SendMessage(message)
	if err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}
	logger.Info("Message sent to partition %d at offset %d", partition, offset)
	return nil
}

// SendMessageToPartition uses manual partitioning
func (p *Producer) SendMessageToPartition(key string, value interface{}, partition int32, topic string) error {
	if topic == "" {
		return fmt.Errorf("topic must be specified")
	}

	// Validate partition
	partitions, err := p.ManualProducerClient.Partitions(topic)
	if err != nil {
		return fmt.Errorf("failed to get partition count: %w", err)
	}

	if partition >= int32(len(partitions)) {
		return fmt.Errorf("invalid partition %d, topic has %d partitions", partition, len(partitions))
	}

	jsonValue, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	message := &sarama.ProducerMessage{
		Topic:     topic,
		Value:     sarama.StringEncoder(jsonValue),
		Partition: partition,
	}

	// Only set the key if it's not empty
	if key != "" {
		message.Key = sarama.StringEncoder(key)
	}

	partition, offset, err := p.ManualProducer.SendMessage(message)
	if err != nil {
		return fmt.Errorf("failed to send message to partition %d: %w", partition, err)
	}
	logger.Info("Message sent successfully to partition %d at offset %d", partition, offset)
	return nil
}

// Close closes both producers and the client
func (p *Producer) Close() error {
	var errs []error

	if err := p.DefaultProducer.Close(); err != nil {
		errs = append(errs, fmt.Errorf("error closing default producer: %w", err))
	}

	if err := p.DefaultProducerClient.Close(); err != nil {
		errs = append(errs, fmt.Errorf("error closing default client: %w", err))
	}

	if err := p.ManualProducer.Close(); err != nil {
		errs = append(errs, fmt.Errorf("error closing manual producer: %w", err))
	}

	if err := p.ManualProducerClient.Close(); err != nil {
		errs = append(errs, fmt.Errorf("error closing manual client: %w", err))
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors while closing producers and clients: %v", errs)
	}
	return nil
}
