#!/bin/bash

# Create required S3 buckets
awslocal s3 mb s3://grofers-prod-dse-sgp
awslocal s3 mb s3://prod-data-feature-store
awslocal s3 mb s3://aws-logs-account-id-region

# Upload sample bootstrap scripts
echo "#!/bin/bash
echo 'Installing Python packages...'" > /tmp/python_bootstrap_packages.sh
awslocal s3 cp /tmp/python_bootstrap_packages.sh s3://prod-data-feature-store/config/emr/python_bootstrap_packages.sh

echo "#!/bin/bash
echo 'Installing Kafka libraries...'" > /tmp/bootstrap_spark_packages.sh
awslocal s3 cp /tmp/bootstrap_spark_packages.sh s3://prod-data-feature-store/config/emr/bootstrap_spark_packages.sh


echo "Mock Spark JAR content" > /tmp/spark-job.jar
awslocal s3 cp /tmp/spark-job.jar s3://prod-data-feature-store/jars/spark-job.jar


# Make the scripts executable
chmod +x /tmp/python_bootstrap_packages.sh
chmod +x /tmp/bootstrap_spark_packages.sh

echo "LocalStack initialized with EMR test resources" 