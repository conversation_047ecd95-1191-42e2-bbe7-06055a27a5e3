version: '3.8'

services:
  cdp-platform-server:
    platform: linux/amd64
    build:
      context: .
    ports:
      - 8000:8000
    container_name: cdp-platform-server
    restart: unless-stopped
    environment:
      - CONFIG_SOURCE=local
      - APP_MODE=http
      - AWS_ACCESS_KEY_ID=AKIAxxxxxxxxxxxxxxx
      - AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    networks:
      - cdp-network

networks:
  cdp-network:
    external: true
