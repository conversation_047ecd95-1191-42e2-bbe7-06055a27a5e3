#!/usr/bin/env python3
"""
Attribute Sync Spark Job

This script is used to execute an attribute query and write the results to a table.
It is designed to be run as a Spark job on an EMR cluster.

Usage:
    spark-submit attribute_sync_spark.py --query "SELECT * FROM table" --table_name "output_table"
"""

import argparse
import sys
import time
import logging
from pyspark.sql import SparkSession

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Attribute Sync Spark Job')
    parser.add_argument('--query', required=True, help='SQL query to execute')
    parser.add_argument('--table_name', required=True, help='Output table name')
    parser.add_argument('--attribute_query_id', required=False, help='Attribute query ID')
    parser.add_argument('--catalog', required=True, help='Catalog to use for output table')
    parser.add_argument('--schema', required=True, help='Schema to use for output table')
    return parser.parse_args()

def create_spark_session():
    """Create and configure a Spark session."""
    return SparkSession.builder.appName("AttributeSyncSparkJob").getOrCreate()

def execute_query(spark, query):
    """Execute the SQL query and return the result DataFrame."""
    logger.info(f"Executing query: {query}")
    try:
        return spark.sql(query)
    except Exception as e:
        logger.error(f"Error executing query: {e}")
        raise

def write_to_table(df, table_name, catalog, schema):
    """Write the DataFrame to an Iceberg table."""
    logger.info(f"Writing results to table: {catalog}.{schema}.{table_name}")
    try:
        if "user_id" not in df.columns:
            raise ValueError("Query result must contain a 'user_id' column")
        
        df.writeTo(f"{catalog}.{schema}.{table_name}") \
            .tableProperty("format-version", "2") \
            .tableProperty("write.format.default", "parquet") \
            .tableProperty("write.parquet.compression-codec", "snappy") \
            .createOrReplace()
    except Exception as e:
        logger.error(f"Error writing to table: {e}")
        raise

def main():
    """Main entry point for the script."""
    start_time = time.time()
    args = parse_arguments()
    
    logger.info(f"Output table name: {args.table_name}")
    if hasattr(args, "attribute_query_id"):
        logger.info(f"Attribute query id: {getattr(args, 'attribute_query_id', None)}")

    try:
        spark = create_spark_session()
        result_df = execute_query(spark, args.query)
        write_to_table(result_df, args.table_name, args.catalog, args.schema)
        elapsed_time = time.time() - start_time
        logger.info(f"Attribute sync job completed successfully in {elapsed_time:.2f} seconds")
        sys.exit(0)
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"Attribute sync job failed after {elapsed_time:.2f} seconds: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
