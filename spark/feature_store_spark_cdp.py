import argparse
import json
import hvac
import boto3
import yaml
import logging
import requests
import hashlib
from pyspark.sql.types import StructType, StructField, MapType, StringType, IntegerType, BooleanType
from pyspark.sql import DataFrame
from pyspark.sql import functions as F
from pyspark.sql import SparkSession
from pyspark.sql.functions import struct, to_json, col
from datetime import datetime
from urllib.parse import urlparse

COLUMN_HASH = "_hashed_"
COLUMN_HASH_RIGHT = COLUMN_HASH + "right_"
SECRET_NAME = "prod/data/consumer-events"
REGION = "ap-southeast-1"
VAULT_URL = "https://vault-ui-prod.grofer.io"

def get_instance_id():
    """
    Get EC2 instance id from metadata service
    """
    try:
        response = requests.get('http://***************/latest/meta-data/instance-id', timeout=5)
        instance_id = response.text.strip()
        return instance_id
    except Exception as e:
        print(f"Error getting instance id: {str(e)}")
        return None

def get_instance_identity():
    """
    Get EC2 instance PKCS7 signature from metadata service
    """
    try:
        response = requests.get(
            'http://***************/latest/dynamic/instance-identity/pkcs7',
            timeout=5
        )
        return response.text.strip()
    except Exception as e:
        print(f"Error getting instance identity: {str(e)}")
        return None


class VaultClient:
    def __init__(self, vault_url=VAULT_URL, github_token=None):
        self.vault_url = vault_url
        self.github_token = github_token
        self.client = None
        self._client_cache = {}
        self._conn_cache = {"secret": {}, "engine": {}}

    def get_hvac_client(self, environment="preprod"):
        if self.client is not None:
            return self.client

        self.client = self._client_cache.get("hvac")
        if self.client is None:
            self.client = hvac.Client(url=self.vault_url)

            if not self.client.is_authenticated():
                if not self._authenticate_using_aws_iam(environment):
                    print("Falling back to EC2 instance profile authentication")
                    self._authenticate_using_aws_ec2_instance_profile(environment)

            self._client_cache["hvac"] = self.client

        return self.client

    def _authenticate_using_aws_ec2_instance_profile(self, environment="preprod"):
        pkcs7 = get_instance_identity()
        nonce = hashlib.md5(get_instance_id().encode()).hexdigest()
        role = get_vault_role(environment)
        self.client.auth.aws.ec2_login(role=role, pkcs7=pkcs7, nonce=nonce, mount_point="aws-analytics")

    def _authenticate_using_github_token(self):
        if self.github_token:
            self.client.auth.github.login(token=self.github_token)

    def _authenticate_using_aws_iam(self, environment="preprod"):
        try:
            session = boto3.Session()
            credentials = session.get_credentials()
            
            if not credentials:
                print("No AWS credentials found")
                return False
                
            access_key = credentials.access_key
            secret_key = credentials.secret_key
            token = credentials.token
            
            region = "ap-southeast-1" 
            role = get_vault_role(environment)
            self.client.auth.aws.iam_login(
                access_key=access_key,
                secret_key=secret_key,
                session_token=token,
                region=region,
                role=role,
                use_token=True,
                header_value=self.vault_url,
                mount_point="aws-analytics"
            )
            print("Successfully authenticated with Vault using AWS IAM")
            return True
            
        except Exception as e:
            print(f"Error authenticating with Vault using AWS IAM: {str(e)}")
            return False

    def get_secret(self, path):
        try:
            secret = self._conn_cache["secret"][path]
        except KeyError:
            client = self.get_hvac_client()
            self._conn_cache["secret"][path] = client.read(path)["data"]
            secret = self._conn_cache["secret"][path]

        return secret


def create_spark_session(app_name):
    spark = (
        SparkSession.builder.appName(app_name)
        .config("spark.sql.orc.impl", "native")
        .config(
            "spark.sql.extensions",
            "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
        )
        .config("spark.sql.iceberg.handle-timestamp-without-timezone", "true")
        .enableHiveSupport()
        .getOrCreate()
    )
    return spark


def fetch_common_prefixes(bucket_name, prefix, delimiter="/"):
    s3_client = boto3.client("s3")
    request = {"Bucket": bucket_name, "Prefix": prefix, "Delimiter": delimiter}

    def list_common_prefixes(list_request, previous_summaries):
        list_object_result = s3_client.list_objects_v2(**list_request)
        summaries = previous_summaries + list_object_result.get("CommonPrefixes", [])
        if list_object_result["IsTruncated"]:
            list_request["ContinuationToken"] = list_object_result["NextContinuationToken"]
            return list_common_prefixes(list_request, summaries)
        else:
            return summaries

    return list_common_prefixes(request, [])


def get_bucket_name(environment):
    if environment == "prod":
        return "s3a://prod-data-feature-store"
    else:
        return "s3a://grofers-test-dse-singapore"

def get_vault_role(environment):
    if environment == "prod":
        return "prod_emr_cdp_spark"
    return "emr_cdp_spark"

def base_path_for_job(entity, context, tenant="BLINKIT", environment="preprod"):
    s3_bucket = get_bucket_name(environment)
    if tenant.upper() != "BLINKIT":
        s3_bucket = f"{s3_bucket}/{tenant.lower()}"

    return f"{s3_bucket}/trino/diff/cdp/{entity}_{context}/"


def last_successful_run_path(entity, context, base_path):
    if base_path.startswith("s3"):
        url = urlparse(base_path)
        common_prefixes = fetch_common_prefixes(url.hostname, url.path.lstrip("/"))
        if not common_prefixes:
            return ""

        prefixes = [entry["Prefix"] for entry in common_prefixes]
        last_prefix = max(prefixes)
        final_path = f"s3a://{url.hostname}/{last_prefix}"
        return final_path
    else:
        print("Cannot check the type for base_path and returning empty")
        return ""


def read_data_frame(path: str, schema: StructType = None) -> DataFrame:
    if len(path) == 0:
        return spark.createDataFrame(spark.sparkContext.emptyRDD(), schema)

    reader = spark.read.option("mode", "DROPMALFORMED")

    if schema is not None:
        reader = reader.schema(schema)

    return reader.parquet(path)


def handle_for_segments(diff_df, prop_type):
    if prop_type == "segment":
        return diff_df.na.fill(False)
    else:
        return diff_df


def convert_dataframe_to_blink_context(df, ttl=None, feature_store_spark_run_id="fs_default_run_id"):
    context_properties_cols = [
        F.col(column).alias(column) for column in df.columns if column != entity_id_column
    ]
    context_properties = F.struct(*context_properties_cols).alias("contextProperties")
    if ttl:
        return df.select(
            F.concat(F.lit(entity + ":"), F.col(entity_id_column)).alias("blinkID"),
            context_properties,
            F.lit("MAP").alias("dataType"),
            F.lit("INSERT").alias("loadType"),
            F.lit(ttl).alias("ttl"),
            F.lit(feature_store_spark_run_id).alias("sourceID"),
            F.lit(context).alias("context"),
        )
    else:
        return df.select(
            F.concat(F.lit(entity + ":"), F.col(entity_id_column)).alias("blinkID"),
            context_properties,
            F.lit("MAP").alias("dataType"),
            F.lit("INSERT").alias("loadType"),
            F.lit(context).alias("context"),
            F.lit(feature_store_spark_run_id).alias("sourceID")
        )


def dump_to_s3(df, base_path):
    df.write.mode("overwrite").parquet(current_successful_run_path(base_path))


def publish_to_kafka(diff_df, boot, topic, username=None, password=None, **kwargs):
    logs_dict = {"start_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    error = ""

    try:
        kafka_writer = diff_df.select(to_json(struct("*")).alias("value")).selectExpr(
            "CAST(value AS STRING)"
        ).write.format("kafka").option("kafka.bootstrap.servers", boot).option(
            "topic", topic
        )
        if username and password:
            print(f"Using SASL authentication for Kafka with username: {username}")
            kafka_writer = kafka_writer.option("kafka.security.protocol", "SASL_SSL") \
                                     .option("kafka.sasl.mechanism", "SCRAM-SHA-512") \
                                     .option("kafka.sasl.jaas.config",
                                           f'org.apache.kafka.common.security.scram.ScramLoginModule required username="{username}" password="{password}";')
        else:
            print("No authentication credentials provided for Kafka")

        kafka_writer.save()
    except Exception as e:
        error = str(e)

    logs_dict["end_timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logs_dict["error"] = error

    # observability not needed in the case of no diff - `diff_df` either has 1 row with null blinkID or 0 rows
    if diff_df.dropna(subset=["blinkID"]).count():
        logs_dict["task_metadata"] = get_task_metadata()
        logs_dict["topic_name"] = topic
        logs_dict["key_passed"] = False
        logs_dict["environment"] = "prod"
        logs_dict["message_count"] = diff_df.count()
        logs_dict["sample_message"] = process_sample_message(diff_df.first())

        observability_brokers = kwargs["observability_brokers"]
        observability_topic = kwargs["observability_topic"]

        try:
            publish_to_observability_kafka(logs_dict, observability_brokers, observability_topic)
        except Exception as e:
            logging.warning(f"Failed to record observability metrics with error: {str(e)}")

    if error:
        raise Exception(error)


def compare_dataframes(df1, df2, entity_id_column):
    columns = df1.columns
    hashed_df1 = add_hash_of_all_values(df1, columns)
    hashed_df2 = add_hash_of_all_values(df2, columns).select(
        F.col(entity_id_column), F.col(COLUMN_HASH).alias(COLUMN_HASH_RIGHT)
    )
    hash_df = hashed_df1.alias("df1").join(
        hashed_df2.alias("df2"),
        on=[
            (F.col(f"df1.{COLUMN_HASH}") == F.col(f"df2.{COLUMN_HASH_RIGHT}"))
            & (F.col(f"df1.{entity_id_column}") == F.col(f"df2.{entity_id_column}"))
        ],
        how="leftanti",
    )
    return hash_df.drop(COLUMN_HASH)


def add_hash_of_all_values(df, columns):
    columnsInDF = [F.col(col) for col in columns if col in df.columns]
    columnsNotInDF = [F.lit(None).alias(col) for col in columns if col not in df.columns]
    resultDF = df.select(columnsInDF + columnsNotInDF)
    return resultDF.withColumn(COLUMN_HASH, F.hash(*resultDF.columns))


def current_successful_run_path(base_path):
    current_datetime = datetime.now().strftime("%Y%m%d%H%M")
    return f"{base_path}{current_datetime}/"


def get_trino_segments(table_properties, entity_id_column):
    # Iterate through properties and create DataFrames
    dfs = []
    for prop in table_properties:
        schema = prop["schema"]
        table = prop["table"]
        columns = ", ".join([entity_id_column] + prop["columns"])
        query = "SELECT {} FROM iceberg.{}.{}".format(columns, schema, table)
        df = spark.sql(query)
        dfs.append(df)
    # Perform full outer join on entity_id_column
    trino_df = dfs[0]
    for df in dfs[1:]:
        trino_df = trino_df.join(df, entity_id_column, "full_outer")
    return trino_df


def load_yaml_data(spark, yaml_s3_path):
    yaml_text = spark.sparkContext.wholeTextFiles(yaml_s3_path).collect()[0][1]
    yaml_data = yaml.safe_load(yaml_text)
    entity = yaml_data.get("entity")
    context = yaml_data.get("context")
    ttl_in_days = yaml_data.get("ttlInDays")
    entity_id_column = yaml_data.get("entityIdColumn")
    prop_type = yaml_data.get("propType")
    table_properties = yaml_data.get("properties", [])
    return entity, context, ttl_in_days, entity_id_column, prop_type, table_properties


# for observability
def publish_to_observability_kafka(logs_dict, observability_brokers, observability_topic):
    dict_schema = StructType(
        [
            StructField("task_metadata", MapType(StringType(), StringType()), True),
            StructField("topic_name", StringType(), True),
            StructField("key_passed", BooleanType(), True),
            StructField("environment", StringType(), True),
            StructField("message_count", IntegerType(), True),
            StructField(
                "sample_message",
                StructType(
                    [
                        StructField("context", StringType(), True),
                        StructField("loadType", StringType(), True),
                        StructField(
                            "contextProperties", MapType(StringType(), BooleanType()), True
                        ),
                        StructField("blinkID", StringType(), True),
                        StructField("dataType", StringType(), True),
                    ]
                ),
                False,
            ),
            StructField("start_timestamp", StringType(), True),
            StructField("end_timestamp", StringType(), True),
            StructField("error", StringType(), True),
        ]
    )

    logs_df = spark.createDataFrame([logs_dict], schema=dict_schema)
    logs_df.select(to_json(struct(col("*"))).alias("value")).write.format("kafka").option(
        "kafka.bootstrap.servers", observability_brokers
    ).option("topic", observability_topic).save()


def get_task_metadata(tenant="BLINKIT"):
    return {
        "user": "<EMAIL>",
        "task_id": "feature_store_spark_cdp",
        "source": "FeatureStoreSparkJob|CDP",
    }


def call_observability_api(run_id, observability_api_creds, increase_count=5):
    if not run_id:
        logging.warning("No run_id provided for observability API call")
        return False

    try:
        # Ensure the endpoint has the proper scheme
        endpoint = observability_api_creds['endpoint']
        if not endpoint.startswith(('http://', 'https://')):
            endpoint = f'https://{endpoint}'
            
        url = f"{endpoint}/observability/{run_id}"
        
        # Prepare headers from the credentials
        headers = {
            'x-api-key': observability_api_creds['api-key'],
            'x-tenant-id': observability_api_creds['tenant-id'],
            'x-tenant-namespace': observability_api_creds['tenant-namespace'],
            'Content-Type': 'application/json'
        }
        
        payload = {
            "increase_expected_count": increase_count
        }

        logging.info(f"Making API call to: {url}")
        
        response = requests.put(
            url,
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            logging.info(f"Successfully called observability API for run_id: {run_id}")
            logging.info(f"Response: {response.text}")
            return True
        else:
            logging.warning(f"Observability API call failed with status {response.status_code}: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        logging.error(f"Request to observability API failed: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"Failed to call observability API for run_id {run_id}: {str(e)}")
        return False


def process_sample_message(sample_message_row):
    sample_message_dict = sample_message_row.asDict()
    if "contextProperties" in sample_message_dict:
        sample_message_dict["contextProperties"] = sample_message_dict["contextProperties"].asDict()
    return sample_message_dict


def get_credential_paths(environment):
    if environment == "prod":
        return {
            "observability_api": "data/services/consumer-events/spark-cluster/feature-store/prod-api",
            "blinkit_id_kafka": "data/services/consumer-events/spark-cluster/kafka/prod-data-blinkID",
            "observability_kafka": "data/services/consumer-events/spark-cluster/kafka/prod-data-events"
        }
    elif environment == "preprod":
        return {
            "observability_api": "data/services/consumer-events/spark-cluster/feature-store/preprod-api",
            "blinkit_id_kafka": "data/services/consumer-events/spark-cluster/kafka/test-data-common",
            "observability_kafka": "data/services/consumer-events/spark-cluster/kafka/test-data-common"
        }
    else:
        raise ValueError(f"Unsupported environment: {environment}. Must be 'prod' or 'preprod'")


def load_credentials_from_vault(environment):
    print(f"Loading credentials for environment: {environment}")
    vault_client = VaultClient()
    vault_client.get_hvac_client(environment=environment)
    credential_paths = get_credential_paths(environment)
    credentials = {}

    observability_api_creds = vault_client.get_secret(credential_paths['observability_api'])
    credentials['observability_api'] = observability_api_creds

    blinkit_kafka_creds = vault_client.get_secret(credential_paths['blinkit_id_kafka'])
    credentials['blinkit_id_kafka'] = blinkit_kafka_creds

    observability_kafka_creds = vault_client.get_secret(credential_paths['observability_kafka'])
    credentials['observability_kafka'] = observability_kafka_creds  

    return credentials


def compare_dataframes_with_dropouts(current_df, previous_df, entity_id_column):
    """
    Enhanced comparison that detects both normal changes AND users who dropped out entirely of the audience
    """
    # Step 1: Get differences in the audience current and prev state
    existing_user_changes = compare_dataframes(current_df, previous_df, entity_id_column)
    
    # Step 2: Find users who completely dropped out of the entire audience
    current_user_ids = current_df.select(entity_id_column).distinct()
    previous_user_ids = previous_df.select(entity_id_column).distinct()
    
    # Users in previous state of the audience but not in current = dropped out
    dropped_users = previous_user_ids.join(current_user_ids, entity_id_column, "leftanti")
    
    # Step 3: Create records for dropped users (all segments = false)
    if dropped_users.count() > 0:  # Only process if there are dropped users
        print(f"Found {dropped_users.count()} users who dropped out of all segments")
        
        # Get all segment columns from current schema (excluding entity_id)
        segment_columns = [col for col in current_df.columns if col != entity_id_column]
        
        # Start with dropped user IDs
        dropped_records = dropped_users
        
        # Add all segment columns as FALSE
        for col_name in segment_columns:
            dropped_records = dropped_records.withColumn(col_name, F.lit(False))
        
        # Ensure column order matches current_df
        dropped_records = dropped_records.select(*current_df.columns)
        
        # Step 4: Union normal changes with dropout records
        complete_delta = existing_user_changes.union(dropped_records)
        print(f"Total changes: {existing_user_changes.count()} existing users + {dropped_records.count()} dropouts = {complete_delta.count()}")
        
        return complete_delta
    else:
        print("No users dropped out completely - using existing user changes only")
        return existing_user_changes


parser = argparse.ArgumentParser()
parser.add_argument("--properties_s3_path", required=False, help="S3 path of the properties YAML file")
parser.add_argument("--tenant", required=False, help="S3 path of the properties YAML file")
parser.add_argument("--feature_store_spark_run_id", required=False, help="Spark run id for feature store observability")
parser.add_argument("--environment", required=True, help="Environment: prod or preprod")

args, redundant_args = parser.parse_known_args()
yaml_s3_path = args.properties_s3_path
tenant = args.tenant
feature_store_spark_run_id = args.feature_store_spark_run_id
environment = args.environment.lower()

if not feature_store_spark_run_id:
    feature_store_spark_run_id = "fs_default_run_id"

all_credentials = load_credentials_from_vault(environment)

spark = create_spark_session("PersonasCDP")
entity, context, ttl_in_days, entity_id_column, prop_type, table_properties = load_yaml_data(
    spark, yaml_s3_path
)

trino_df = (
    get_trino_segments(table_properties, entity_id_column)
    .repartition(30, F.col(entity_id_column))
    .cache()
)

base_path = base_path_for_job(entity, context, tenant=tenant, environment=environment)

last_successful_df = read_data_frame(
    last_successful_run_path(entity, context, base_path), trino_df.schema
).repartition(30, F.col(entity_id_column))

diff_df = compare_dataframes_with_dropouts(trino_df, last_successful_df, entity_id_column)

update_diff = handle_for_segments(diff_df, prop_type)

# Use environment-specific credentials loaded from vault
kafka_creds = all_credentials['blinkit_id_kafka']
observability_kafka_creds = all_credentials['observability_kafka']

if ttl_in_days > 0:
    kafka_df = convert_dataframe_to_blink_context(update_diff, ttl=ttl_in_days * 86400, feature_store_spark_run_id=feature_store_spark_run_id)
else:
    kafka_df = convert_dataframe_to_blink_context(update_diff, ttl=None, feature_store_spark_run_id=feature_store_spark_run_id)

kwargs = {
    "observability_brokers": observability_kafka_creds["bootstrap_servers"],
    "observability_topic": observability_kafka_creds["topic"],
}

kafka_topic_key = "topic"
if tenant == "BISTRO":
    kafka_topic_key = "bistro_topic"

# Determine if password authentication is needed (only for prod Blinkit ID Kafka)
use_password_auth_blink_id = (environment == "prod")

username = None
password = None
if use_password_auth_blink_id:
    username = kafka_creds.get("username")
    password = kafka_creds.get("password")
    print(f"Password authentication enabled for prod Blinkit ID Kafka. Username: {username}")
else:
    print(f"No password authentication needed for environment: {environment}")

if feature_store_spark_run_id:
    logging.info(f"Calling observability API for run_id: {feature_store_spark_run_id}")
    message_count = kafka_df.count()
    logging.info(f"Actual message count: {message_count}")
    observability_api_creds = all_credentials['observability_api']
    call_observability_api(feature_store_spark_run_id, observability_api_creds, increase_count=message_count)
else:
    logging.warning("No feature_store_spark_run_id provided, skipping observability API call")


publish_to_kafka(
    diff_df=kafka_df,
    boot=kafka_creds.get("bootstrap_servers"),
    topic=kafka_creds.get(kafka_topic_key),
    username=username,
    password=password,
    **kwargs,
)

dump_to_s3(trino_df, base_path)


