########################################
## Build Stage
########################################
FROM public.ecr.aws/zomato/golang:1.23.0-bullseye as builder

# add a label to clean up later
LABEL stage=intermediate

# setup the working directory
WORKDIR /go/src

# add netrc file to allow access to private github repo
COPY .netrc /root/.netrc

# install dependencies
ENV GO111MODULE=on
COPY ./go.mod ./go.mod
COPY ./go.sum ./go.sum
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download -x

# add source code
COPY . .

# add required files from host
COPY ./configs /root/configs

# build the source
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o cdp-platform-linux-amd64

########################################
## Production Stage
########################################
FROM public.ecr.aws/zomato/zomato/base:v1
# set working directory
WORKDIR /root

# copy required files from builder
COPY --from=builder /go/src/cdp-platform-linux-amd64 ./cdp-platform-linux-amd64
COPY --from=builder /go/src/configs ./configs
RUN touch .env
# Expose port 8000
EXPOSE 8000

CMD ["./cdp-platform-linux-amd64"]