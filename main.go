package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	handlers "github.com/Zomato/cdp-platform/handlers"
	authinternal "github.com/Zomato/cdp-platform/internal/auth"
	"github.com/Zomato/cdp-platform/internal/database/postgres"
	"github.com/Zomato/cdp-platform/internal/database/starrocks"
	"github.com/Zomato/cdp-platform/internal/router"
	"github.com/Zomato/cdp-platform/pkg/appsflyer"
	featurestore "github.com/Zomato/cdp-platform/pkg/feature-store"
	"github.com/Zomato/cdp-platform/pkg/kafka"
	"github.com/Zomato/cdp-platform/pkg/s3"
	zsql "github.com/Zomato/cdp-platform/pkg/sqlx"
	"github.com/Zomato/cdp-platform/pkg/temporal"
	logger "github.com/Zomato/go/logger"

	"github.com/Zomato/cdp-platform/internal/app"
	"github.com/Zomato/cdp-platform/internal/env"

	"github.com/Zomato/cdp-platform/pkg/emr"
	"github.com/Zomato/go/config"
	"github.com/joho/godotenv"
)

func main() {
	/* Load Environment Variables*/
	err := godotenv.Load()
	if err != nil {
		logger.Info("Error loading .env file on startup")
	}

	/* Initialise config */
	config.Init()
	ctx, err := config.TODOContext()
	if err != nil {
		logger.Panic("Error while fetching App context", err)
	}

	err = logger.Initialize(
		logger.Formatter(config.GetString(ctx, "log.format")),
		logger.Level(config.GetString(ctx, "log.level")),
	)
	if err != nil {
		logger.FromContext(ctx).WithError(err).Panic("failed to initialise logger client")
	}

	serviceName := config.GetString(ctx, "service.name")
	logger.Debug("Validating config ", serviceName)

	/* Establish Connection with postgres */
	masterConfig := zsql.NewConfig()
	masterConfig.Host = config.GetString(ctx, "postgres.master.host")
	masterConfig.Port = config.GetInt(ctx, "postgres.master.port")
	masterConfig.DBName = config.GetString(ctx, "postgres.master.dbname")
	masterConfig.User = config.GetString(ctx, "postgres.master.username")
	masterConfig.Password = config.GetString(ctx, "postgres.master.password")

	replicaConfig := zsql.NewConfig()
	replicaConfig.Host = config.GetString(ctx, "postgres.replica.host")
	replicaConfig.Port = config.GetInt(ctx, "postgres.replica.port")
	replicaConfig.DBName = config.GetString(ctx, "postgres.replica.dbname")
	replicaConfig.User = config.GetString(ctx, "postgres.replica.username")
	replicaConfig.Password = config.GetString(ctx, "postgres.replica.password")

	appMode := os.Getenv("APP_MODE")

	postgresConsistency := config.GetBool(ctx, fmt.Sprintf(`postgres.strong_consistency.%s`, appMode))
	postgresClient, err := postgres.NewClient(postgresConsistency, masterConfig, replicaConfig)
	if err != nil {
		logger.WithError(err).Error("Failed to initialise postgres client")
	} else {
		logger.Info("Postgres Client created successfully")
	}
	defer postgresClient.Close()

	/* Initialize Temporal client */
	temporalConfig := temporal.NewConfig()
	temporalConfig.Host = config.GetString(ctx, "temporal.server.host")
	temporalConfig.Port = config.GetInt(ctx, "temporal.server.port")

	temporalClient, err := temporal.NewClient(temporalConfig)
	if err != nil {
		logger.WithError(err).Error("Failed to create Temporal client")
	} else {
		logger.Info("Temporal Client created successfully")
	}
	defer temporalClient.Close()

	/* Initialize StarRocks client */
	starrocksConfig := zsql.NewConfig()
	starrocksConfig.Host = config.GetString(ctx, "starrocks.master.host")
	starrocksConfig.Port = config.GetInt(ctx, "starrocks.master.port")
	starrocksConfig.User = config.GetString(ctx, "starrocks.master.username")

	// Optional fields
	starrocksConfig.Password = config.GetString(ctx, "starrocks.master.password")
	starrocksConfig.DBName = config.GetString(ctx, "starrocks.master.dbname")

	// Connection pool settings
	// starrocksConfig.MaxOpenConn = config.GetInt(ctx, "starrocks.master.max_open_conn")
	// starrocksConfig.MaxIdleConn = config.GetInt(ctx, "starrocks.master.max_idle_conn")
	// starrocksConfig.ConnMaxLifetime = config.GetDuration(ctx, "starrocks.master.conn_max_lifetime")
	// starrocksConfig.ConnMaxIdleTime = config.GetDuration(ctx, "starrocks.master.conn_max_idle_time")

	// Validate required configuration
	if starrocksConfig.Host == "" || starrocksConfig.Port == 0 || starrocksConfig.User == "" {
		logger.Error("StarRocks configuration missing required fields (host, port, or username)")
	}

	// Initialize StarRocks client with proper error handling
	starrocksClient, err := starrocks.NewClient(
		config.GetBool(ctx, fmt.Sprintf("starrocks.strong_consistency.%s", appMode)),
		starrocksConfig,
		nil, // We can add replica config later if needed
	)

	if err != nil {
		logger.WithError(err).Error("Failed to initialize StarRocks client")
	}

	if starrocksClient == nil {
		logger.Error("StarRocks client is nil after initialization")
	}

	// Verify connection with retry logic
	maxRetries := 3
	var pingErr error
	for i := 0; i < maxRetries; i++ {
		pingErr = starrocksClient.Ping()
		if pingErr == nil {
			break
		}
		logger.WithError(pingErr).Warnf("Failed to ping StarRocks database (attempt %d/%d)", i+1, maxRetries)
		time.Sleep(time.Second * 2) // Wait 2 seconds between retries
	}

	if pingErr != nil {
		logger.WithError(pingErr).Error("Failed to establish connection with StarRocks after multiple attempts")
	} else {
		logger.Info("StarRocks Client created successfully")
	}

	defer func() {
		if err := starrocksClient.Close(); err != nil {
			logger.WithError(err).Error("Error closing StarRocks connection")
		}
	}()

	// Adding Kafka Producer
	kafkaConfig := kafka.NewKafkaConfigWithoutPassword(
		config.GetStringSlice(ctx, "kafka.brokers"),
	)
	if err := kafkaConfig.Validate(); err != nil {
		fmt.Println("Configuration error:", err)
	} else {
		fmt.Println("Kafka configuration is valid.")
	}
	// Initialize the Kafka Producer
	kafkaProducer, err := kafka.NewProducer(kafkaConfig)
	if err != nil {
		fmt.Printf("Failed to initialize Kafka producer: %v\n", err)
	} else {
		logger.Info("Kafka Client created successfully")
	}

	defer func() {
		if err := kafkaProducer.Close(); err != nil {
			fmt.Printf("Failed to close producer: %v\n", err)
		}
	}()

	// Initialize AppsFlyer client if enabled
	var appsflyerClient *appsflyer.Client

	enableAppsflyer := config.GetBool(ctx, "appsflyer.enable")
	if enableAppsflyer {
		appsflyerToken := config.GetString(ctx, "appsflyer.token")

		if appsflyerToken == "" {
			logger.Error("AppsFlyer token is missing in configuration")
		} else {
			appsflyerClient, err = appsflyer.NewClient(appsflyerToken)
			if err != nil {
				logger.WithError(err).Error("Error creating AppsFlyer client")
			} else if appsflyerClient == nil {
				logger.Error("AppsFlyer client is nil after initialization")
			} else {
				logger.Info("AppsFlyer Client created successfully")
				defer appsflyerClient.Close()
			}
		}
	} else {
		logger.Info("AppsFlyer client initialization skipped (disabled in config)")
	}

	/* Initialize EMR client */
	emrConfig := &emr.ClientConfig{
		Region: config.GetString(ctx, "emr.region"),
	}
	// Check if custom endpoint is specified (for local testing)
	if endpoint := config.GetString(ctx, "emr.endpoint"); endpoint != "" {
		emrConfig.Endpoint = endpoint
	}

	emrClient, err := emr.NewClient(ctx, emrConfig)
	if err != nil {
		logger.WithError(err).Error("Failed to initialize EMR client")
	} else {
		logger.Info("EMR Client created successfully")
	}

	/* Initialize S3 client */
	s3Config := &s3.ClientConfig{
		Region: config.GetString(ctx, "s3.region"),
	}
	// Check if custom endpoint is specified (for local testing)
	if endpoint := config.GetString(ctx, "s3.endpoint"); endpoint != "" {
		s3Config.Endpoint = endpoint
	}

	s3Client, err := s3.NewClient(ctx, s3Config)
	if err != nil {
		logger.WithError(err).Error("Failed to initialize S3 client")
	} else {
		logger.Info("S3 Client created successfully")
	}

	// Initialize FeatureStore client if enabled
	var featurestoreClient *featurestore.Client

	enableFeatureStore := config.GetBool(ctx, "feature-store.enable")
	if enableFeatureStore {
		featureStoreConfig := featurestore.Config{
			BaseURL:         config.GetString(ctx, "feature-store.endpoint"),
			APIKey:          config.GetString(ctx, "feature-store.api-key"),
			TenantID:        config.GetString(ctx, "feature-store.tenant-id"),
			TenantNamespace: config.GetString(ctx, "feature-store.tenant-namespace"),
		}
		featurestoreClient, err = featurestore.NewClient(featureStoreConfig)
		if err != nil {
			logger.WithError(err).Error("Failed to initialize Feature Store client")
		} else {
			logger.Info("Feature Store Client created successfully")
		}
	} else {
		logger.Info("Feature Store client initialization skipped (disabled in config)")
	}

	/* Spin up Environment with all clients */
	environment := env.NewEnv(
		env.WithPostgresClient(postgresClient),
		env.WithTemporalClient(temporalClient),
		env.WithStarRocksClient(starrocksClient),
		env.WithKafkaProducer(kafkaProducer),
		env.WithAppsflyerClient(appsflyerClient),
		env.WithEMRClient(emrClient),
		env.WithFeatureStoreClient(featurestoreClient),
		env.WithS3Client(s3Client),
	)
	ctx = environment.WithContext(ctx)

	/* Setup the router & server */
	r := router.SetupRouter(ctx)
	server := &http.Server{
		Addr:    config.GetString(ctx, "service.port"),
		Handler: r,
	}

	// Initialize auth configuration
	authinternal.SetSigningKey(config.GetString(ctx, "auth.signing-key"))
	/* Initialise Auth Handler */
	handlers.AuthInitialise(&handlers.AuthConfig{
		GoogleClientID:     config.GetString(ctx, "auth.google.client-id"),
		GoogleClientSecret: config.GetString(ctx, "auth.google.client-secret"),
		GoogleRedirectURL:  config.GetString(ctx, "auth.google.redirect-url"),
	})

	/* Launch the application */
	zapp := app.NewApp()
	err = zapp.WithRouter(server).WithMode(appMode).Run(ctx)
	if err != nil {
		defer log.Panicf("Could not start: %s", err)
	}
}
