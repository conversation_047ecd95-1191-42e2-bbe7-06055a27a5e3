package postgres

import (
	"github.com/lib/pq"
)

type FeatureStoreSegment struct {
	Base
	Tenant       string         `db:"tenant" json:"tenant"`
	Namespace    string         `db:"namespace" json:"namespace"`
	Entity       string         `db:"entity" json:"entity"`
	ContextName  string         `db:"context_name" json:"context_name"`
	ContextType  string         `db:"context_type" json:"context_type"`
	FeatureName  string         `db:"feature_name" json:"feature_name"`
	FeatureType  string         `db:"feature_type" json:"feature_type"`
	IsVerified   bool           `db:"is_verified" json:"is_verified"`
	IsPII        bool           `db:"is_pii" json:"is_pii"`
	Destinations pq.StringArray `db:"destinations" json:"destinations"`
	Description  string         `db:"description" json:"description"`
	Expiry       string         `db:"expiry" json:"expiry"`
	OwnerID      int64          `db:"owner_id" json:"owner_id"`
	IsActive     bool           `db:"is_active" json:"is_active"`
	LogTableName string         `db:"log_table_name" json:"log_table_name"`
}

type FeatureStoreSyncMapping struct {
	Base
	FeatureID         int64 `db:"feature_id" json:"feature_id"`
	DestinationSyncID int64 `db:"destination_sync_id" json:"destination_sync_id"`
}
