package postgres

import "time"

type AttributeSyncRuns struct {
	Base
	AttributeQueryID int64      `db:"attribute_query_id" json:"attribute_query_id"`
	ExecutionStart   *time.Time `db:"execution_start" json:"execution_start"`
	ExecutionEnd     *time.Time `db:"execution_end" json:"execution_end"`
	Status           string     `db:"status" json:"status"`
	SyncTableName    string     `db:"sync_table_name" json:"sync_table_name"`
	RowsAffected     int64      `db:"rows_affected" json:"rows_affected"`
	ErrorMessage     string     `db:"error_message" json:"error_message"`
}
