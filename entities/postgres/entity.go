package postgres

type Entity struct {
	Base
	Description    string `db:"description" json:"description"`
	Type           string `db:"type" json:"type"`
	TableName      string `db:"table_name" json:"table_name"`
	SchemaName     string `db:"schema_name" json:"schema_name"`
	CatalogName    string `db:"catalog_name" json:"catalog_name"`
	RequiresFilter bool   `db:"requires_filter" json:"requires_filter"`
	OwnerID        int64  `db:"owner_id" json:"owner_id"`
	CreatedAt      string `db:"created_at" json:"created_at"`
	UpdatedAt      string `db:"updated_at" json:"updated_at"`
}
