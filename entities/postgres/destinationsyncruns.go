package postgres

import "time"

type DestinationSyncRun struct {
	Base
	DestinationSyncID int64      `db:"destination_sync_id" json:"destination_sync_id"`
	ExecutionStart    *time.Time `db:"execution_start" json:"execution_start"`
	ExecutionEnd      *time.Time `db:"execution_end" json:"execution_end,omitempty"`
	Status            string     `db:"status" json:"status"`
	SyncTableName     string     `db:"sync_table_name" json:"sync_table_name,omitempty"`
	RowsAffected      int        `db:"rows_affected" json:"rows_affected,omitempty"`
	ErrorMessage      string     `db:"error_message" json:"error_message,omitempty"`
}
