package postgres

type AttributeType string

const (
	AttributeTypeBase    AttributeType = "BASE"
	AttributeTypeDerived AttributeType = "DERIVED"
)

type DerivedAttributeType string

const (
	DerivedAttributeTypeTGCG        DerivedAttributeType = "TGCG"
	DerivedAttributeTypeGeneral     DerivedAttributeType = "GENERAL"
	DerivedAttributeTypeArithmetic  DerivedAttributeType = "ARITHMETIC"
	DerivedAttributeTypeConditional DerivedAttributeType = "CONDITIONAL"
)

type AttributeCategory string

const (
	AttributeCategoryMetric    AttributeCategory = "METRIC"
	AttributeCategoryDimension AttributeCategory = "DIMENSION"
	AttributeCategoryProperty  AttributeCategory = "PROPERTY"
)

type AttributePurpose string

const (
	AttributePurposeCondition AttributePurpose = "CONDITION"
	AttributePurposeFilter    AttributePurpose = "FILTER"
)

type Attributes struct {
	Base
	Name              string            `db:"name" json:"name"`
	Description       string            `db:"description" json:"description"`
	OwnerID           int64             `db:"owner_id" json:"owner_id"`
	AttributeType     AttributeType     `db:"attribute_type" json:"attribute_type"`
	AttributeCategory AttributeCategory `db:"attribute_category" json:"attribute_category"`
	AttributePurpose  AttributePurpose  `db:"attribute_purpose" json:"attribute_purpose"`
}

type BaseAttributeInfo struct {
	Base
	AttributeID      int64  `db:"attribute_id" json:"attribute_id"`
	QueryColumnName  string `db:"query_column_name" json:"query_column_name"`
	DataType         string `db:"data_type" json:"data_type"`
	AttributeQueryID int64  `db:"attribute_query_id" json:"attribute_query_id"`
}

type DerivedAttributeInfo struct {
	Base
	AttributeID          int64                `db:"attribute_id" json:"attribute_id"`
	Expression           string               `db:"expression" json:"expression"`
	DataType             string               `db:"data_type" json:"data_type"`
	DerivedAttributeType DerivedAttributeType `db:"attribute_type" json:"attribute_type"`
}

type DerivedAttributeMapping struct {
	Base
	DerivedAttributeID int64 `db:"derived_attribute_id" json:"derived_attribute_id"`
	SourceAttributeID  int64 `db:"source_attribute_id" json:"source_attribute_id"`
}
