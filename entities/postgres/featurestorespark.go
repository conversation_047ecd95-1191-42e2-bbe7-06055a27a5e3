package postgres

import (
	"time"
)

type FeatureStoreSparkRun struct {
	Base
	RunID          string     `db:"run_id" json:"run_id"`
	ExecutionStart *time.Time `db:"execution_start" json:"execution_start"`
	ExecutionEnd   *time.Time `db:"execution_end" json:"execution_end,omitempty"`
	Status         string     `db:"status" json:"status"`
	ErrorMessage   string     `db:"error_message" json:"error_message,omitempty"`
}

type FeatureStoreSparkDestinationSyncRunMapping struct {
	Base
	FeatureStoreSparkRunID int64 `db:"feature_store_spark_run_id" json:"feature_store_spark_run_id"`
	DestinationSyncRunID   int64 `db:"destination_sync_run_id" json:"destination_sync_run_id"`
}
