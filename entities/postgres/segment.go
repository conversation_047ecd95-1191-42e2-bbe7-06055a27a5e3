package postgres

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type Condition struct {
	Type            string      `db:"type" json:"type"`
	AttributeId     *int64      `db:"attribute_id" json:"attribute_id,omitempty"`
	Operator        *string     `db:"operator" json:"operator,omitempty"`
	ConditionValue  interface{} `db:"value" json:"value,omitempty"`
	LogicalOperator *string     `db:"logical_operator" json:"logical_operator,omitempty"`
	Conditions      []Condition `db:"conditions" json:"conditions,omitempty"`
	IsFilter        *bool       `db:"is_filter" json:"is_filter,omitempty"`
	EntityId        *int64      `db:"entity_id" json:"entity_id,omitempty"`
}

// Scan implements the sql.Scanner interface
// TODO: create utils for this
func (c *Condition) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to scan Condition")
	}
	return json.Unmarshal(bytes, c)
}

// Value implements the driver.Valuer interface
// TODO: create utils for this
func (c Condition) Value() (driver.Value, error) {
	return json.Marshal(c)
}

type Segment struct {
	Base
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	QueryText   string    `db:"query_text" json:"query_text"`
	Conditions  Condition `db:"conditions" json:"conditions"`
	OwnerID     int64     `db:"owner_id" json:"owner_id"`
}
