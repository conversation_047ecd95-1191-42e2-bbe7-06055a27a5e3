package postgres

import "time"

type AttributeQueries struct {
	Base
	DataSourceID   int64      `db:"data_source_id" json:"data_source_id"`
	OwnerID        int64      `db:"owner_id" json:"owner_id"`
	CronExpression string     `db:"cron_expression" json:"cron_expression"`
	QueryText      string     `db:"query_text" json:"query_text"`
	EntityID       int64      `db:"entity_id" json:"entity_id"`
	EndDate        *time.Time `db:"end_date" json:"end_date,omitempty"`
}
