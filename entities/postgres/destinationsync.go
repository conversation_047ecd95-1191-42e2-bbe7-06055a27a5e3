package postgres

type DestinationSync struct {
	Base
	SegmentID             int64   `db:"segment_id" json:"segment_id"`
	DestinationID         int64   `db:"destination_id" json:"destination_id"`
	CommittedSegmentTable string  `db:"commited_segment_table_name" json:"committed_segment_table"`
	OngoingSegmentTable   string  `db:"ongoing_segment_table_name" json:"ongoing_segment_table"`
	SyncStrategy          string  `db:"sync_strategy" json:"sync_strategy"`
	CronExpression        string  `db:"cron_expression" json:"cron_expression"`
	OwnerID               int64   `db:"owner_id" json:"owner_id"`
	EndDate               *string `db:"end_date" json:"end_date,omitempty"`
}

type TgCgDestinationSync struct {
	Base
	DestinationSyncID int64     `db:"destination_sync_id" json:"destination_sync_id"`
	Conditions        Condition `db:"conditions" json:"conditions"`
}
