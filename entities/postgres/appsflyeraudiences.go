package postgres

type AppsflyerAudience struct {
	Base
	Name                string `db:"name" json:"name"`
	Platform            string `db:"platform" json:"platform"`
	Description         string `db:"description" json:"description"`
	ImportKey           string `db:"import_key" json:"import_key"`
	SnapshotTable       string `db:"snapshot_table" json:"snapshot_table"`
	AppsflyerAudienceID int64  `db:"appsflyer_audience_id" json:"appsflyer_audience_id"`
}
