package utils

import (
	"github.com/Zomato/go/logger"
	goEmail "github.com/mcnijman/go-emailaddress"
)

const (
	ZOMATO_EMAIL_DOMAIN  = "zomato.com"
	BLINKIT_EMAIL_DOMAIN = "grofers.com"
)

var ALLOWED_EMAIL_DOMAINS = []string{ZOMATO_EMAIL_DOMAIN, BLINKIT_EMAIL_DOMAIN}

func StringInSlice(a string, list []string) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

func StringInSliceIndex(a string, list []string) int {
	for i, b := range list {
		if b == a {
			return i
		}
	}
	return -1
}

func RemoveIndex(s []map[string]interface{}, index int) []map[string]interface{} {
	ret := make([]map[string]interface{}, 0)
	ret = append(ret, s[:index]...)
	return append(ret, s[index+1:]...)
}

func IsAllowedDomain(email string) bool {
	emailDomain, err := goEmail.Parse(email)
	if err != nil {
		logger.WithError(err).<PERSON><PERSON><PERSON>("Something went wrong while parsing the email %v", email)
		return false
	}
	return StringInSlice(emailDomain.Domain, ALLOWED_EMAIL_DOMAINS)
}

func StringOrEmpty(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
