package utils

import (
	"html"
	"reflect"

	"github.com/microcosm-cc/bluemonday"
)

func SanitizeString(input string) string {
	p := bluemonday.StrictPolicy()
	return html.UnescapeString(p.Sanitize(input))
}

func SanitizeStruct(v interface{}) {
	p := bluemonday.StrictPolicy()
	val := reflect.ValueOf(v).Elem()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		if field.Kind() == reflect.String {
			field.SetString(html.UnescapeString(p.Sanitize(field.String())))
		}
	}
}
