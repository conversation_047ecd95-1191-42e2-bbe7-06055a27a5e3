package utils

import (
	"fmt"
	"time"
)

var (
	TimeFormat string = "2006-01-02T15:04:05-0700"
)

func GetCurrentTimeString() string {
	return GetCurrentTime().Format(TimeFormat)
}

func GetTimeString(t time.Time) string {
	if t.<PERSON>ero() == true {
		return GetCurrentTimeString()
	}
	return t.Format(TimeFormat)
}

func GetCurrentTime() time.Time {
	return time.Now()
}

// ParseISTDateToStartOfDayinUTC parses a date string and returns it as start of day in UTC
// returned time is IST but corresponds to the start of the day in UTC
func ParseISTDateToStartOfDay(istDateStr string) (time.Time, error) {
	// Load IST timezone
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to load IST timezone: %w", err)
	}

	// Support multiple date formats
	formats := []string{
		"2006-01-02T15:04:05Z",      // RFC3339 UTC
		"2006-01-02T15:04:05",       // ISO 8601 without timezone
		"2006-01-02 15:04:05",       // Common datetime format
		"2006-01-02",                // Date only (will set to start of day)
		"02/01/2006",                // DD/MM/YYYY
		"01/02/2006",                // MM/DD/YYYY
		"2006-01-02T15:04:05-07:00", // RFC3339 with timezone
	}

	var parsedTime time.Time

	for _, format := range formats {
		parsedTime, err = time.Parse(format, istDateStr)
		if err == nil {
			break
		}
	}

	if err != nil {
		return time.Time{}, fmt.Errorf("unable to parse date '%s': supported formats are YYYY-MM-DD, YYYY-MM-DDTHH:MM:SS, etc.", istDateStr)
	}

	// Always treat input as IST and set to start of day
	parsedTime = time.Date(
		parsedTime.Year(),
		parsedTime.Month(),
		parsedTime.Day(),
		5, 30, 0, 0, // Start of day: 00:00:00
		istLocation,
	)

	// Return IST time (not converted to UTC)
	return parsedTime, nil
}

func GetMinimumTime(endDates []*time.Time) *time.Time {
	var minEndDate *time.Time

	for _, endDate := range endDates {
		if endDate == nil {
			continue
		}

		if minEndDate == nil || endDate.Before(*minEndDate) {
			minEndDate = endDate
		}
	}

	return minEndDate
}
