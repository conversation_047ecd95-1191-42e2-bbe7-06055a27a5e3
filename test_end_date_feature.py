#!/usr/bin/env python3
"""
Comprehensive test script for the end_date feature implementation.
Tests all changes from PR #169: attribute end_date support.

Usage:
    python test_end_date_feature.py

Requirements:
    - Server running on localhost:8000 (use: dcb && dcu)
    - PostgreSQL accessible via docker exec
"""

import requests
import json
import time
import random
import string
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    print("Warning: psycopg2 not available. Database tests will be skipped.")
    print("Install with: pip install psycopg2-binary")

class CDPTestClient:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        # Set headers that might be needed for authentication
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            print(f"{method} {url} -> {response.status_code}")
            if response.status_code >= 400:
                print(f"Error response: {response.text}")
            return response
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            raise
    
    def get(self, endpoint: str, **kwargs) -> requests.Response:
        return self._make_request('GET', endpoint, **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> requests.Response:
        return self._make_request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, **kwargs) -> requests.Response:
        return self._make_request('PUT', endpoint, **kwargs)

class DatabaseHelper:
    @staticmethod
    def get_connection():
        """Get PostgreSQL connection"""
        if not PSYCOPG2_AVAILABLE:
            return None

        try:
            conn = psycopg2.connect(
                host="localhost",
                port=5432,
                database="cdp_platform",
                user="cdp",
                password="cdp"
            )
            return conn
        except Exception as e:
            print(f"Database connection failed: {e}")
            return None

    @staticmethod
    def execute_sql(sql: str, fetch: bool = False):
        """Execute SQL command in PostgreSQL"""
        if not PSYCOPG2_AVAILABLE:
            print("psycopg2 not available, falling back to docker exec")
            return DatabaseHelper.execute_sql_docker(sql, fetch)

        conn = DatabaseHelper.get_connection()
        if not conn:
            print("Failed to connect to database, falling back to docker exec")
            return DatabaseHelper.execute_sql_docker(sql, fetch)

        try:
            cursor = conn.cursor()
            cursor.execute(sql)

            if fetch:
                result = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                conn.close()
                return {"columns": columns, "rows": result}
            else:
                conn.commit()
                conn.close()
                return True
        except Exception as e:
            print(f"SQL execution failed: {e}")
            conn.close()
            return False

    @staticmethod
    def execute_sql_docker(sql: str, fetch: bool = False):
        """Fallback: Execute SQL command via docker exec"""
        cmd = [
            'docker', 'exec', 'cdp-platform-postgres',
            'psql', '-U', 'cdp', '-d', 'cdp_platform', '-c', sql
        ]

        try:
            if fetch:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                return result.stdout
            else:
                subprocess.run(cmd, check=True)
                return True
        except subprocess.CalledProcessError as e:
            print(f"SQL execution failed: {e}")
            if hasattr(e, 'stderr') and e.stderr:
                print(f"Error: {e.stderr}")
            return False
    
    @staticmethod
    def insert_test_data(is_test: bool = True):
        """Insert test data for end_date testing"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=6))
        
        # Insert test attribute query with end_date
        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S+05:30")
        
        sql = f"""
        INSERT INTO attribute_queries (
            data_source_id, owner_id, cron_expression, query_text, entity_id, end_date
        ) VALUES (
            1, 1, '0 */6 * * *', 
            'SELECT user_id, test_column_{random_suffix} FROM test_table_{timestamp}',
            1, '{future_date}'
        ) RETURNING id;
        """
        
        if is_test:
            print(f"Test SQL to insert: {sql}")
            return {"query_id": f"test_{timestamp}_{random_suffix}", "end_date": future_date}
        else:
            result = DatabaseHelper.execute_sql(sql, fetch=True)
            if result:
                # Extract ID from result
                lines = result.strip().split('\n')
                for line in lines:
                    if line.strip().isdigit():
                        return {"query_id": int(line.strip()), "end_date": future_date}
            return None
    
    @staticmethod
    def cleanup_test_data(query_id: int, is_test: bool = True):
        """Clean up test data"""
        sql = f"DELETE FROM attribute_queries WHERE id = {query_id};"
        
        if is_test:
            print(f"Cleanup SQL: {sql}")
            return True
        else:
            return DatabaseHelper.execute_sql(sql)

class EndDateFeatureTests:
    def __init__(self, client: CDPTestClient, is_test: bool = True):
        self.client = client
        self.is_test = is_test
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if success else "FAIL"
        print(f"[{status}] {test_name}: {message}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def test_health_check(self):
        """Test if server is running"""
        try:
            response = self.client.get("/api/health")
            success = response.status_code == 200
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Health Check", False, f"Error: {e}")
            return False
    
    def test_get_all_attribute_queries(self):
        """Test GET /api/attribute-queries/all - should include end_date field"""
        print("testing here")
        try:
            response = self.client.get("/api/attribute-queries/all")
            success = response.status_code == 200
            
            if success and response.json():
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    # Check if end_date field is present
                    has_end_date = 'end_date' in data[0]
                    self.log_test("Get All Attribute Queries", has_end_date, 
                                f"end_date field present: {has_end_date}")
                    return has_end_date
                else:
                    self.log_test("Get All Attribute Queries", True, "Empty response")
                    return True
            else:
                self.log_test("Get All Attribute Queries", success, f"Status: {response.status_code}")
                return success
        except Exception as e:
            self.log_test("Get All Attribute Queries", False, f"Error: {e}")
            return False
    
    def test_get_attribute_query_by_id(self, query_id: int = 1):
        """Test GET /api/attribute-queries?id={id} - should include end_date field"""
        try:
            response = self.client.get(f"/api/attribute-queries?id={query_id}")
            success = response.status_code == 200

            if success and response.json():
                data = response.json()
                # Check if response has the expected structure
                if 'result' in data:
                    result = data['result']
                    has_end_date = 'end_date' in result
                    end_date_value = result.get('end_date', 'Not found')
                    self.log_test("Get Attribute Query By ID", has_end_date,
                                f"end_date field present: {has_end_date}, value: {end_date_value}")
                    return has_end_date
                else:
                    has_end_date = 'end_date' in data
                    self.log_test("Get Attribute Query By ID", has_end_date,
                                f"end_date field present: {has_end_date}")
                    return has_end_date
            else:
                self.log_test("Get Attribute Query By ID", success, f"Status: {response.status_code}")
                return success
        except Exception as e:
            self.log_test("Get Attribute Query By ID", False, f"Error: {e}")
            return False

    def test_update_end_date_valid(self, query_id: int = 1):
        """Test POST /api/attribute-queries/update/end-date with valid future date"""
        try:
            future_date = (datetime.now() + timedelta(days=60)).strftime("%Y-%m-%d")
            payload = {
                "id": query_id,
                "end_date": future_date
            }

            response = self.client.post("/api/attribute-queries/update/end-date", json=payload)
            success = response.status_code == 200

            message = f"Status: {response.status_code}"
            if response.text:
                message += f", Response: {response.text}"

            self.log_test("Update End Date (Valid)", success, message)
            return success
        except Exception as e:
            self.log_test("Update End Date (Valid)", False, f"Error: {e}")
            return False

    def test_update_end_date_invalid_past(self, query_id: int = 1):
        """Test POST /api/attribute-queries/update/end-date with past date (should fail)"""
        try:
            past_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            payload = {
                "id": query_id,
                "end_date": past_date
            }

            response = self.client.post("/api/attribute-queries/update/end-date", json=payload)
            # Should fail with 400 or 500
            success = response.status_code >= 400

            message = f"Status: {response.status_code} (expected error)"
            if response.text:
                message += f", Response: {response.text}"

            self.log_test("Update End Date (Invalid Past)", success, message)
            return success
        except Exception as e:
            self.log_test("Update End Date (Invalid Past)", False, f"Error: {e}")
            return False

    def test_update_end_date_decrease(self, query_id: int = 1):
        """Test POST /api/attribute-queries/update/end-date with decreased date (should fail)"""
        try:
            # First set a future date
            future_date = (datetime.now() + timedelta(days=90)).strftime("%Y-%m-%d")
            payload1 = {
                "id": query_id,
                "end_date": future_date
            }

            response1 = self.client.post("/api/attribute-queries/update/end-date", json=payload1)

            if response1.status_code == 200:
                # Now try to decrease it
                decreased_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
                payload2 = {
                    "id": query_id,
                    "end_date": decreased_date
                }

                response2 = self.client.post("/api/attribute-queries/update/end-date", json=payload2)
                # Should fail
                success = response2.status_code >= 400

                message = f"Status: {response2.status_code} (expected error)"
                if response2.text:
                    message += f", Response: {response2.text}"

                self.log_test("Update End Date (Decrease)", success, message)
                return success
            else:
                self.log_test("Update End Date (Decrease)", False, "Failed to set initial date")
                return False
        except Exception as e:
            self.log_test("Update End Date (Decrease)", False, f"Error: {e}")
            return False

    def test_register_attribute_with_end_date(self):
        """Test POST /api/attributes/register with end_date field"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            random_suffix = ''.join(random.choices(string.ascii_lowercase, k=6))
            future_date = (datetime.now() + timedelta(days=45)).strftime("%Y-%m-%d")

            payload = {
                "data_source_id": 1,
                "cron_expression": "0 */8 * * *",
                "query_text": f"SELECT user_id, test_attr_{random_suffix} FROM test_table_{timestamp}",
                "attributes": [
                    {
                        "name": f"Test Attribute {timestamp}",
                        "description": f"Test attribute created at {timestamp}",
                        "query_column_name": f"test_attr_{random_suffix}",
                        "data_type": "varchar",
                        "attribute_category": "METRIC",
                        "attribute_purpose": "GENERAL"
                    }
                ],
                "owner_id": 1,
                "entity_id": 1,
                "end_date": future_date
            }

            if self.is_test:
                self.log_test("Register Attribute with End Date", True,
                            f"Test payload prepared: {json.dumps(payload, indent=2)}")
                return True
            else:
                response = self.client.post("/api/attributes/register", json=payload)
                success = response.status_code == 200

                message = f"Status: {response.status_code}"
                if response.text:
                    message += f", Response: {response.text}"

                self.log_test("Register Attribute with End Date", success, message)
                return success
        except Exception as e:
            self.log_test("Register Attribute with End Date", False, f"Error: {e}")
            return False

    def test_database_migration(self):
        """Test that the database migration V5 was applied correctly"""
        try:
            # Check if end_date column exists in attribute_queries table
            sql = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'attribute_queries' AND column_name = 'end_date';
            """

            if self.is_test:
                self.log_test("Database Migration Check", True, f"Test SQL: {sql}")
                return True
            else:
                result = DatabaseHelper.execute_sql(sql, fetch=True)
                if result:
                    if isinstance(result, dict) and 'rows' in result:
                        # New psycopg2 format
                        has_end_date = len(result['rows']) > 0
                        if has_end_date:
                            row = result['rows'][0]
                            self.log_test("Database Migration Check", True,
                                        f"end_date column exists: {row}")
                        else:
                            self.log_test("Database Migration Check", False, "end_date column not found")
                        return has_end_date
                    elif isinstance(result, str) and 'end_date' in result:
                        # Fallback docker format
                        self.log_test("Database Migration Check", True, "end_date column exists")
                        return True
                    else:
                        self.log_test("Database Migration Check", False, "end_date column not found")
                        return False
                else:
                    self.log_test("Database Migration Check", False, "No result from database query")
                    return False
        except Exception as e:
            self.log_test("Database Migration Check", False, f"Error: {e}")
            return False

    def test_filtered_queries_by_end_date(self):
        """Test that expired queries are filtered out from GET /all endpoint"""
        try:
            # This test checks if queries with end_date <= NOW() are excluded
            response = self.client.get("/api/attribute-queries/all")
            success = response.status_code == 200

            if success and response.json():
                data = response.json()
                # All returned queries should have end_date in the future or null
                all_valid = True
                for query in data:
                    if 'end_date' in query and query['end_date']:
                        end_date = datetime.fromisoformat(query['end_date'].replace('Z', '+00:00'))
                        if end_date <= datetime.now():
                            all_valid = False
                            break

                self.log_test("Filtered Queries by End Date", all_valid,
                            f"All {len(data)} queries have valid end_dates")
                return all_valid
            else:
                self.log_test("Filtered Queries by End Date", success, f"Status: {response.status_code}")
                return success
        except Exception as e:
            self.log_test("Filtered Queries by End Date", False, f"Error: {e}")
            return False

    def test_destination_sync_end_date_validation(self):
        """Test destination sync end_date validation logic"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            random_suffix = ''.join(random.choices(string.ascii_lowercase, k=6))

            # Test 1: Valid end_date (before attribute end_date)
            valid_end_date = "2099-06-01"  # Before the attribute end_date of 2099-12-31

            appsflyer_payload = {
                "destination_type": "APPSFLYER",
                "destination_id": 1,  # Sample Destination (APPSFLYER)
                "segment_id": 1,      # Sample Segment
                "sync_strategy": "FULL",
                "cron_expression": "0 0 * * *",
                "owner_id": 1,
                "tg_cg_applied": False,
                "tg_cg_conditions": {},
                "appsflyer_audience": {
                    "name": f"Test Audience {timestamp}_{random_suffix}",
                    "platform": "android",
                    "description": f"Test audience created at {timestamp}"
                },
                "end_date": valid_end_date
            }

            feature_store_payload = {
                "destination_type": "FEATURE_STORE",
                "destination_id": 2,  # Feature Store
                "segment_id": 1,      # Sample Segment
                "sync_strategy": "FULL",
                "cron_expression": "0 0 * * *",
                "owner_id": 1,
                "tg_cg_applied": False,
                "tg_cg_conditions": {},
                "feature_config": {
                    "feature_name": f"test_feature_{timestamp}_{random_suffix}",
                    "description": f"Test feature created at {timestamp}",
                    "is_active": True,
                    "context_type": "user",
                    "context_name": "user_id",
                    "feature_type": "boolean",
                    "is_verified": False,
                    "is_pii": False,
                    "destinations": ["feature_store"]
                },
                "end_date": valid_end_date
            }

            # Test 2: Invalid end_date (after attribute end_date)
            invalid_end_date = "2100-01-01"  # After the attribute end_date of 2099-12-31

            invalid_payload = {
                "destination_type": "APPSFLYER",
                "destination_id": 1,
                "segment_id": 1,
                "sync_strategy": "FULL",
                "cron_expression": "0 0 * * *",
                "owner_id": 1,
                "tg_cg_applied": False,
                "tg_cg_conditions": {},
                "appsflyer_audience": {
                    "name": f"Invalid Test Audience {timestamp}_{random_suffix}",
                    "platform": "android",
                    "description": f"Invalid test audience created at {timestamp}"
                },
                "end_date": invalid_end_date
            }

            if self.is_test:
                self.log_test("Destination Sync End Date Validation", True,
                            f"Test payloads prepared:\n" +
                            f"Valid AppsFlyer: {json.dumps(appsflyer_payload, indent=2)}\n" +
                            f"Valid Feature Store: {json.dumps(feature_store_payload, indent=2)}\n" +
                            f"Invalid: {json.dumps(invalid_payload, indent=2)}")
                return True
            else:
                # Test valid AppsFlyer destination sync
                response1 = self.client.post("/api/destination-sync/register", json=appsflyer_payload)
                appsflyer_success = response1.status_code == 200

                # Test valid Feature Store destination sync
                response2 = self.client.post("/api/destination-sync/register", json=feature_store_payload)
                feature_store_success = response2.status_code == 200

                # Test invalid destination sync (should fail)
                response3 = self.client.post("/api/destination-sync/register", json=invalid_payload)
                invalid_rejected = response3.status_code != 200

                overall_success = appsflyer_success and feature_store_success and invalid_rejected

                message = f"AppsFlyer: {response1.status_code}, Feature Store: {response2.status_code}, Invalid: {response3.status_code}"
                if response1.text:
                    message += f"\nAppsFlyer Response: {response1.text}"
                if response2.text:
                    message += f"\nFeature Store Response: {response2.text}"
                if response3.text:
                    message += f"\nInvalid Response: {response3.text}"

                self.log_test("Destination Sync End Date Validation", overall_success, message)
                return overall_success

        except Exception as e:
            self.log_test("Destination Sync End Date Validation", False, f"Error: {e}")
            return False

    def test_destination_sync_update_end_date(self):
        """Test destination sync end_date update functionality"""
        try:
            # Test updating destination sync end_date
            update_payload = {
                "id": 1,  # Assuming there's a destination sync with ID 1
                "end_date": "2099-07-01",  # Valid date before attribute end_date
                "destination_type": "APPSFLYER",
                "owner_id": 1
            }

            invalid_update_payload = {
                "id": 1,
                "end_date": "2100-01-01",  # Invalid date after attribute end_date
                "destination_type": "APPSFLYER",
                "owner_id": 1
            }

            if self.is_test:
                self.log_test("Destination Sync Update End Date", True,
                            f"Test payloads prepared:\n" +
                            f"Valid: {json.dumps(update_payload, indent=2)}\n" +
                            f"Invalid: {json.dumps(invalid_update_payload, indent=2)}")
                return True
            else:
                # Test valid update
                response1 = self.client.post("/api/destination-sync/update/end-date", json=update_payload)
                valid_success = response1.status_code == 200

                # Test invalid update (should fail)
                response2 = self.client.post("/api/destination-sync/update/end-date", json=invalid_update_payload)
                invalid_rejected = response2.status_code != 200

                overall_success = valid_success and invalid_rejected

                message = f"Valid Update: {response1.status_code}, Invalid Update: {response2.status_code}"
                if response1.text:
                    message += f"\nValid Response: {response1.text}"
                if response2.text:
                    message += f"\nInvalid Response: {response2.text}"

                self.log_test("Destination Sync Update End Date", overall_success, message)
                return overall_success

        except Exception as e:
            self.log_test("Destination Sync Update End Date", False, f"Error: {e}")
            return False

    def run_all_tests(self):
        """Run all tests and return summary"""
        print("=" * 60)
        print("STARTING END_DATE FEATURE TESTS")
        print("=" * 60)

        # Basic connectivity tests
        if not self.test_health_check():
            print("❌ Server not accessible, stopping tests")
            return False

        # Database tests
        self.test_database_migration()

        # API tests
        self.test_get_all_attribute_queries()
        self.test_get_attribute_query_by_id()
        self.test_filtered_queries_by_end_date()

        # End date update tests
        self.test_update_end_date_valid()
        self.test_update_end_date_invalid_past()
        self.test_update_end_date_decrease()

        # Registration tests
        self.test_register_attribute_with_end_date()

        # Destination sync tests
        self.test_destination_sync_end_date_validation()
        self.test_destination_sync_update_end_date()

        # Print summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)

        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)

        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test']}")
            if result['message']:
                print(f"   {result['message']}")

        print(f"\nPassed: {passed}/{total}")

        if passed == total:
            print("🎉 All tests passed!")
        else:
            print(f"⚠️  {total - passed} tests failed")

        return passed == total

def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(description='Test end_date feature implementation')
    parser.add_argument('--test-mode', action='store_true',
                       help='Run in test mode (no actual API calls or DB operations)')
    parser.add_argument('--server-url', default='http://localhost:8000',
                       help='Server URL (default: http://localhost:8000)')

    args = parser.parse_args()

    print(f"Running tests against: {args.server_url}")
    print(f"Test mode: {'ON' if args.test_mode else 'OFF'}")

    if not args.test_mode:
        print("\n⚠️  WARNING: This will make actual API calls and database operations!")
        print("Make sure the server is running with: dcb && dcu")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Aborted.")
            return

    # Initialize test client
    client = CDPTestClient(args.server_url)

    # Run tests
    tester = EndDateFeatureTests(client, is_test=args.test_mode)
    success = tester.run_all_tests()

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
