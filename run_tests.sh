#!/bin/bash

# End Date Feature Test Runner
# This script provides easy commands to test the end_date feature implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    print_status "Checking if server is running on localhost:8000..."
    if curl -s -f http://localhost:8000/api/health > /dev/null 2>&1; then
        print_success "Server is running"
        return 0
    else
        print_error "Server is not running on localhost:8000"
        return 1
    fi
}

# Function to start the server
start_server() {
    print_status "Starting CDP platform server..."
    print_warning "This will run: dcb && dcu"
    
    if command -v dcb >/dev/null 2>&1 && command -v dcu >/dev/null 2>&1; then
        dcb && dcu
    else
        print_warning "dcb/dcu commands not found, trying docker-compose..."
        docker-compose build && docker-compose up -d
    fi
    
    # Wait for server to be ready
    print_status "Waiting for server to be ready..."
    for i in {1..30}; do
        if check_server; then
            print_success "Server is ready!"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    print_error "Server failed to start or is not responding"
    return 1
}

# Function to run tests in test mode (dry run)
run_test_mode() {
    print_status "Running tests in TEST MODE (dry run)..."
    python3 test_end_date_feature.py --test-mode
}

# Function to run actual tests
run_real_tests() {
    print_status "Running REAL tests against the server..."
    print_warning "This will make actual API calls and database operations!"
    
    if ! check_server; then
        print_error "Server is not running. Start it first with: $0 start"
        return 1
    fi
    
    python3 test_end_date_feature.py
}

# Function to show database schema
show_schema() {
    print_status "Showing attribute_queries table schema..."
    docker exec -it cdp-platform-postgres psql -U cdp -d cdp_platform -c "
        \d attribute_queries
    "
}

# Function to show sample data
show_data() {
    print_status "Showing sample attribute_queries data..."
    docker exec -it cdp-platform-postgres psql -U cdp -d cdp_platform -c "
        SELECT id, data_source_id, owner_id, cron_expression, 
               end_date, created_at, updated_at 
        FROM attribute_queries 
        ORDER BY id 
        LIMIT 5;
    "
}

# Function to insert test data
insert_test_data() {
    print_status "Inserting test data for end_date testing..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    FUTURE_DATE=$(date -d "+30 days" "+%Y-%m-%d %H:%M:%S+05:30")
    
    docker exec -it cdp-platform-postgres psql -U cdp -d cdp_platform -c "
        INSERT INTO attribute_queries (
            data_source_id, owner_id, cron_expression, query_text, entity_id, end_date
        ) VALUES (
            1, 1, '0 */6 * * *', 
            'SELECT user_id, test_column_${TIMESTAMP} FROM test_table_${TIMESTAMP}',
            1, '${FUTURE_DATE}'
        ) RETURNING id, end_date;
    "
    
    print_success "Test data inserted with end_date: ${FUTURE_DATE}"
}

# Function to clean up test data
cleanup_test_data() {
    print_status "Cleaning up test data..."
    docker exec -it cdp-platform-postgres psql -U cdp -d cdp_platform -c "
        DELETE FROM attribute_queries 
        WHERE query_text LIKE '%test_column_%' OR query_text LIKE '%test_table_%';
    "
    print_success "Test data cleaned up"
}

# Function to show help
show_help() {
    echo "End Date Feature Test Runner"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start           Start the CDP platform server (dcb && dcu)"
    echo "  check           Check if server is running"
    echo "  test            Run tests in test mode (dry run)"
    echo "  run             Run actual tests against the server"
    echo "  schema          Show attribute_queries table schema"
    echo "  data            Show sample attribute_queries data"
    echo "  insert          Insert test data for testing"
    echo "  cleanup         Clean up test data"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start        # Start the server"
    echo "  $0 test         # Run dry run tests"
    echo "  $0 run          # Run real tests"
    echo "  $0 insert       # Insert test data"
    echo "  $0 cleanup      # Clean up test data"
}

# Main script logic
case "${1:-help}" in
    "start")
        start_server
        ;;
    "check")
        check_server
        ;;
    "test")
        run_test_mode
        ;;
    "run")
        run_real_tests
        ;;
    "schema")
        show_schema
        ;;
    "data")
        show_data
        ;;
    "insert")
        insert_test_data
        ;;
    "cleanup")
        cleanup_test_data
        ;;
    "help"|*)
        show_help
        ;;
esac
