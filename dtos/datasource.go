package dtos

import (
	"encoding/json"

	"github.com/Zomato/cdp-platform/internal/enum"
)

type DataSource struct {
	Base
	TenantID           int64           `json:"tenant_id"`
	Name               string          `json:"name"`
	ConfigMap          json.RawMessage `json:"config_map"`
	DataSourceConfigID int64           `json:"data_source_config_id"`
}

type DataSourceInfo struct {
	Base
	Name           string              `json:"name,omitempty" db:"name"`
	DataSourceType enum.DataSourceType `json:"data_source_type,omitempty" db:"data_source_type"`
	ConfigMap      json.RawMessage     `json:"config_map,omitempty" db:"config_map"`
}

type ExpiredSyncRunTablesResponse struct {
	ExpiredTables []string `json:"expired_tables"`
}
