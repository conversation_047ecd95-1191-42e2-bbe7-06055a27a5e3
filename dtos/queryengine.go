package dtos

type QueryEngineInput struct {
	DataSourceID int64  `json:"data_source_id"`
	Query        string `json:"query"`
}

type PreviewResultRow map[string]interface{}

type ColumnInfo struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type PreviewSQLResponse struct {
	Columns []ColumnInfo       `json:"columns"`
	Rows    []PreviewResultRow `json:"rows"`
	Meta    PreviewMeta        `json:"meta"`
}

type PreviewMeta struct {
	RowCount         int32 `json:"rowCount"`
	ColumnCount      int32 `json:"columnCount"`
	ProcessingTimeMS int64 `json:"processingTimeMS"`
}

type PreviewColumn struct {
	Columns []ColumnInfo `json:"columns"`
}
