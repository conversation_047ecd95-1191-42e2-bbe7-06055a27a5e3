package dtos

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

type Condition struct {
	Type            string      `json:"type,omitempty"` // "simple" or "complex"
	AttributeId     *int64      `json:"attribute_id,omitempty"`
	Operator        *string     `json:"operator,omitempty"`
	ConditionValue  interface{} `json:"value,omitempty"`
	LogicalOperator *string     `json:"logical_operator,omitempty"`
	Conditions      []Condition `json:"conditions,omitempty"`
	IsFilter        *bool       `json:"is_filter,omitempty"`
	EntityId        *int64      `json:"entity_id,omitempty"`
}

func (c *Condition) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to scan Condition")
	}
	return json.Unmarshal(bytes, c)
}

func (c Condition) ToValue() (driver.Value, error) {
	return json.Marshal(c)
}

type Segment struct {
	Base
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description,omitempty" db:"description"`
	Conditions  *Condition `json:"conditions,omitempty" db:"conditions"`
	OwnerName   string     `json:"owner_name,omitempty" db:"owner_name"`
	OwnerID     int64      `json:"owner_id,omitempty" db:"owner_id"`
	LastRefresh *time.Time `json:"last_refresh,omitempty" db:"last_refresh"`
}

type SegmentSize struct {
	Count int64 `json:"count"`
}

type SegmentDownstreamDestinationsResponse struct {
	DestinationType string `json:"destination_type" db:"destination_type"`
	DestinationName string `json:"destination_name" db:"destination_name"`
	AudienceName    string `json:"audience_name" db:"audience_name"`
}

type SegmentSizeTgCgRequest struct {
	Base
	TgCgConditions *Condition `json:"tg_cg_conditions,omitempty" db:"conditions"`
}

type SegmentSizeTgCgResponse struct {
	OriginalSize    int64   `json:"original_size"`
	FilteredSize    int64   `json:"filtered_size"`
	CoveragePercent float64 `json:"coverage_percent"`
}

type UpdateSegmentRequest struct {
	ID          int64      `json:"id" binding:"required"`
	Name        *string    `json:"name,omitempty"`
	Description *string    `json:"description,omitempty"`
	Conditions  *Condition `json:"conditions,omitempty"`
}
