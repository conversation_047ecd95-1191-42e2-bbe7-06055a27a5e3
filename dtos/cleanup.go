package dtos

import (
	"time"
)

type ScheduleAttributeQueriesCleanupRequest struct {
	DatasourceID      int64  `json:"datasource_id" binding:"required"`
	RetentionRunCount int    `json:"retention_run_count" binding:"required,min=1"`
	CronExpression    string `json:"cron_expression" binding:"required"`
}

type TriggerAttributeQueriesCleanupRequest struct {
	DatasourceID      int64 `json:"datasource_id" binding:"required"`
	RetentionRunCount int   `json:"retention_run_count" binding:"required,min=1"`
}

type ScheduleAttributeQueriesCleanupResponse struct {
	Message   string    `json:"message,omitempty"`
	CreatedAt time.Time `json:"created_at"`
}

type TriggerAttributeQueriesCleanupResponse struct {
	TriggeredAt time.Time `json:"triggered_at"`
	Message     string    `json:"message,omitempty"`
}

type AttributeQueriesCleanupWorkflowResult struct {
	Message       string   `json:"message"`
	TablesDropped int      `json:"tables_dropped"`
	Errors        []string `json:"errors,omitempty"`
	SkippedReason string   `json:"skipped_reason,omitempty"`
}
