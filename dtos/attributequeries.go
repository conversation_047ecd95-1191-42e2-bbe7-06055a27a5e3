package dtos

import "time"

type AttributeQueryInfo struct {
	Base
	CronExpression string              `json:"cron_expression,omitempty" db:"cron_expression"`
	QueryText      string              `json:"query_text,omitempty" db:"query_text"`
	DataSourceID   int64               `json:"data_source_id" db:"data_source_id"`
	OwnerID        *int64              `json:"owner_id" db:"owner_id"`
	CurrentTable   *string             `json:"current_table,omitempty" db:"sync_table_name"` // '*' to fix `converting NULL to string is unsupported` while quering
	LatestStatus   *string             `json:"latest_status,omitempty" db:"latest_status"`   // '*' to fix `converting NULL to string is unsupported` while quering
	Attributes     []AttributeInfo     `json:"attributes,omitempty"`
	RunLogs        []AttributeSyncRuns `json:"run_logs,omitempty"`
	NextRuns       []time.Time         `json:"next_runs,omitempty"`
	EntityID       int64               `json:"entity_id" db:"entity_id"`
	EndDate        *time.Time          `json:"end_date" db:"end_date"`
}

type ValidateAttributeQueryRequest struct {
	Query        string `json:"query" binding:"required"`
	DataSourceID int64  `json:"data_source_id"`
	EntityID     int64  `json:"entity_id"`
}

type ValidateAttributeQueryResponse struct {
	ExistingColumns []string `json:"existing_columns"`
	ContainsUserID  bool     `json:"contains_user_id"`
	IsValid         bool     `json:"is_valid"`
}

type AttributeQueryUpdateQueryRequest struct {
	Base
	QueryText string `json:"query_text,omitempty" db:"query_text"`
}

type AttributeQueryUpdateEndDateRequest struct {
	Base
	EndDate string `json:"end_date" binding:"required" db:"end_date"`
}

type AttributeQueryUpdateCronRequest struct {
	Base
	CronExpression string `json:"cron_expression,omitempty" db:"cron_expression"`
}

type AttributeQueryUpdateQueryResponse struct {
	SchemasMatch   bool            `json:"schemas_match"`
	ChangedColumns []*ColumnChange `json:"changed_columns"`
}

type ColumnChange struct {
	ColumnName   string `json:"column_name"`
	ExistingType string `json:"existing_type"`
	UpdatedType  string `json:"updated_type"`
}
