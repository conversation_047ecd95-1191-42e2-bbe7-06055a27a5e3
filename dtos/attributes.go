package dtos

type AttributeType string

const (
	AttributeTypeBase    AttributeType = "BASE"
	AttributeTypeDerived AttributeType = "DERIVED"
)

type DerivedAttributeType string

const (
	DerivedAttributeTypeTGCG        DerivedAttributeType = "TGCG"
	DerivedAttributeTypeGeneral     DerivedAttributeType = "GENERAL"
	DerivedAttributeTypeArithmetic  DerivedAttributeType = "ARITHMETIC"
	DerivedAttributeTypeConditional DerivedAttributeType = "CONDITIONAL"
	DerivedAttributeTypeFunctional  DerivedAttributeType = "FUNCTIONAL"
)

type AttributeCategory string

const (
	AttributeCategoryMetric    AttributeCategory = "METRIC"
	AttributeCategoryDimension AttributeCategory = "DIMENSION"
	AttributeCategoryProperty  AttributeCategory = "PROPERTY"
)

type AttributePurpose string

const (
	AttributePurposeCondition AttributePurpose = "CONDITION"
	AttributePurposeFilter    AttributePurpose = "FILTER"
)

type Attributes struct {
	Base
	Name          string        `json:"name" db:"name"`
	Description   string        `json:"description" db:"description"`
	AttributeType AttributeType `json:"attribute_type" db:"attribute_type"`
	OwnerID       int64         `json:"owner_id" db:"owner_id"`
}

type AttributeList struct {
	Base
	Attributes []Attributes `json:"attributes"`
}

type BaseAttributeInfo struct {
	Base
	Name              string            `json:"name" db:"name"`
	Description       string            `json:"description" db:"description"`
	AttributeType     AttributeType     `json:"attribute_type" db:"attribute_type"`
	AttributeCategory AttributeCategory `json:"attribute_category,omitempty" db:"attribute_category"`
	OwnerID           int64             `json:"owner_id" db:"owner_id"`
	QueryColumnName   string            `json:"query_column_name" db:"query_column_name"`
	DataType          string            `json:"data_type" db:"data_type"`
	AttributeQueryId  int64             `json:"attribute_query_id" db:"attribute_query_id"`
	QueryText         string            `json:"query_text" db:"query_text"`
	CronExpression    *string           `json:"cron_expression" db:"cron_expression"`
	DataSourceID      int64             `json:"data_source_id" db:"data_source_id"`
	EntityID          int64             `json:"entity_id" db:"entity_id"`
	AttributePurpose  AttributePurpose  `json:"attribute_purpose" db:"attribute_purpose"`
}

type SourceAttributeRef struct {
	ID       int64  `json:"id,omitempty" db:"id"`
	Name     string `json:"name,omitempty" db:"name"`
	Position int64  `json:"position,omitempty" db:"position"`
}

type DerivedAttributeInfo struct {
	Base
	Name                 string               `json:"name" db:"name"`
	Description          string               `json:"description" db:"description"`
	AttributeType        AttributeType        `json:"attribute_type" db:"attribute_type"`
	AttributeCategory    AttributeCategory    `json:"attribute_category,omitempty" db:"attribute_category"`
	OwnerID              int64                `json:"owner_id" db:"owner_id"`
	DataType             string               `json:"data_type" db:"data_type"`
	Expression           *string              `json:"expression" db:"expression"`
	DerivedAttributeType DerivedAttributeType `json:"derived_attribute_type" db:"derived_attribute_type"`
	SourceAttributes     []SourceAttributeRef `json:"source_attributes"`
	AttributePurpose     AttributePurpose     `json:"attribute_purpose" db:"attribute_purpose"`
	ReadableExpression   *string              `json:"readable_expression" db:"readable_expression"`
	EntityID             int64                `json:"entity_id" db:"entity_id"`
}

type AttributeInfo struct {
	Base
	Name                 string               `json:"name,omitempty" db:"name"`
	Description          string               `json:"description,omitempty" db:"description"`
	AttributeType        AttributeType        `json:"attribute_type,omitempty" db:"attribute_type"`
	AttributeCategory    AttributeCategory    `json:"attribute_category,omitempty" db:"attribute_category"`
	OwnerID              int64                `json:"owner_id,omitempty" db:"owner_id"`
	QueryColumnName      string               `json:"query_column_name,omitempty" db:"query_column_name"`
	DataType             string               `json:"data_type,omitempty" db:"data_type"`
	AttributeQueryId     int64                `json:"attribute_query_id,omitempty" db:"attribute_query_id"`
	QueryText            string               `json:"query_text,omitempty" db:"query_text"`
	CronExpression       *string              `json:"cron_expression,omitempty" db:"cron_expression"`
	DataSourceID         int64                `json:"data_source_id,omitempty" db:"data_source_id"`
	Expression           *string              `json:"expression,omitempty" db:"expression"`
	DerivedAttributeType DerivedAttributeType `json:"derived_attribute_type,omitempty" db:"derived_attribute_type"`
	SourceAttributes     []SourceAttributeRef `json:"source_attributes,omitempty"`
	SourceAttributeIds   []int64              `json:"source_attribute_ids,omitempty"`
	Segments             []Segment            `json:"segments,omitempty"`
	AttributePurpose     AttributePurpose     `json:"attribute_purpose,omitempty" db:"attribute_purpose"`
	EntityID             int64                `json:"entity_id,omitempty" db:"entity_id"`
	ReadableExpression   *string              `json:"readable_expression,omitempty" db:"readable_expression"`
	TableName            *string              `json:"table_name,omitempty" db:"table_name"`
}

type ValidateAttributeNames struct {
	AttributeNames []string `json:"attribute_names"`
}

type ValidateAttributeNameResponse struct {
	ExistingColumns []string `json:"existing_columns"`
	IsValid         bool     `json:"is_valid"`
}

type RegisterAttributeRequestAttributeInfo struct {
	Name              string            `json:"name"`
	ColumnName        string            `json:"column_name"`
	Description       string            `json:"description"`
	DataType          string            `json:"data_type"`
	AttributeCategory AttributeCategory `json:"attribute_category" binding:"required"`
}

type RegisterAttributeRequest struct {
	DataSourceID   int64                                   `json:"data_source_id"`
	CronExpression string                                  `json:"cron_expression"`
	QueryText      string                                  `json:"query_text"`
	Attributes     []RegisterAttributeRequestAttributeInfo `json:"attributes"`
	OwnerID        int64                                   `json:"owner_id"`
	EntityID       int64                                   `json:"entity_id"`
	EndDate        *string                                 `json:"end_date,omitempty"`
}

type AttributeDistinctValues struct {
	AttributeId   int64         `json:"attribute_id"`
	DistinctCount int64         `json:"distinct_count"`
	DataType      string        `json:"data_type"`
	Values        []interface{} `json:"values"`
}
