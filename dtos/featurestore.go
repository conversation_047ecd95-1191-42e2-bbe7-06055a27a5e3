package dtos

type RunPersonasInput struct {
	WorkflowName   string `json:"workflow_name"`
	Context        string `json:"context"`
	PropertiesPath string `json:"properties_path"`
	Tenant         string `json:"tenant"`
}

// FeatureStoreConfig represents the configuration for a feature store destination
type FeatureStoreConfig struct {
	Tenant    string `json:"tenant"`
	Namespace string `json:"namespace"`
	Entity    string `json:"entity"`
}

// FeatureStoreUser represents a user in the feature store

// CreateFeatureRequest represents a request to create a feature in the feature store
type CreateFeatureRequest struct {
	IsActive     *bool     `json:"is_active"`
	ContextType  *string   `json:"context_type"`
	ContextName  *string   `json:"context_name"`
	FeatureName  *string   `json:"feature_name"`
	FeatureType  *string   `json:"feature_type"`
	IsVerified   *bool     `json:"is_verified"`
	IsPII        *bool     `json:"is_pii"`
	Destinations *[]string `json:"destinations,omitempty"`
	Description  *string   `json:"description"`
	Expiry       *string   `json:"expiry"`
}

// CreateFeatureResponse represents a response from creating a feature in the feature store
type CreateFeatureResponse struct {
	Status    string `json:"status"`
	FeatureID int64  `json:"feature_id"`
	Message   string `json:"message"`
}

// GetFeatureRequest represents a request to get a feature from the feature store
type GetFeatureRequest struct {
	DestinationID int64  `json:"destination_id"`
	ContextName   string `json:"context_name"`
	FeatureName   string `json:"feature_name"`
}

// FeatureStoreSegmentInfo represents feature store segment information
type FeatureStoreSegmentInfo struct {
	DestinationSyncID        int64   `db:"destination_sync_id"`
	LastDestinationSyncRunID *int64  `db:"last_destination_sync_run_id"`
	CommitedSegmentTableName *string `db:"commited_segment_table_name"`
	Tenant                   string  `db:"tenant"`
	Namespace                string  `db:"namespace"`
	ContextType              string  `db:"context_type"`
	ContextName              string  `db:"context_name"`
	Entity                   string  `db:"entity"`
	FeatureName              string  `db:"feature_name"`
	FeatureType              string  `db:"feature_type"`
	SegmentName              string  `db:"segment_name"`
}

// SegmentYAML represents the YAML structure for segment configuration
type SegmentYAML struct {
	Context        string            `yaml:"context"`
	Entity         string            `yaml:"entity"`
	EntityIdColumn string            `yaml:"entityIdColumn"`
	PropType       string            `yaml:"propType"`
	TTLInDays      int               `yaml:"ttlInDays,omitempty"`
	Properties     []SegmentProperty `yaml:"properties"`
}

// SegmentProperty represents a property in the segment YAML
type SegmentProperty struct {
	Schema  string   `yaml:"schema"`
	Table   string   `yaml:"table"`
	Columns []string `yaml:"columns"`
}

type UpdateFeatureRequest struct {
	IsActive        *bool   `json:"is_active,omitempty"`
	Expiry          *string `json:"expiry,omitempty"`
	CreatedAt       *string `json:"created_at,omitempty"`
	UpdatedByUserID *int64  `json:"updated_by_user_id,omitempty"`
}

type UpdateFeatureResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}
