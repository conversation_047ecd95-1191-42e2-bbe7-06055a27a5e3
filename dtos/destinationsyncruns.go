package dtos

import "time"

type DestinationSyncRun struct {
	Base
	DestinationSyncID      int64      `json:"destination_sync_id,omitempty" db:"destination_sync_id"`
	ExecutionStart         *time.Time `json:"execution_start,omitempty" db:"execution_start"`
	ExecutionEnd           *time.Time `json:"execution_end,omitempty" db:"execution_end"`
	Status                 string     `json:"status,omitempty" db:"status"`
	SyncTableName          *string    `json:"sync_table_name,omitempty" db:"sync_table_name"`
	RowsAffected           *int       `json:"rows_affected,omitempty" db:"rows_affected"`
	ErrorMessage           *string    `json:"error_message,omitempty" db:"error_message"`
	FeatureStoreSparkRunID *string    `json:"feature_store_spark_run_id,omitempty" db:"feature_store_spark_run_id"`
}
