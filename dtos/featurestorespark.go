package dtos

import "time"

// FeatureStoreSparkRun represents a single run of the personas processing workflow
type FeatureStoreSparkRun struct {
	Base
	RunID          *string    `json:"run_id"`
	ExecutionStart *time.Time `json:"execution_start,omitempty"`
	ExecutionEnd   *time.Time `json:"execution_end,omitempty"`
	Status         string     `json:"status"`
	ErrorMessage   *string    `json:"error_message,omitempty"`
}

// FeatureStoreSparkDestinationSyncRunMapping represents the mapping between feature store spark runs and destination sync runs
type FeatureStoreSparkDestinationSyncRunMapping struct {
	Base
	FeatureStoreSparkRunID int64 `json:"feature_store_spark_run_id"`
	DestinationSyncRunID   int64 `json:"destination_sync_run_id"`
}
