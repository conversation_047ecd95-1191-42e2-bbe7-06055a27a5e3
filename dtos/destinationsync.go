package dtos

import (
	"encoding/json"
	"time"
)

type DestinationSync struct {
	Base
	SegmentID             int64   `json:"segment_id"`
	DestinationID         int64   `json:"destination_id"`
	CommittedSegmentTable string  `json:"committed_segment_table"`
	OngoingSegmentTable   string  `json:"ongoing_segment_table"`
	SyncStrategy          string  `json:"sync_strategy"`
	CronExpression        string  `json:"cron_expression"`
	OwnerID               int64   `json:"owner_id"`
	EndDate               *string `json:"end_date,omitempty" db:"end_date"`
}

type AppsflyerAudienceDetails struct {
	ImportKey string
	Platform  string
}

type DestinationSyncInfo struct {
	Base                `db:",inline"`
	SegmentName         string               `db:"segment_name" json:"segment_name"`
	SegmentID           int64                `db:"segment_id" json:"segment_id"`
	OwnerID             int64                `db:"owner_id" json:"owner_id"`
	DestinationType     string               `db:"destination_type" json:"destination_type"`
	SyncStrategy        string               `db:"sync_strategy" json:"sync_strategy"`
	CronExpression      string               `db:"cron_expression" json:"cron_expression"`
	OwnerName           string               `db:"owner_name" json:"owner_name"`
	LastSyncRows        *int                 `db:"last_sync_rows" json:"last_sync_rows,omitempty"`
	LastSyncStatus      *string              `db:"last_sync_status" json:"last_sync_status,omitempty"`
	LastSyncEndTime     *time.Time           `db:"last_sync_end_time" json:"last_sync_end_time,omitempty"`
	AudienceName        *string              `db:"audience_name" json:"audience_name,omitempty"`
	TgCgConditions      *Condition           `db:"tg_cg_conditions" json:"tg_cg_conditions,omitempty"`
	DestinationSyncRuns []DestinationSyncRun `db:"destination_sync_runs" json:"destination_sync_runs,omitempty"`
	NextScheduledRuns   []time.Time          `json:"next_scheduled_runs,omitempty"`
	SnapshotTableName   *string              `db:"snapshot_table_name" json:"snapshot_table_name,omitempty"`
	EndDate             *string              `db:"end_date" json:"end_date,omitempty"`
}

type AppsflyerAudienceConfig struct {
	Name        string `json:"name"`
	Platform    string `json:"platform"`
	Description string `json:"description"`
}

type RegisterDestinationSyncRequest struct {
	DestinationType   string                   `json:"destination_type"`
	DestinationID     int64                    `json:"destination_id"`
	SegmentID         int64                    `json:"segment_id"`
	SyncStrategy      string                   `json:"sync_strategy"`
	CronExpression    string                   `json:"cron_expression"`
	OwnerID           int64                    `json:"owner_id"`
	TgCgApplied       bool                     `json:"tg_cg_applied"`
	TgCgConditions    json.RawMessage          `json:"tg_cg_conditions"`
	AppsflyerAudience *AppsflyerAudienceConfig `json:"appsflyer_audience,omitempty"`
	FeatureConfig     *CreateFeatureRequest    `json:"feature_config,omitempty"`
	EndDate           *string                  `json:"end_date,omitempty"`
}

type ValidateDestinationSyncRequest struct {
	SegmentID       int64  `json:"segment_id" binding:"required"`
	DestinationType string `json:"destination_type" binding:"required"`
}

type ValidateDestinationSyncResponse struct {
	IsValid bool   `json:"is_valid"`
	Message string `json:"message,omitempty"`
}

type ValidateAppsflyerAudienceNameRequest struct {
	Name string `json:"name" binding:"required"`
}

type ValidateFeatureStoreSegmentRequest struct {
	FeatureName   string `json:"feature_name" binding:"required"`
	DestinationID int64  `json:"destination_id" binding:"required"`
}

type ValidateAudienceResponse struct {
	IsValid bool   `json:"is_valid"`
	Message string `json:"message,omitempty"`
}

type DestinationSyncUpdateCronRequest struct {
	Base
	CronExpression  string `json:"cron_expression" binding:"required" db:"cron_expression"`
	DestinationType string `json:"destination_type" binding:"required"`
}

type DestinationSyncUpdateTgCgRequest struct {
	Base
	Conditions Condition `json:"tg_cg_conditions" binding:"required"`
}

type ValidateDestinationSyncSizeRequest struct {
	SegmentID       int64      `json:"segment_id" binding:"required"`
	DestinationType string     `json:"destination_type" binding:"required"`
	TgCgConditions  *Condition `json:"tg_cg_conditions,omitempty" db:"conditions"`
}

type DestinationSyncUpdateEndDateRequest struct {
	Base
	EndDate         string `json:"end_date" binding:"required" db:"end_date"`
	DestinationType string `json:"destination_type" binding:"required"`
	OwnerID         int64  `json:"owner_id" binding:"required"`
}
