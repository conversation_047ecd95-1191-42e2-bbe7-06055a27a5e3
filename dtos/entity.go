package dtos

type EntityType string

const (
	EntityTypeBase    EntityType = "BASE"
	EntityTypeDerived EntityType = "DERIVED"
)

type EntityInfo struct {
	Base
	Name           *string `json:"name,omitempty" db:"name"`
	Description    *string `json:"description,omitempty" db:"description"`
	Type           *string `json:"type,omitempty" db:"type"`
	TableName      *string `json:"table_name,omitempty" db:"table_name"`
	SchemaName     *string `json:"schema_name,omitempty" db:"schema_name"`
	CatalogName    *string `json:"catalog_name,omitempty" db:"catalog_name"`
	RequiresFilter *bool   `json:"requires_filter,omitempty" db:"requires_filter"`
	OwnerID        *int64  `json:"owner_id,omitempty" db:"owner_id"`
}

// JoinInfo represents join information between two entities
type JoinInfo struct {
	SourceEntityId int64  `db:"source_entity_id"`
	TargetEntityId int64  `db:"target_entity_id"`
	SourceColumn   string `db:"source_column"`
	TargetColumn   string `db:"target_column"`
	JoinType       string `db:"join_type"`
}
