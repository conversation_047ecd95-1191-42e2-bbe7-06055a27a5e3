package dtos

import (
	"time"

	"github.com/Zomato/cdp-platform/internal/enum"
)

type ScheduleRangeRequest struct {
	Start int `json:"start"`
	End   int `json:"end"`
	Step  int `json:"step"`
}

type CalendarSpecRequest struct {
	Second     []ScheduleRangeRequest `json:"second"`
	Minute     []ScheduleRangeRequest `json:"minute"`
	Hour       []ScheduleRangeRequest `json:"hour"`
	DayOfMonth []ScheduleRangeRequest `json:"dayOfMonth"`
	Month      []ScheduleRangeRequest `json:"month"`
	DayOfWeek  []ScheduleRangeRequest `json:"dayOfWeek"`
	Year       []ScheduleRangeRequest `json:"year,omitempty"`
	Comment    string                 `json:"comment"`
}

type IntervalSpecRequest struct {
	Every  int `json:"every"`  // in minutes
	Offset int `json:"offset"` // in minutes
}

type ScheduleSpecRequest struct {
	Intervals       []IntervalSpecRequest `json:"intervals,omitempty"`
	Calendars       []CalendarSpecRequest `json:"calendars,omitempty"`
	CronExpressions []string              `json:"cronExpressions,omitempty"`
	Timezone        string                `json:"timezone"`
	Jitter          int                   `json:"jitter,omitempty"` // in minutes
	EndAt           *time.Time            `json:"endAt,omitempty"`
}

type ScheduleInput struct {
	WorkflowType enum.WorkflowType   `json:"workflowType"`
	ScheduleSpec ScheduleSpecRequest `json:"scheduleSpec,omitempty"`
	Args         []interface{}       `json:"args,omitempty"`
}

type TriggerWorkflowInput struct {
	WorkflowType enum.WorkflowType `json:"workflowType"`
	Args         []interface{}     `json:"args,omitempty"`
}
