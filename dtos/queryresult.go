package dtos

type MessageSchema struct {
	ColumnSchemas []string
	ColumnNames   []string
}

type AnyValue struct {
	Value interface{}
	Type  string
}

type Row struct {
	Columns []*AnyValue
}

type ResultTable struct {
	Rows             []*Row
	Schema           *MessageSchema
	RowCount         int32
	ColumnCount      int32
	ProcessingTimeMS int64
}

type Result struct {
	Schema *MessageSchema
	Row    *Row
}
