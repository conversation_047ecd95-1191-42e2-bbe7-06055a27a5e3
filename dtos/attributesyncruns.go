package dtos

import "time"

type AttributeSyncRuns struct {
	Base
	AttributeQueryID int64      `json:"attribute_query_id,omitempty" db:"attribute_query_id"`
	ExecutionStart   *time.Time `json:"execution_start,omitempty" db:"execution_start"`
	ExecutionEnd     *time.Time `json:"execution_end,omitempty" db:"execution_end"`
	Status           string     `json:"status,omitempty" db:"status"`
	SyncTableName    *string    `json:"sync_table_name,omitempty" db:"sync_table_name"`
	RowsAffected     *int64     `json:"rows_affected,omitempty" db:"rows_affected"`
	ErrorMessage     *string    `json:"error_message,omitempty" db:"error_message"`
}
