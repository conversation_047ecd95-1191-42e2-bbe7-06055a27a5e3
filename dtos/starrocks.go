package dtos

type TableColumn struct {
	ColumnName   string `json:"column_name"`
	DataType     string `json:"data_type"`
	IsNullable   string `json:"is_nullable"`
	Key          string `json:"key"`
	DefaultValue string `json:"default_value"`
	Extra        string `json:"extra"`
	Comment      string `json:"comment"`
}

type CreateMaterializedView struct {
	Query     string `json:"query"`
	TableName string `json:"table_name"`
}

type CreateIcebergTable struct {
	TableName string `json:"table_name"`
	Query     string `json:"query"`
}

type CreateHiveTable struct {
	TableName string `json:"table_name"`
	Query     string `json:"query"`
}

type LoadJobStatus struct {
	IsComplete   bool   `json:"is_complete"`
	Status       string `json:"status"`
	ErrorMessage string `json:"error_message,omitempty"`
	ScanRows     int64  `json:"scan_rows"`
}

type DatabaseBackupRequest struct {
	CronExpression string `json:"cron_expression,omitempty"`
	DatabaseName   string `json:"database_name" binding:"required"`
	BackupRepo     string `json:"backup_repo" binding:"required"`
}

type DatabaseBackupUpdateCronRequest struct {
	DatabaseName   string `json:"database_name" binding:"required"`
	BackupRepo     string `json:"backup_repo" binding:"required"`
	CronExpression string `json:"cron_expression" binding:"required"`
}

type CreateFeatureStoreLogTable struct {
	SourceIcebergTable string `json:"source_iceberg_table"`
	LogTableName       string `json:"log_table_name"`
	FeatureName        string `json:"feature_name"`
	PartitionDt        string `json:"partition_dt"`
	PartitionHr        string `json:"partition_hr"`
}
