package dtos

import "github.com/Zomato/cdp-platform/pkg/emr"

type CreateEMRClusterRequest struct {
	WorkflowName   string                    `json:"workflow_name"`
	OverrideConfig *EMRClusterConfigOverride `json:"override_config,omitempty"`
}

type EMRClusterConfigOverride struct {
	InstanceType  string `json:"instance_type,omitempty"`
	InstanceCount int32  `json:"instance_count,omitempty"`
	ReleaseLabel  string `json:"release_label,omitempty"`
}

type CreateEMRClusterResponse struct {
	ClusterID   string `json:"cluster_id"`
	ClusterName string `json:"cluster_name"`
}

type SubmitSparkJobRequest struct {
	WorkflowName    string            `json:"workflow_name"`
	ClusterID       string            `json:"cluster_id"`
	JobName         string            `json:"job_name"`
	JarURI          string            `json:"jar_uri"`
	MainClass       string            `json:"main_class,omitempty"`
	Args            []string          `json:"args,omitempty"`
	Properties      map[string]string `json:"properties,omitempty"`
	AdditionalJars  []string          `json:"additional_jars,omitempty"`
	ConfigOverrides map[string]string `json:"config_overrides,omitempty"`
}

type SubmitSparkJobResponse struct {
	StepID string `json:"step_id"`
}

type EMRClusterStatusRequest struct {
	ClusterID string `json:"cluster_id"`
}

type EMRClusterStatusResponse struct {
	ClusterID string        `json:"cluster_id"`
	Status    emr.EMRStatus `json:"status"`
}

type EMRStepStatusRequest struct {
	ClusterID string `json:"cluster_id"`
	StepID    string `json:"step_id"`
}

type EMRStepStatusResponse struct {
	ClusterID string        `json:"cluster_id"`
	StepID    string        `json:"step_id"`
	Status    emr.EMRStatus `json:"status"`
}

type EMRStatus string
