

INSERT INTO tenants (name) VALUES ('zomato');

INSERT INTO users (tenant_id, name, email, google_id, is_allowed, created_at, updated_at) 
VALUES (1, '<PERSON>', '<EMAIL>', 'google_id_example', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert initial configurations
INSERT INTO data_source_configs (data_source_type, config_fields) VALUES
('trino', '{"host": true, "port": true, "catalog": true, "schema": true, "username": true, "password": true, "ssl": false, "source": false}');

-- Dummy Spark data source config
INSERT INTO data_source_configs (data_source_type, config_fields) VALUES ('spark', '{"catalog": true, "schema": true}');

-- Insert a data source
INSERT INTO data_sources (tenant_id, name, config_map, data_source_config_id)
VALUES (
    1, -- assuming tenant_id 1 from your earlier insert
    'CDP Database',
    '{"host": "localhost", "port": "3306", "catalog": "cdp", "schema": "public", "username": "user", "password": "pass"}',
    1  -- assuming the data_source_config_id is 1
);

-- Dummy Spark data source (assume config_id=2, tenant_id=1)
INSERT INTO data_sources (tenant_id, name, config_map, data_source_config_id) VALUES (
    1,
    '[Preprod] Spark',
    '{"catalog": "iceberg_staging", "schema": "cdp"}',
    2
);

-- Insert attribute query
INSERT INTO attribute_queries (id, data_source_id, owner_id, cron_expression, query_text)
VALUES (
    1,
    1, -- data_source_id from above insert
    1, -- owner_id (using the John Doe user we created)
    '0 */12 * * *', -- Run twice daily
    'SELECT user_id, gaid, phone, email, phone_e164, dslo, last_city, name, created_at, amount FROM user_attribute_test'
);


-- Example inserts for the new structure
-- First, insert into attributes table
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category) VALUES 
    (1, 'User ID', 'Unique identifier for the user', 1, 'BASE', 'PROPERTY'),
    (2, 'Days Since Last Order', 'Number of days since the user''s last order', 1, 'BASE', 'METRIC'),
    (3, 'High Value Customer', 'Derived attribute for high value customers', 1, 'DERIVED', 'DIMENSION'),
    (4, 'Customer Segment', 'TGCG based customer segmentation', 1, 'DERIVED', 'METRIC'),
    (5, 'Mod 19 General', 'Mod 19', 1, 'DERIVED', 'METRIC'),
    (6, 'Mod 19 TGCG', 'Mod 19', 1, 'DERIVED', 'METRIC');

-- Insert base attributes
INSERT INTO base_attribute_info (attribute_id, query_column_name, data_type, attribute_query_id) VALUES 
    (1, 'user_id', 'bigint', 1),
    (2, 'platform', 'varchar', 1);

-- Insert derived attributes
INSERT INTO derived_attribute_info (attribute_id, expression, data_type, attribute_type) VALUES 
    (3, 'IF(order_amount > 1000 AND dslo < 30, ''high_value'', ''regular'')', 'boolean', 'GENERAL'),
    (4, 'CASE WHEN category IN (''grocery'', ''household'') THEN ''TGCG'' ELSE ''OTHER'' END', 'varchar', 'TGCG'),
    (5, 'MOD(CAST(user_id AS BIGINT), 19)', 'bigint', 'GENERAL'),
    (6, 'MOD(CAST(user_id AS BIGINT), 19)', 'bigint', 'TGCG');

-- Map derived attributes to their source attributes
INSERT INTO derived_attribute_mapping (derived_attribute_id, source_attribute_id) VALUES 
    (3, 1),  -- High Value Customer depends on User ID
    (3, 2),  -- High Value Customer depends on DSLO
    (4, 1);  -- Customer Segment depends on User ID

INSERT INTO attribute_sync_runs (attribute_query_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message)
VALUES
    (1, '2025-02-01 00:00:00+00', '2025-02-01 01:00:00+00', 'SUCCESSFUL', 'user_attribute_test_sync_table', 1000, NULL);

INSERT INTO attribute_sync_runs (attribute_query_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message)
VALUES
    (1, '2025-02-01 02:00:00+00', '2025-02-01 03:00:00+00', 'FAILED', 'user_attribute_test_sync_table', 0, 'Error during sync operation');

INSERT INTO attribute_sync_runs (attribute_query_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message)
VALUES
    (1, '2025-02-02 04:00:00+00', '2025-02-02 05:00:00+00', 'STARTED', 'user_attribute_sync_table_2', 500, NULL);

INSERT INTO attribute_sync_runs (attribute_query_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message)
VALUES
    (1, '2025-02-02 06:00:00+00', '2025-02-02 07:00:00+00', 'INTERM_TABLE_CREATED', 'user_attribute_sync_table_3', 300, NULL);


INSERT INTO segments (name, description, conditions, owner_id, created_at, updated_at) 
VALUES ('Sample Segment', 'This is a sample segment description.', '{"type": "simple", "attribute_id": 6, "operator": ">", "value": "10"}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO segments (name, description, conditions, owner_id, created_at, updated_at) 
VALUES ('Sample Segment 2', 'This is a sample segment description.', '{"type": "simple", "attribute_id": 6, "operator": ">", "value": "10"}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


INSERT INTO segment_attribute_mapping ( segment_id, attribute_id ) 
VALUES ( 1, 1 );


INSERT INTO destinations (name, destination_type, config, created_at, updated_at) 
VALUES ('Sample Destination', 'APPSFLYER', '{"key": "value"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO destination_syncs (id, segment_id, destination_id, commited_segment_table_name, ongoing_segment_table_name, sync_strategy, cron_expression, owner_id, created_at, updated_at) 
VALUES (1002, 1, 1, 'sample_commited_segment_table_name', 'sample_ongoing_segment_table_name', 'FULL', '0 * * * *', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


INSERT INTO "public"."appsflyer_audiences" ("id", "name", "platform", "import_key", "appsflyer_audience_id") VALUES (1, 'Testing Audience', 'android', 'SFAO8CZJUBORLHUOKRGSICDCSG2VJRDRTURN+VOOWK4=', 12);
INSERT INTO "public"."appsflyer_sync_mappings" ("id", "destination_sync_id", "audience_id") VALUES (1, 1002, 1);

-- Insert dummy destination sync runs
INSERT INTO destination_sync_runs (destination_sync_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message)
VALUES
    (1002, '2024-03-01 00:00:00+00', '2024-03-01 01:00:00+00', 'SUCCESSFUL', 'segment_sync_table_1', 1000, NULL),
    (1002, '2024-03-02 00:00:00+00', '2024-03-02 01:30:00+00', 'FAILED', 'segment_sync_table_2', 0, 'Connection timeout error'),
    (1002, '2024-03-03 00:00:00+00', NULL, 'STARTED', 'segment_sync_table_3', NULL, NULL),
    (1002, '2024-03-04 00:00:00+00', '2024-03-04 00:30:00+00', 'INTERM_TABLE_CREATED', 'segment_sync_table_4', 500, NULL),
    (1002, '2024-03-05 00:00:00+00', '2024-03-05 01:15:00+00', 'SUCCESSFUL', 'segment_sync_table_5', 750, NULL);

-- Insert corresponding partition status records
INSERT INTO destination_sync_partition_status (destination_sync_run_id, partition_number, status)
VALUES
    (1, 1, 'COMPLETED'),
    (1, 2, 'COMPLETED'),
    (2, 1, 'PENDING'),
    (3, 1, 'PENDING'),
    (4, 1, 'COMPLETED'),
    (4, 2, 'PENDING'),
    (5, 1, 'COMPLETED');


INSERT INTO destinations (
    name,
    destination_type,
    config,
    created_at,
    updated_at
) VALUES (
    'Feature Store',
    'FEATURE_STORE',
    '{"tenant": "consumer", "namespace": "features", "entity": "user"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

