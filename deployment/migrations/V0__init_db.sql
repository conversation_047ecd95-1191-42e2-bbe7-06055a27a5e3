------------------------------------------------------------ TENANT

-- Create tenants table
CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata')
);

------------------------------------------------------------ USERS 

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    google_id VARCHAR(255) UNIQUE,
    is_allowed BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

------------------------------------------------------------ DATA SOURCES 

-- Create data_source_configs table
CREATE TABLE data_source_configs (
    id SERIAL PRIMARY KEY,
    data_source_type VARCHAR(50) NOT NULL UNIQUE,
    config_fields JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata')
);

-- Create data_sources table (updated to reference data_source_configs)
CREATE TABLE data_sources (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL UNIQUE,
    config_map JSONB NOT NULL,
    data_source_config_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (data_source_config_id) REFERENCES data_source_configs(id)
);

------------------------------------------------------------ ATTRIBUTES 

-- Create attribute_queries table
CREATE TABLE attribute_queries (
    id SERIAL PRIMARY KEY,
    data_source_id INTEGER NOT NULL,
    owner_id INTEGER NOT NULL,
    cron_expression VARCHAR(100) NOT NULL,
    query_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (data_source_id) REFERENCES data_sources(id),
    FOREIGN KEY (owner_id) REFERENCES users(id)
);

CREATE TYPE attribute_type AS ENUM ('BASE', 'DERIVED');
CREATE TYPE attribute_category AS ENUM ('METRIC', 'DIMENSION', 'PROPERTY');

-- Create attributes table (base table with common fields)
CREATE TABLE attributes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    owner_id INTEGER NOT NULL,
    attribute_type attribute_type NOT NULL,
    attribute_category attribute_category NOT NULL DEFAULT 'PROPERTY',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Create base_attribute_info table
CREATE TABLE base_attribute_info (
    id SERIAL PRIMARY KEY,
    attribute_id INTEGER NOT NULL UNIQUE,
    query_column_name VARCHAR(255) NOT NULL,
    data_type VARCHAR(255) NOT NULL,
    attribute_query_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_query_id) REFERENCES attribute_queries(id)
);

-- Create derived attribute type enum
CREATE TYPE derived_attribute_type AS ENUM ('TGCG', 'GENERAL', 'ARITHMETIC', 'CONDITIONAL');

-- Create derived_attribute_info table
CREATE TABLE derived_attribute_info (
    id SERIAL PRIMARY KEY,
    attribute_id INTEGER NOT NULL UNIQUE,
    expression TEXT NOT NULL,
    data_type VARCHAR(255) NOT NULL,
    attribute_type derived_attribute_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE
);

-- Create derived attribute mapping table
CREATE TABLE derived_attribute_mapping (
    id SERIAL PRIMARY KEY,
    derived_attribute_id INTEGER NOT NULL,
    source_attribute_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (derived_attribute_id) REFERENCES derived_attribute_info(attribute_id) ON DELETE CASCADE,
    FOREIGN KEY (source_attribute_id) REFERENCES attributes(id) ON DELETE CASCADE
);

------------------------------------------------------------ ATTRIBUTES SYNC   

-- Create attribute_query_logs table
-- For attribute syncs
CREATE TYPE attribute_sync_status AS ENUM (
    'REGISTERED',
    'STARTED',
    'INTERM_TABLE_CREATED',
    'SUCCESSFUL',
    'FAILED'
);

CREATE TABLE attribute_sync_runs (
    id SERIAL PRIMARY KEY,
    attribute_query_id INTEGER NOT NULL,
    execution_start TIMESTAMP WITH TIME ZONE NOT NULL,
    execution_end TIMESTAMP WITH TIME ZONE,
    status attribute_sync_status NOT NULL,
    sync_table_name VARCHAR(255) DEFAULT NULL,
    rows_affected INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (attribute_query_id) REFERENCES attribute_queries(id)
);


------------------------------------------------------------ SEGMENT

-- Create segments table
CREATE TABLE segments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    conditions JSONB NOT NULL,
    owner_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (owner_id) REFERENCES users(id)
);


-- Create segment_attributes junction table
CREATE TABLE segment_attribute_mapping (
    segment_id INTEGER NOT NULL,
    attribute_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    PRIMARY KEY (segment_id, attribute_id),
    FOREIGN KEY (segment_id) REFERENCES segments(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE
);


------------------------------------------------------------ DESTINATIONS 
-- Destination types enum
CREATE TYPE destination_type AS ENUM ('APPSFLYER', 'PROFILE_STORE', 'FEATURE_STORE', 'CONSUMER_NOTIFICATIONS', 'CLEVERTAP');

-- Destinations table
CREATE TABLE destinations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    destination_type destination_type NOT NULL,
    config JSONB NOT NULL,  -- Stores destination-specific configuration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata')
);


------------------------------------------------------------ DESTINATIONS SYNCS 

-- SYNC_STRATEGY enum
CREATE TYPE sync_strategy AS ENUM ('FULL', 'INCREMENTAL');

-- Destination syncs table (without audience coupling)
CREATE TABLE destination_syncs (
    id SERIAL PRIMARY KEY,
    segment_id INTEGER NOT NULL,
    destination_id INTEGER NOT NULL,
    commited_segment_table_name VARCHAR(255),
    ongoing_segment_table_name VARCHAR(255),
    sync_strategy sync_strategy NOT NULL,
    cron_expression VARCHAR(100) NOT NULL,
    owner_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (segment_id) REFERENCES segments(id),
    FOREIGN KEY (destination_id) REFERENCES destinations(id),
    FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Create TG CG destination syncs table
CREATE TABLE tg_cg_destination_syncs (
    id SERIAL PRIMARY KEY,
    destination_sync_id INTEGER NOT NULL,
    conditions JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (destination_sync_id) REFERENCES destination_syncs(id) ON DELETE CASCADE,
    UNIQUE(destination_sync_id)  -- Ensures one-to-one relationship with destination_syncs
);

CREATE TYPE destination_sync_status AS ENUM (
    'REGISTERED',
    'STARTED',
    'INTERM_TABLE_CREATED',
    'DATA_PUSHED_FOR_CONSUMPTION',
    'SUCCESSFUL',
    'FAILED'
);

CREATE TABLE destination_sync_runs (
    id SERIAL PRIMARY KEY,
    destination_sync_id INTEGER NOT NULL,
    execution_start TIMESTAMP WITH TIME ZONE NOT NULL,
    execution_end TIMESTAMP WITH TIME ZONE,
    status destination_sync_status NOT NULL,
    sync_table_name VARCHAR(255) DEFAULT NULL,
    rows_affected INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (destination_sync_id) REFERENCES destination_syncs(id)
);

-- Appsflyer audiences table
CREATE TABLE appsflyer_audiences (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    platform VARCHAR(255) NOT NULL,
    import_key VARCHAR(255) NOT NULL UNIQUE,
    appsflyer_audience_id INTEGER NOT NULL,
    snapshot_table_name VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata')
);

-- Appsflyer sync mappings (junction table)
CREATE TABLE appsflyer_sync_mappings (
    id SERIAL PRIMARY KEY,
    destination_sync_id INTEGER NOT NULL,
    audience_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (destination_sync_id) REFERENCES destination_syncs(id),
    FOREIGN KEY (audience_id) REFERENCES appsflyer_audiences(id),
    UNIQUE(destination_sync_id, audience_id)
);

------------------------------------------------------------ DESTINATIONS DUMMY MSG  

CREATE TYPE partition_status AS ENUM ('PENDING', 'COMPLETED');

CREATE TABLE destination_sync_partition_status (
    id SERIAL PRIMARY KEY,
    destination_sync_run_id INTEGER NOT NULL,
    partition_number INTEGER NOT NULL,
    status partition_status DEFAULT 'PENDING', -- Default set to 'PENDING'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (destination_sync_run_id) REFERENCES destination_sync_runs(id),
    UNIQUE(destination_sync_run_id, partition_number)
);

------------------------------------------------------------ AUTO UPDATE TIMESTAMP

-- Create trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger to all tables with updated_at column
DO $$ 
DECLARE 
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'updated_at' 
        AND table_schema = 'public'
    LOOP
        EXECUTE format('CREATE TRIGGER update_updated_at_trigger
                       BEFORE UPDATE ON %I
                       FOR EACH ROW
                       EXECUTE FUNCTION update_updated_at_column()', t);
    END LOOP;
END $$;
