
------------------------------------------------------------ ENTITY SUPPORT 

-- Create entity type enum
CREATE TYPE entity_type AS ENUM ('BASE', 'DERIVED');

-- Create entities table
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    type entity_type NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    schema_name VARCHAR(255),
    catalog_name VARCHAR(255),
    requires_filter BOOLEAN NOT NULL DEFAULT FALSE, -- In order to handle filtering for entities in Segment Builder
    owner_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (owner_id) REFERENCES users(id)
);


-- Create derived entity mapping table (without join_condition)
CREATE TABLE derived_entity_mapping (
    id SERIAL PRIMARY KEY,
    entity_id INTEGER NOT NULL,
    parent_entity_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (entity_id) REFERENCES entities(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_entity_id) REFERENCES entities(id) ON DELETE CASCADE,
    UNIQUE(entity_id, parent_entity_id)
);


-- Create entity join table for centralized join conditions
CREATE TABLE entity_join_info (
    id SERIAL PRIMARY KEY,
    source_entity_id INTEGER NOT NULL,
    target_entity_id INTEGER NOT NULL,
    source_column VARCHAR(255) NOT NULL,
    target_column VARCHAR(255) NOT NULL,
    join_type VARCHAR(50) DEFAULT 'INNER',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (source_entity_id) REFERENCES entities(id) ON DELETE CASCADE,
    FOREIGN KEY (target_entity_id) REFERENCES entities(id) ON DELETE CASCADE,
    UNIQUE(source_entity_id, target_entity_id)
);

------------------------------------------------------------ CHANGES IN ATTRIBUTE QUERIES


-- Add entity_id to attribute_queries
ALTER TABLE attribute_queries 
ADD COLUMN entity_id INTEGER REFERENCES entities(id);

-- [INSERT] 
-- Base entities -> Need this otherwise below updates wont work
INSERT INTO entities (id, name, description, type, catalog_name, schema_name, table_name, requires_filter, owner_id) VALUES
(1, 'User', 'Base user entity', 'BASE', 'default_catalog', 'cdp', 'user_properties_test', FALSE, 1),
(2, 'User X Pid', 'User-product relationship entity', 'BASE', 'iceberg', 'cdp', 'user_pid_test', TRUE, 1);

-- [UPDATE]
-- Update existing queries with entity IDs (assuming they belong to the User entity)
UPDATE attribute_queries SET entity_id = 1 WHERE entity_id IS NULL;

------------------------------------------------------------ CHANGES IN DERIVED ATTRIBUTES

-- Update the derived attribute type enum to include FUNCTIONAL
ALTER TYPE derived_attribute_type ADD VALUE 'FUNCTIONAL' AFTER 'CONDITIONAL';


-- Modify derived_attribute_mapping table
ALTER TABLE derived_attribute_mapping
ADD COLUMN position INTEGER DEFAULT 0;

-- Modify derived_attribute_mapping table to add entity_id for easy lookup [Duplicate : can be derived by 3 joins]
ALTER TABLE derived_attribute_mapping
ADD COLUMN entity_id INTEGER DEFAULT 1;

-- Add readable_expression column to derived_attribute_info table [TO USE IT IN FRONTEND, OTHERWISE WILL HAVE TO BUILD EVERYTIME like API upstream-attributes]
ALTER TABLE derived_attribute_info
ADD COLUMN readable_expression TEXT;

COMMIT;

-- [MANUAL DELETE]
-- [DELETE AND INSERT EXISTING ACCORDING TO NEW STRUCTURE]
-- First, delete existing derived attribute mappings
DELETE FROM derived_attribute_mapping WHERE derived_attribute_id IN (3, 4, 5, 6);

-- Then delete the derived attribute info entries
DELETE FROM derived_attribute_info WHERE attribute_id IN (3, 4, 5, 6);

------------------------------------------------------------ CHANGES FOR ENTITY LEVEL FILTER SELECTION ON SELECTED ATTRIBUTES

-- Create attribute purpose enum to differentiate between filter and condition attributes
CREATE TYPE attribute_purpose AS ENUM ('FILTER', 'CONDITION');


-- Add attribute_purpose column to attributes table
ALTER TABLE attributes
ADD COLUMN attribute_purpose attribute_purpose NOT NULL DEFAULT 'CONDITION';

-- Added required filter in the entities table for this:
-- requires_filter BOOLEAN NOT NULL DEFAULT FALSE,

------------------------------------------------------------ FEATURE STORE DESTINATION TABLES

CREATE TABLE feature_store_segments (
    id SERIAL PRIMARY KEY,
    tenant TEXT NOT NULL,
    namespace TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    context_type TEXT NOT NULL,
    context_name TEXT NOT NULL,
    entity TEXT NOT NULL,
    feature_name TEXT NOT NULL,
    feature_type TEXT NOT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_pii BOOLEAN NOT NULL DEFAULT FALSE,
    destinations TEXT[],
    description TEXT,
    expiry TEXT,
    owner_id INTEGER NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE, -- STORE THIS IN IST
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (owner_id) REFERENCES users(id),
    UNIQUE(tenant, namespace, context_type, context_name, entity, feature_name)
);

CREATE TABLE feature_store_sync_mapping (
    id SERIAL PRIMARY KEY,
    feature_id INTEGER NOT NULL,
    destination_sync_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (feature_id) REFERENCES feature_store_segments(id) ON DELETE CASCADE,
    FOREIGN KEY (destination_sync_id) REFERENCES destination_syncs(id) ON DELETE CASCADE,
    UNIQUE(feature_id, destination_sync_id)
);

------------------------------------------------------------ PERSONAS TABLES 

CREATE TYPE feature_store_spark_job_status AS ENUM (
    'STARTED',
    'FAILED',
    'SUCCESSFUL'
);

CREATE TABLE feature_store_spark_runs (
    id SERIAL PRIMARY KEY,
    run_id VARCHAR(255) UNIQUE, 
    execution_start TIMESTAMP WITH TIME ZONE,
    execution_end TIMESTAMP WITH TIME ZONE,
    status feature_store_spark_job_status NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata')
);

CREATE TABLE feature_store_spark_destination_sync_run_mapping (
    id SERIAL PRIMARY KEY,
    feature_store_spark_run_id INTEGER NOT NULL,
    destination_sync_run_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'),
    FOREIGN KEY (feature_store_spark_run_id) REFERENCES feature_store_spark_runs(id) ON DELETE CASCADE,
    FOREIGN KEY (destination_sync_run_id) REFERENCES destination_sync_runs(id) ON DELETE CASCADE
);

