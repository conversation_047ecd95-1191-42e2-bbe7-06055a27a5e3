
------------------------------------------------------------ ENTITY SUPPORT 

-- -- Base entities
-- INSERT INTO entities (id, name, description, type, table_name, requires_filter, owner_id) VALUES
-- (1, 'User', 'Base user entity', 'BASE', 'user_properties', FALSE, 1),
-- (2, 'User X Pid', 'User-product relationship entity', 'BASE', 'user_pid_properties', TRUE, 1);

-- Derived entity
INSERT INTO entities (id, name, description, type, table_name, requires_filter, owner_id) VALUES
(3, 'User + User X Pid', 'Combined user and user-product view', 'DERIVED', 'user_plus_user_pid_view', TRUE, 1);


-- Map derived entity to its parents (without join conditions)
INSERT INTO derived_entity_mapping (entity_id, parent_entity_id) VALUES
(3, 1), -- User + User X Pid depends on User
(3, 2); -- User + User X Pid depends on User X Pid


-- Store join information for User and User X Pid entities
INSERT INTO entity_join_info (source_entity_id, target_entity_id, source_column, target_column, join_type) VALUES
(1, 2, 'user_id', 'user_id', 'INNER');


------------------------------------------------------------ CHANGES IN ATTRIBUTE QUERIES

-- Add User X Pid attribute query 
INSERT INTO attribute_queries (id, data_source_id, owner_id, cron_expression, query_text, entity_id)
VALUES (
    2,
    2, -- data_source_id from above insert
    1, -- owner_id (using the John Doe user we created)
    '0 */12 * * *', -- Run twice daily
    'SELECT customer_id, product_id, aov_1m, cart_count_1m, aov_3m, cart_count_3m FROM user_pid_properties',
    2
);

-- Adding BASE ATTRIBUTES 
-- Insert sample product attributes for User X Pid entity (matching the attribute query fields)
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category) VALUES
(7, 'Product ID', 'Product identifier', 1, 'BASE', 'PROPERTY'),
(8, 'Average Order Value 1M', 'Average order value in last month', 1, 'BASE', 'METRIC'),
(9, 'Cart Count 1M', 'Number of cart additions in last month', 1, 'BASE', 'METRIC'),
(10, 'Average Order Value 3M', 'Average order value in last 3 months', 1, 'BASE', 'METRIC'),
(11, 'Cart Count 3M', 'Number of cart additions in last 3 months', 1, 'BASE', 'METRIC');


-- Insert base attribute info for the product attributes (matching attribute query #2)
INSERT INTO base_attribute_info (attribute_id, query_column_name, data_type, attribute_query_id) VALUES
(7, 'product_id', 'varchar', 2),  -- Using attribute query ID 2 for User X Pid entity
(8, 'aov_1m', 'decimal', 2),
(9, 'cart_count_1m', 'integer', 2),
(10, 'aov_3m', 'decimal', 2),
(11, 'cart_count_3m', 'integer', 2);



------------------------------------------------------------ CHANGES FOR ENTITY LEVEL FILTER SELECTION ON SELECTED ATTRIBUTES

-- Insert filter attributes for categories (assuming these are related to User X Pid)
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category, attribute_purpose) 
VALUES 
    (16, 'L0 Category', 'Top level product category', 1, 'BASE', 'DIMENSION', 'FILTER'),
    (17, 'L1 Category', 'Second level product category', 1, 'BASE', 'DIMENSION', 'FILTER');

-- Add corresponding entries in base_attribute_info 
INSERT INTO base_attribute_info (attribute_id, query_column_name, data_type, attribute_query_id) 
VALUES 
    (16, 'l0_category', 'varchar', 2),  -- Using attribute query ID 2 for User X Pid entity
    (17, 'l1_category', 'varchar', 2);


------------------------------------------------------------ CHANGES IN DERIVED ATTRIBUTES


-- Now insert using the new parameterized structure
-- For High Value Customer (was ID 3)
INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    5, 3, 
    'IF({2} > 1000 AND {1} < 30, ''high_value'', ''regular'')', 
    'IF(Order Amount > 1000 AND Days Since Last Order < 30, "high_value", "regular")', 
    'boolean', 'GENERAL'
);

-- Map derived attributes to their source attributes with position and entity info
INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
    (3, 2, 1, 1),    -- Position 1: dslo from User entity
    (3, 1, 2, 1);    -- Position 2: order_amount (assuming this is user_id for demo)

-- For Customer Segment (was ID 4)
INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    6, 4, 
    'CASE WHEN {1} IN ({params}) THEN ''TGCG'' ELSE ''OTHER'' END', 
    'CASE WHEN Category IN (Selected Categories) THEN "TGCG" ELSE "OTHER" END', 
    'varchar', 'TGCG'
);

INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
    (4, 1, 1, 2);  -- Position 1: category (assuming this is user_id for demo)

-- For Mod 19 General (was ID 5)
INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    7, 5, 
    'MOD(CAST({1} AS BIGINT), 19)', 
    'MOD(User ID, 19)', 
    'bigint', 'GENERAL'
);

INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
    (5, 1, 1, 1);  -- Position 1: user_id from User entity

-- For Mod 19 TGCG (was ID 6) 
INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    8, 6, 
    'MOD(CAST({1} AS BIGINT), 19)', 
    'MOD(User ID, 19)', 
    'bigint', 'TGCG'
);

INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
    (6, 1, 1, 1);  -- Position 1: user_id from User entity



-- [NEW FUNCTIONAL]
-- Example 1: Cart count sum for specific PIDs
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category) VALUES
(12, 'Total Cart Count for PIDs', 'Sum of cart count for selected PIDs', 1, 'DERIVED', 'METRIC');


-- Create a functional derived attribute for "Total Cart Count for PIDs"
INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    1, 12, 
    'SUM(IF({1} IN ({params}), {2}, 0))', 
    'SUM(IF(Product ID IN (Selected Products), Cart Count 1M, 0))', 
    'integer', 'FUNCTIONAL'
);

-- Map to source attributes with position
INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
(12, 16, 1, 2),    -- Position 1: product_id attribute from User X Pid entity
(12, 9, 2, 2);     -- Position 2: cart_count_1m attribute from User X Pid entity


-- Example 2: Max cart count for any PID
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category) VALUES
(13, 'Max Cart Count for any PID', 'Maximum cart count for any selected PID', 1, 'DERIVED', 'METRIC');

INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    2, 13, 
    'MAX(IF({1} IN ({params}), {2}, 0))', 
    'MAX(IF(Product ID IN (Selected Products), Cart Count 1M, 0))', 
    'integer', 'FUNCTIONAL'
);

INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES 
(13, 7, 1, 2),
(13, 9, 2, 2);

-- Example 3: "Bought any in 3 months" functional attribute
INSERT INTO attributes (id, name, description, owner_id, attribute_type, attribute_category) VALUES
(14, 'Bought Any in 3 Months', 'Whether user bought any of the selected PIDs in 3 months', 1, 'DERIVED', 'DIMENSION');

INSERT INTO derived_attribute_info (
    id, attribute_id, expression, readable_expression, data_type, attribute_type
) VALUES (
    3, 14, 
    'CASE WHEN SUM(IF({1} IN ({params}) AND {2} > 0, 1, 0)) > 0 THEN TRUE ELSE FALSE END', 
    'CASE WHEN ANY(Product ID IN (Selected Products) AND Cart Count 3M > 0) THEN TRUE ELSE FALSE END', 
    'boolean', 'FUNCTIONAL'
);

INSERT INTO derived_attribute_mapping (
    derived_attribute_id, source_attribute_id, position, entity_id
) VALUES
(14, 7, 1, 2),
(14, 11, 2, 2);

--------------------------------------------------------------- PERSONAS TABLES 

INSERT INTO feature_store_spark_runs (id, run_id, execution_start, execution_end, status) VALUES
(1, 'run_1', NOW(), NOW(), 'STARTED'),
(2, 'run_2', NOW(), NOW(), 'FAILED'),
(3, 'run_3', NOW(), NOW(), 'SUCCESSFUL');

INSERT INTO feature_store_spark_destination_sync_run_mapping (feature_store_spark_run_id, destination_sync_run_id) VALUES
(1, 1),
(2, 2),
(3, 3);


INSERT INTO destination_syncs (
    id,
    segment_id,
    destination_id,
    sync_strategy,
    cron_expression,
    owner_id,
    created_at,
    updated_at
) VALUES (
    1003,
    1,                  
    2,                  
    'FULL',             
    '0 0 * * *',        
    1,                  
    CURRENT_TIMESTAMP,  
    CURRENT_TIMESTAMP   
);

INSERT INTO feature_store_segments (id, tenant, namespace, context_type, context_name, entity, feature_name, feature_type, is_verified, is_pii, destinations, description, expiry, owner_id, is_active, created_at, updated_at ) VALUES (
    1003, 
    'consumer',                     
    'features',                     
    'user',                         
    'audiences_cdp_1',                
    'user',                         
    'user_segment_feature_9117',    
    'boolean',                      
    false,                          
    false,                          
    ARRAY['feature_store'],         
    'Feature representing user segment membership', 
    '30d',                          
    1,                              
    true,                           
    CURRENT_TIMESTAMP,              
    CURRENT_TIMESTAMP               
);


INSERT INTO feature_store_sync_mapping (
    feature_id,
    destination_sync_id,
    created_at,
    updated_at
) VALUES (
    1003,        
    1003, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP   
);

INSERT INTO destination_sync_runs (id, destination_sync_id, execution_start, execution_end, status, sync_table_name, rows_affected, error_message, created_at, updated_at) VALUES 
(1003, 1003, '2025-01-27 06:00:00+00', '2025-01-27 06:30:00+00', 'SUCCESSFUL', 'api_test_sync_1', 1000, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1004, 1003, '2025-01-27 12:00:00+00', '2025-01-27 12:25:00+00', 'DATA_PUSHED_FOR_CONSUMPTION', 'api_test_sync_2', 1200, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1005, 1003, '2025-01-27 18:00:00+00', '2025-01-27 18:10:00+00', 'INTERM_TABLE_CREATED', 'api_test_sync_3', 800, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1006, 1003,'2025-01-27 23:00:00+00', NULL, 'STARTED', 'api_test_sync_4', NULL, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1007, 1003, '2025-01-28 01:00:00+00', '2025-01-28 01:05:00+00', 'INTERM_TABLE_CREATED', 'api_test_sync_5', 900, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


INSERT INTO feature_store_spark_destination_sync_run_mapping (feature_store_spark_run_id, destination_sync_run_id)
VALUES
  (1, 1003),
  (1, 1004),
  (1, 1005),
  (1, 1006),
  (1, 1007);

